/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */

export abstract class DomainEvent implements IDomainEvent {
  readonly name: IDomainEvent['name']
  readonly occuredAt: Date
  constructor(name: IDomainEvent['name']) {
    this.name = name
    this.occuredAt = new Date()
  }
}
