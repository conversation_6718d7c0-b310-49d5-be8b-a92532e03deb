@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}.harvester{display:flex;order:10 !important;cursor:pointer;-webkit-box-pack:start;justify-content:flex-start;-webkit-box-direction:normal;-webkit-box-orient:horizontal;flex-direction:row;margin-left:3em}.harvester svg{fill:currentColor}.harvester svg path:nth-child(1){d:path("M12,16l-5.7-5.7l1.4-1.4l3.3,3.3V2.6h2v9.6l3.3-3.3l1.4,1.4L12,16z M21,15l0,3.5c0,1.4-1.1,2.5-2.5,2.5h-13C4.1,21,3,19.9,3,18.5V15h2v3.5C5,18.8,5.2,19,5.5,19h13c0.3,0,0.5-0.2,0.5-0.5l0-3.5H21z")}.harvester svg path{transform-origin:center;transition-property:d;transition-timing-function:ease;transition-duration:500ms}.harvester.downloaded svg{fill:#f1b91a}.harvester.downloaded svg path:nth-child(1){d:path("M17.5,16.5h-11v-3h11V16.5z M17.5,9.3h-11v3h11V9.3z M17.5,5h-11v3h11V5z M19,15l0,3.5c0,0.3-0.2,0.5-0.5,0.5h-13C5.2,19,5,18.8,5,18.5V15H3v3.5C3,19.9,4.1,21,5.5,21h13c1.4,0,2.5-1.1,2.5-2.5l0-3.5H19z")}.harvester.downloading svg path{fill:#1da1f2;animation-name:spin;animation-duration:800ms;animation-iteration-count:infinite;animation-timing-function:linear}.harvester.downloading svg path:nth-child(1){d:path("M22.1,12c0.2,1.1,0.1,2.3-0.2,3.4c-0.3,1.1-0.8,2.2-1.4,3.2c-0.7,1-1.4,2-2.5,2.6c-1.1,0.6-2.3,0.8-3.5,0.6l0-0.2c0.9-0.8,1.7-1.4,2.5-2.1c0.7-0.7,1.6-1.1,2.3-1.9c0.7-0.7,1.3-1.6,1.7-2.6c0.5-0.9,0.8-2,0.9-3.1H22.1z")}.harvester.downloading svg path:nth-child(2){d:path("M6.9,20.7c-1-0.4-2-1.1-2.9-1.9c-0.8-0.8-1.6-1.8-2.1-2.8c-0.5-1.1-1.1-2.2-1-3.5c0.1-1.2,0.4-2.4,1.3-3.4l0.2,0.1c0.2,1.2,0.4,2.2,0.5,3.2c0.2,1,0.2,2,0.5,3c0.3,1,0.8,1.9,1.4,2.8c0.6,0.9,1.4,1.6,2.2,2.3L6.9,20.7z")}.harvester.downloading svg path:nth-child(3){d:path("M7,3.2C7.8,2.6,8.9,2,10,1.7c1.1-0.3,2.3-0.5,3.5-0.4C14.7,1.4,16,1.5,17,2.2c1,0.7,1.9,1.6,2.3,2.8l-0.1,0.1c-1.2-0.4-2.1-0.8-3-1.1c-0.9-0.3-1.8-0.9-2.8-1.1c-1-0.2-2-0.3-3.1-0.2C9.1,2.7,8.1,3,7.1,3.4L7,3.2z")}.harvester.error svg{fill:red !important}.harvester.success svg{fill:#00ba7c !important}.harvester.success path:nth-child(1){d:path("M17.5,16.5h-11v-3h11V16.5z M17.5,9.3h-11v3h11V9.3z M17.5,5h-11v3h11V5z M19,15l0,3.5c0,0.3-0.2,0.5-0.5,0.5h-13C5.2,19,5,18.8,5,18.5V15H3v3.5C3,19.9,4.1,21,5.5,21h13c1.4,0,2.5-1.1,2.5-2.5l0-3.5H19z")}.harvester.status{min-height:1.875rem;padding-bottom:0px;padding-top:0px}.harvester.stream{-webkit-box-flex:1;flex-grow:0;flex-basis:auto;margin-left:12px}.harvester:hover svg,.harvester:hover:active svg{fill:#f1b91a}.harvester:hover .photoBG{background:hsla(0,0%,100%,.1)}.harvester:hover .statusBG,.harvester:hover .streamBG{background:rgba(241,185,26,.1)}.harvester:hover:active .photoBG{background:hsla(0,0%,100%,.2)}.harvester:hover:active .statusBG,.harvester:hover:active .streamBG{background:rgba(241,185,26,.2)}.photoColor{color:#fff !important}.statusColor,.streamColor{color:#f1b91a !important}.deck-harvester{cursor:pointer}.deck-harvester svg{fill:currentColor}.deck-harvester svg path:nth-child(1){d:path("M12,16l-5.7-5.7l1.4-1.4l3.3,3.3V2.6h2v9.6l3.3-3.3l1.4,1.4L12,16z M21,15l0,3.5c0,1.4-1.1,2.5-2.5,2.5h-13C4.1,21,3,19.9,3,18.5V15h2v3.5C5,18.8,5.2,19,5.5,19h13c0.3,0,0.5-0.2,0.5-0.5l0-3.5H21z")}.deck-harvester svg path{transform-origin:center;transition-property:d;transition-timing-function:ease;transition-duration:500ms}.deck-harvester.downloaded svg{fill:#f1b91a}.deck-harvester.downloaded svg path:nth-child(1){d:path("M17.5,16.5h-11v-3h11V16.5z M17.5,9.3h-11v3h11V9.3z M17.5,5h-11v3h11V5z M19,15l0,3.5c0,0.3-0.2,0.5-0.5,0.5h-13C5.2,19,5,18.8,5,18.5V15H3v3.5C3,19.9,4.1,21,5.5,21h13c1.4,0,2.5-1.1,2.5-2.5l0-3.5H19z")}.deck-harvester.downloading svg path{fill:#1da1f2;animation-name:spin;animation-duration:800ms;animation-iteration-count:infinite;animation-timing-function:linear}.deck-harvester.downloading svg path:nth-child(1){d:path("M22.1,12c0.2,1.1,0.1,2.3-0.2,3.4c-0.3,1.1-0.8,2.2-1.4,3.2c-0.7,1-1.4,2-2.5,2.6c-1.1,0.6-2.3,0.8-3.5,0.6l0-0.2c0.9-0.8,1.7-1.4,2.5-2.1c0.7-0.7,1.6-1.1,2.3-1.9c0.7-0.7,1.3-1.6,1.7-2.6c0.5-0.9,0.8-2,0.9-3.1H22.1z")}.deck-harvester.downloading svg path:nth-child(2){d:path("M6.9,20.7c-1-0.4-2-1.1-2.9-1.9c-0.8-0.8-1.6-1.8-2.1-2.8c-0.5-1.1-1.1-2.2-1-3.5c0.1-1.2,0.4-2.4,1.3-3.4l0.2,0.1c0.2,1.2,0.4,2.2,0.5,3.2c0.2,1,0.2,2,0.5,3c0.3,1,0.8,1.9,1.4,2.8c0.6,0.9,1.4,1.6,2.2,2.3L6.9,20.7z")}.deck-harvester.downloading svg path:nth-child(3){d:path("M7,3.2C7.8,2.6,8.9,2,10,1.7c1.1-0.3,2.3-0.5,3.5-0.4C14.7,1.4,16,1.5,17,2.2c1,0.7,1.9,1.6,2.3,2.8l-0.1,0.1c-1.2-0.4-2.1-0.8-3-1.1c-0.9-0.3-1.8-0.9-2.8-1.1c-1-0.2-2-0.3-3.1-0.2C9.1,2.7,8.1,3,7.1,3.4L7,3.2z")}.deck-harvester.error svg{fill:red !important}.deck-harvester.success svg{fill:#00ba7c !important}.deck-harvester.success path:nth-child(1){d:path("M17.5,16.5h-11v-3h11V16.5z M17.5,9.3h-11v3h11V9.3z M17.5,5h-11v3h11V5z M19,15l0,3.5c0,0.3-0.2,0.5-0.5,0.5h-13C5.2,19,5,18.8,5,18.5V15H3v3.5C3,19.9,4.1,21,5.5,21h13c1.4,0,2.5-1.1,2.5-2.5l0-3.5H19z")}.dark .deck-harvester svg{fill:#8899a6}.dark .deck-harvester:hover svg{fill:#f1b91a}article:hover .deck-harvester svg{fill:#8899a6}article .deck-harvester svg{fill:#aab8c2}article .deck-harvester:hover svg{fill:#f1b91a}.tweet-detail-actions.deck-harvest-actions>li{width:20% !important}

/*# sourceMappingURL=main.css.map*/