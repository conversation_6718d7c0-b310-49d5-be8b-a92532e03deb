// BetterTweetDeck Download Index Patch
// This patch adds index numbering for multiple media files when downloading from tweets
// It modifies the filename format to include _1, _2, etc. for multiple media files

(function() {
    'use strict';

    // Store the original chrome.downloads.download function
    const originalDownload = chrome.downloads.download;
    
    // Track downloads per tweet to add index
    const tweetDownloadCounts = new Map();
    const recentDownloads = new Map();
    
    // Clear old entries periodically
    setInterval(() => {
        const now = Date.now();
        for (const [key, timestamp] of recentDownloads.entries()) {
            if (now - timestamp > 60000) { // Clear after 1 minute
                tweetDownloadCounts.delete(key);
                recentDownloads.delete(key);
            }
        }
    }, 30000);

    // Override chrome.downloads.download
    chrome.downloads.download = function(options, callback) {
        try {
            // Extract tweet ID from the URL or filename
            let tweetId = null;
            
            // Try to extract tweet ID from URL
            const urlMatch = options.url && options.url.match(/status\/(\d+)/);
            if (urlMatch) {
                tweetId = urlMatch[1];
            }
            
            // If no tweet ID from URL, try from filename
            if (!tweetId && options.filename) {
                const filenameMatch = options.filename.match(/(\d{15,20})/);
                if (filenameMatch) {
                    tweetId = filenameMatch[1];
                }
            }
            
            // If we have a tweet ID, track and modify filename
            if (tweetId && options.filename) {
                // Get or initialize count for this tweet
                const count = (tweetDownloadCounts.get(tweetId) || 0) + 1;
                tweetDownloadCounts.set(tweetId, count);
                recentDownloads.set(tweetId, Date.now());
                
                // Add index to filename if multiple files
                if (count > 1 || shouldAlwaysIndex(options.url)) {
                    const parts = options.filename.split('.');
                    const extension = parts.pop();
                    const nameWithoutExt = parts.join('.');
                    
                    // Add index before the extension
                    options.filename = `${nameWithoutExt}_${count}.${extension}`;
                }
            }
        } catch (error) {
            console.error('BetterTweetDeck Download Index Patch error:', error);
        }
        
        // Call the original function
        return originalDownload.call(this, options, callback);
    };
    
    // Helper function to determine if we should always add index
    // This checks if there might be multiple media files
    function shouldAlwaysIndex(url) {
        // If URL contains certain patterns that indicate it's part of a set
        return url && (
            url.includes('/photo/2') ||
            url.includes('/photo/3') ||
            url.includes('/photo/4') ||
            url.includes('/video/2') ||
            url.includes('/video/3') ||
            url.includes('/video/4')
        );
    }

    // Also patch any XMLHttpRequest or fetch that might be used for downloads
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url) {
        this._downloadUrl = url;
        return originalXHROpen.apply(this, arguments);
    };

    console.log('BetterTweetDeck Download Index Patch loaded');
})();