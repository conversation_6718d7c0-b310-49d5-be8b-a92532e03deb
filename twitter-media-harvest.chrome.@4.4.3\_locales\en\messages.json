{"app_appDesc": {"message": "Download videos and images from twitter or TweetDeck with only one click."}, "app_appName": {"message": "Media Harvest : X (twitter) Media Downloader"}, "1f924ad_5d5053f03f": {"message": "API Rate limit exceeded."}, "1f924ad_eae34ccbc4": {"message": "Download failed"}, "1f924ad_2a2196801a": {"message": "Media in {{account}}({{tweet-id}}) download failed."}, "1f924ad_802600d124": {"message": "Too many requests"}, "378c634_92665f39a5": {"message": "The filename is modified by other extensions, please check extensions' settings."}, "378c634_f56e2a81af": {"message": "WARNING: Filename is modified"}, "33917e2_fce77c34d3": {"message": "Ignore"}, "31564b1_3838980a1a": {"message": "Failed to parse tweet information"}, "31564b1_4a87d0c817": {"message": "Failed to parse tweet information. Please report bug to developer."}, "0b8d28d_a23ddae9e3": {"message": "Download Quota Warning"}, "0b8d28d_d3846e72a3": {"message": "Remaining quota: {{quota}}. Resets at {{time}}"}, "b36390a_942087cc2d": {"message": "Retry"}, "b36390a_dcc839a401": {"message": "View"}, "9ffd3d1_78342a0905": {"message": "Forbidden"}, "9ffd3d1_445e314917": {"message": "Please check your login session and your permission."}, "9ffd3d1_c8119ad044": {"message": "Please contact with developer."}, "9ffd3d1_c1fed0368e": {"message": "The tweet cannot be found"}, "9ffd3d1_3177e027e2": {"message": "The tweet might be deleted."}, "9ffd3d1_d089c8a9fc": {"message": "Unauthorized"}, "9ffd3d1_a0973b2764": {"message": "Unknown Error ({{code}})"}, "9ffd3d1_b71071945b": {"message": "Your login session might be expired, please refresh the session."}, "f7ea7d1_ead07c84ba": {"message": "Changelog"}, "f7ea7d1_b16446d433": {"message": "Official website"}, "f7ea7d1_ba445cff38": {"message": "Privacy policy"}, "f7ea7d1_c2821fad73": {"message": "Reoprt issues"}, "f7ea7d1_dd167905de": {"message": "Version"}, "7057380_4d559ca1d7": {"message": "Auto-reveal sensitive content"}, "7057380_dfe7dbf99d": {"message": "Download the thumbnail when the media is video."}, "7057380_dc46abcc54": {"message": "Download video thumbnail"}, "7057380_cda8fb1794": {"message": "Keyboard shortcut"}, "7057380_8e968c0d5d": {"message": "Use keyboard shortcut to trigger download."}, "7057380_db7f1a8def": {"message": "When the tweet was flagged as sensitive content, this feature can show the blured content automatically."}, "bb82949_f65a6e40cb": {"message": "Buy me a coffee"}, "bb82949_1607f9781b": {"message": "Rate it"}, "11b85b2_941e2161cc": {"message": "Do you like Media Harvest?"}, "11b85b2_7175517a37": {"message": "or"}, "5762e0b_90ff589169": {"message": "Ask where to save files."}, "5762e0b_96dbb75e3e": {"message": "Create sub-directory"}, "5762e0b_744d204052": {"message": "Create sub-directory under the default download directory. Sub-directory can be seperated with \"/\"."}, "5762e0b_739f24f82a": {"message": "Filename pattern"}, "5762e0b_d5756164e0": {"message": "Group Files"}, "5762e0b_601ddaa59b": {"message": "Group files by the selected attribute."}, "5762e0b_d962e2cb76": {"message": "ディレクトリ名に次の文字は使えません <>:”\\|?*"}, "5762e0b_47b94f14fb": {"message": "Invalid pattern. The pattern must include `Tweet ID` + `Serial` or `Hash`."}, "5762e0b_99ffa803e8": {"message": "Show the file chooser or not when download is triggered. Recommend to disable this option."}, "5762e0b_bc14606c3e": {"message": "You can choose what info to be included in the filename."}, "0e181a2_daee7606b3": {"message": "Reset"}, "0e181a2_1509f561f2": {"message": "Save"}, "21cbf45_7e1b0d5641": {"message": "Account"}, "21cbf45_919bb4cb21": {"message": "Account ID"}, "21cbf45_eb8b5f0d5c": {"message": "Download Date"}, "21cbf45_14556392de": {"message": "Download Datetime"}, "21cbf45_a9c384acd2": {"message": "Download Timestamp"}, "21cbf45_edc4d7f989": {"message": "Download_Datetime"}, "21cbf45_a91069147f": {"message": "Hash"}, "21cbf45_8ea0949377": {"message": "Serial"}, "21cbf45_1204734978": {"message": "Tweet Date"}, "21cbf45_64c42b529f": {"message": "Tweet Datetime"}, "21cbf45_47dcbc4388": {"message": "Tweet ID"}, "21cbf45_f9c7227037": {"message": "Tweet Timestamp"}, "21cbf45_842977b303": {"message": "Tweet_Datetime"}, "70c798e_488ce0534b": {"message": "Are you sure you want to import this history file?"}, "70c798e_3a41081f84": {"message": "Cannot access the selected file. Please grant permission to read the file and try again."}, "70c798e_3664895579": {"message": "Export"}, "70c798e_b3fb2c8616": {"message": "Failed to export file."}, "70c798e_75a86f3387": {"message": "Failed to import file."}, "70c798e_2cff9baabf": {"message": "Import"}, "70c798e_75e97739dc": {"message": "Invalid format."}, "70c798e_c08ac736a5": {"message": "Next page"}, "70c798e_1208ec01f2": {"message": "Previous page"}, "70c798e_0e91610117": {"message": "Refresh"}, "70c798e_5aa9c40532": {"message": "The history file has been imported successfully."}, "08eacdf_e3b89e9d33": {"message": "Username"}, "48da247_1aa4cb0bcc": {"message": "Image"}, "48da247_f340b6d6ed": {"message": "Mixed"}, "48da247_d534be829e": {"message": "Video"}, "7d3d183_a52ace420f": {"message": "All"}, "7d3d183_1aa4cb0bcc": {"message": "Image"}, "7d3d183_f340b6d6ed": {"message": "Mixed"}, "7d3d183_d534be829e": {"message": "Video"}, "864cf82_7c86c7390d": {"message": "Select media type"}, "aa5855a_2b0dcdd400": {"message": "actions"}, "aa5855a_2d37bcd2f2": {"message": "download time"}, "aa5855a_dfbca01ace": {"message": "post time"}, "aa5855a_80f61f9618": {"message": "thumbnail"}, "aa5855a_1303c06b0b": {"message": "type"}, "aa5855a_04f8996da7": {"message": "user"}, "15ddb8b_7aadbc5005": {"message": "Aria2-Explorer"}, "15ddb8b_98a3aa3aed": {"message": "Dispatch download to Aria2"}, "15ddb8b_5770acf312": {"message": "Filename Detector"}, "15ddb8b_2383361968": {"message": "The detector can notify user when the filename is modified by other extensions."}, "15ddb8b_0e24240744": {"message": "This integration is not compatible with {{platform}}"}, "15ddb8b_e80343e864": {"message": "Transfer the download to Aria2 via {{aria2-extension}}."}, "f36977d_4efca0d10c": {"message": "About"}, "f36977d_5697d03dae": {"message": "Features"}, "f36977d_c910d474dc": {"message": "General"}, "f36977d_0e76960093": {"message": "History"}, "f36977d_090512d93f": {"message": "Integrations"}, "358771f_b6ff6dc46b": {"message": "Auto-reveal NSFW"}, "358771f_304a3206a8": {"message": "Buy me a coffee!"}, "358771f_ead07c84ba": {"message": "Changelog"}, "358771f_1607f9781b": {"message": "Rate it"}, "358771f_8ac18b6ceb": {"message": "Report issues"}, "358771f_69f28754fe": {"message": "Video thumbnail"}}