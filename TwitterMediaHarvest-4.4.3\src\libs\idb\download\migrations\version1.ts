/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */
import type { IDBMirgration } from '#libs/idb/base'
import type { DownloadDBSchema } from '../schema'

const migrate: IDBMirgration<DownloadDBSchema> = database => {
  database.createObjectStore('record', { keyPath: 'id' })
}

export default migrate
