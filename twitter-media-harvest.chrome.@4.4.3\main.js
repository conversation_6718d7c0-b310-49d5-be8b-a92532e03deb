(()=>{var e={6749:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getApplicativeMonoid=function(e){var t=(0,n.getApplySemigroup)(e);return function(r){return{concat:t(r).concat,empty:e.of(r.empty)}}},t.getApplicativeComposition=function(e,t){var r=(0,o.getFunctorComposition)(e,t).map,i=(0,n.ap)(e,t);return{map:r,of:function(r){return e.of(t.of(r))},ap:function(e,t){return(0,s.pipe)(e,i(t))}}};var n=r(3871),s=r(3643),o=r(1124)},3871:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ap=function(e,t){return function(r){return function(n){return e.ap(e.map(n,(function(e){return function(r){return t.ap(e,r)}})),r)}}},t.apFirst=function(e){return function(t){return function(r){return e.ap(e.map(r,(function(e){return function(){return e}})),t)}}},t.apSecond=function(e){return function(t){return function(r){return e.ap(e.map(r,(function(){return function(e){return e}})),t)}}},t.apS=function(e){return function(t,r){return function(n){return e.ap(e.map(n,(function(e){return function(r){var n;return Object.assign({},e,((n={})[t]=r,n))}})),r)}}},t.getApplySemigroup=function(e){return function(t){return{concat:function(r,n){return e.ap(e.map(r,(function(e){return function(r){return t.concat(e,r)}})),n)}}}},t.sequenceT=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n=t.length,s=function(e){return a.has.call(c,e)||(c[e]=u(i.tuple,e-1,[])),c[e]}(n),o=e.map(t[0],s),l=1;l<n;l++)o=e.ap(o,t[l]);return o}},t.sequenceS=function(e){return function(t){for(var r=Object.keys(t),n=r.length,s=function(e){var t=e.length;switch(t){case 1:return function(t){var r;return(r={})[e[0]]=t,r};case 2:return function(t){return function(r){var n;return(n={})[e[0]]=t,n[e[1]]=r,n}};case 3:return function(t){return function(r){return function(n){var s;return(s={})[e[0]]=t,s[e[1]]=r,s[e[2]]=n,s}}};case 4:return function(t){return function(r){return function(n){return function(s){var o;return(o={})[e[0]]=t,o[e[1]]=r,o[e[2]]=n,o[e[3]]=s,o}}}};case 5:return function(t){return function(r){return function(n){return function(s){return function(o){var i;return(i={})[e[0]]=t,i[e[1]]=r,i[e[2]]=n,i[e[3]]=s,i[e[4]]=o,i}}}}};default:return u((function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];for(var s={},o=0;o<t;o++)s[e[o]]=r[o];return s}),t-1,[])}}(r),o=e.map(t[r[0]],s),i=1;i<n;i++)o=e.ap(o,t[r[i]]);return o}};var i=r(3643),a=o(r(174));function u(e,t,r){return function(n){for(var s=Array(r.length+1),o=0;o<r.length;o++)s[o]=r[o];return s[r.length]=n,0===t?e.apply(null,s):u(e,t-1,s)}}var c={1:function(e){return[e]},2:function(e){return function(t){return[e,t]}},3:function(e){return function(t){return function(r){return[e,t,r]}}},4:function(e){return function(t){return function(r){return function(n){return[e,t,r,n]}}}},5:function(e){return function(t){return function(r){return function(n){return function(s){return[e,t,r,n,s]}}}}}}},7910:(e,t)=>{"use strict";function r(e){return function(t,r){return e.chain(t,(function(t){return e.map(r(t),(function(){return t}))}))}}Object.defineProperty(t,"__esModule",{value:!0}),t.chainFirst=function(e){var t=r(e);return function(e){return function(r){return t(r,e)}}},t.tap=r,t.bind=function(e){return function(t,r){return function(n){return e.chain(n,(function(n){return e.map(r(n),(function(e){var r;return Object.assign({},n,((r={})[t]=e,r))}))}))}}}},5921:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.eqDate=t.eqNumber=t.eqString=t.eqBoolean=t.eq=t.strictEqual=t.getStructEq=t.getTupleEq=t.Contravariant=t.getMonoid=t.getSemigroup=t.eqStrict=t.URI=t.contramap=t.tuple=t.struct=t.fromEquals=void 0;var n=r(3643);t.fromEquals=function(e){return{equals:function(t,r){return t===r||e(t,r)}}},t.struct=function(e){return(0,t.fromEquals)((function(t,r){for(var n in e)if(!e[n].equals(t[n],r[n]))return!1;return!0}))},t.tuple=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(0,t.fromEquals)((function(t,r){return e.every((function(e,n){return e.equals(t[n],r[n])}))}))},t.contramap=function(e){return function(r){return(0,t.fromEquals)((function(t,n){return r.equals(e(t),e(n))}))}},t.URI="Eq",t.eqStrict={equals:function(e,t){return e===t}};var s={equals:function(){return!0}};t.getSemigroup=function(){return{concat:function(e,r){return(0,t.fromEquals)((function(t,n){return e.equals(t,n)&&r.equals(t,n)}))}}},t.getMonoid=function(){return{concat:(0,t.getSemigroup)().concat,empty:s}},t.Contravariant={URI:t.URI,contramap:function(e,r){return(0,n.pipe)(e,(0,t.contramap)(r))}},t.getTupleEq=t.tuple,t.getStructEq=t.struct,t.strictEqual=t.eqStrict.equals,t.eq=t.Contravariant,t.eqBoolean=t.eqStrict,t.eqString=t.eqStrict,t.eqNumber=t.eqStrict,t.eqDate={equals:function(e,t){return e.valueOf()===t.valueOf()}}},6006:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.fromOption=c,t.fromPredicate=function(e){return function(t,r){return function(n){return e.fromEither(t(n)?u.right(n):u.left(r(n)))}}},t.fromOptionK=l,t.chainOptionK=function(e,t){var r=l(e);return function(e){var n=r(e);return function(e){return function(r){return t.chain(r,n(e))}}}},t.fromEitherK=f,t.chainEitherK=function(e,t){var r=f(e);return function(e){return function(n){return t.chain(n,r(e))}}},t.chainFirstEitherK=function(e,t){var r=p(e,t);return function(e){return function(t){return r(t,e)}}},t.filterOrElse=function(e,t){return function(r,n){return function(s){return t.chain(s,(function(t){return e.fromEither(r(t)?u.right(t):u.left(n(t)))}))}}},t.tapEither=p;var i=r(7910),a=r(3643),u=o(r(174));function c(e){return function(t){return function(r){return e.fromEither(u.isNone(r)?u.left(t()):u.right(r.value))}}}function l(e){var t=c(e);return function(e){var r=t(e);return function(e){return(0,a.flow)(e,r)}}}function f(e){return function(t){return(0,a.flow)(t,e.fromEither)}}function p(e,t){var r=f(e),n=(0,i.tap)(t);return function(e,t){return n(e,r(t))}}},1124:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.map=s,t.flap=function(e){return function(t){return function(r){return e.map(r,(function(e){return e(t)}))}}},t.bindTo=function(e){return function(t){return function(r){return e.map(r,(function(e){var r;return(r={})[t]=e,r}))}}},t.let=function(e){return function(t,r){return function(n){return e.map(n,(function(e){var n;return Object.assign({},e,((n={})[t]=r(e),n))}))}}},t.getFunctorComposition=function(e,t){var r=s(e,t);return{map:function(e,t){return(0,n.pipe)(e,r(t))}}},t.as=o,t.asUnit=function(e){var t=o(e);return function(e){return t(e,void 0)}};var n=r(3643);function s(e,t){return function(r){return function(n){return e.map(n,(function(e){return t.map(e,r)}))}}}function o(e){return function(t,r){return e.map(t,(function(){return r}))}}},1066:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatAll=t.endo=t.filterSecond=t.filterFirst=t.reverse=void 0,t.reverse=function(e){return{concat:function(t,r){return e.concat(r,t)}}},t.filterFirst=function(e){return function(t){return{concat:function(r,n){return e(r)?t.concat(r,n):n}}}},t.filterSecond=function(e){return function(t){return{concat:function(r,n){return e(n)?t.concat(r,n):r}}}},t.endo=function(e){return function(t){return{concat:function(r,n){return t.concat(e(r),e(n))}}}},t.concatAll=function(e){return function(t){return function(r){return r.reduce((function(t,r){return e.concat(t,r)}),t)}}}},3702:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.throwError=t.Witherable=t.wilt=t.wither=t.Traversable=t.sequence=t.traverse=t.Filterable=t.partitionMap=t.partition=t.filterMap=t.filter=t.Compactable=t.separate=t.compact=t.Extend=t.extend=t.Alternative=t.guard=t.Zero=t.zero=t.Alt=t.alt=t.altW=t.orElse=t.Foldable=t.reduceRight=t.foldMap=t.reduce=t.Monad=t.Chain=t.flatMap=t.Applicative=t.Apply=t.ap=t.Pointed=t.of=t.asUnit=t.as=t.Functor=t.map=t.getMonoid=t.getOrd=t.getEq=t.getShow=t.URI=t.getRight=t.getLeft=t.some=t.none=void 0,t.getLastMonoid=t.getFirstMonoid=t.getApplyMonoid=t.getApplySemigroup=t.option=t.mapNullable=t.chainFirst=t.chain=t.sequenceArray=t.traverseArray=t.traverseArrayWithIndex=t.traverseReadonlyArrayWithIndex=t.traverseReadonlyNonEmptyArrayWithIndex=t.ApT=t.apS=t.bind=t.let=t.bindTo=t.Do=t.exists=t.toUndefined=t.toNullable=t.chainNullableK=t.fromNullableK=t.tryCatchK=t.tryCatch=t.fromNullable=t.chainFirstEitherK=t.chainEitherK=t.fromEitherK=t.duplicate=t.tapEither=t.tap=t.flatten=t.apSecond=t.apFirst=t.flap=t.getOrElse=t.getOrElseW=t.fold=t.match=t.foldW=t.matchW=t.isNone=t.isSome=t.FromEither=t.fromEither=t.MonadThrow=void 0,t.fromPredicate=function(e){return function(r){return e(r)?(0,t.some)(r):t.none}},t.elem=function e(r){return function(n,s){if(void 0===s){var o=e(r);return function(e){return o(n,e)}}return!(0,t.isNone)(s)&&r.equals(n,s.value)}},t.getRefinement=function(e){return function(r){return(0,t.isSome)(e(r))}};var i=r(6749),a=r(3871),u=o(r(7910)),c=r(6006),l=r(3643),f=r(1124),p=o(r(174)),m=r(9918),d=r(6598),h=r(4276),g=r(8084),y=r(9641);t.none=p.none,t.some=p.some,t.getLeft=function(e){return"Right"===e._tag?t.none:(0,t.some)(e.left)},t.getRight=function(e){return"Left"===e._tag?t.none:(0,t.some)(e.right)};var v=function(e,r){return(0,l.pipe)(e,(0,t.map)(r))},b=function(e,r){return(0,l.pipe)(e,(0,t.ap)(r))},_=function(e,r,n){return(0,l.pipe)(e,(0,t.reduce)(r,n))},w=function(e){var r=(0,t.foldMap)(e);return function(e,t){return(0,l.pipe)(e,r(t))}},x=function(e,r,n){return(0,l.pipe)(e,(0,t.reduceRight)(r,n))},A=function(e){var r=(0,t.traverse)(e);return function(e,t){return(0,l.pipe)(e,r(t))}},R=function(e,r){return(0,l.pipe)(e,(0,t.alt)(r))},$=function(e,r){return(0,l.pipe)(e,(0,t.filter)(r))},S=function(e,r){return(0,l.pipe)(e,(0,t.filterMap)(r))},j=function(e,r){return(0,l.pipe)(e,(0,t.extend)(r))},O=function(e,r){return(0,l.pipe)(e,(0,t.partition)(r))},k=function(e,r){return(0,l.pipe)(e,(0,t.partitionMap)(r))};t.URI="Option",t.getShow=function(e){return{show:function(r){return(0,t.isNone)(r)?"none":"some(".concat(e.show(r.value),")")}}},t.getEq=function(e){return{equals:function(r,n){return r===n||((0,t.isNone)(r)?(0,t.isNone)(n):!(0,t.isNone)(n)&&e.equals(r.value,n.value))}}},t.getOrd=function(e){return{equals:(0,t.getEq)(e).equals,compare:function(r,n){return r===n?0:(0,t.isSome)(r)?(0,t.isSome)(n)?e.compare(r.value,n.value):1:-1}}},t.getMonoid=function(e){return{concat:function(r,n){return(0,t.isNone)(r)?n:(0,t.isNone)(n)?r:(0,t.some)(e.concat(r.value,n.value))},empty:t.none}},t.map=function(e){return function(r){return(0,t.isNone)(r)?t.none:(0,t.some)(e(r.value))}},t.Functor={URI:t.URI,map:v},t.as=(0,l.dual)(2,(0,f.as)(t.Functor)),t.asUnit=(0,f.asUnit)(t.Functor),t.of=t.some,t.Pointed={URI:t.URI,of:t.of},t.ap=function(e){return function(r){return(0,t.isNone)(r)||(0,t.isNone)(e)?t.none:(0,t.some)(r.value(e.value))}},t.Apply={URI:t.URI,map:v,ap:b},t.Applicative={URI:t.URI,map:v,ap:b,of:t.of},t.flatMap=(0,l.dual)(2,(function(e,r){return(0,t.isNone)(e)?t.none:r(e.value)})),t.Chain={URI:t.URI,map:v,ap:b,chain:t.flatMap},t.Monad={URI:t.URI,map:v,ap:b,of:t.of,chain:t.flatMap},t.reduce=function(e,r){return function(n){return(0,t.isNone)(n)?e:r(e,n.value)}},t.foldMap=function(e){return function(r){return function(n){return(0,t.isNone)(n)?e.empty:r(n.value)}}},t.reduceRight=function(e,r){return function(n){return(0,t.isNone)(n)?e:r(n.value,e)}},t.Foldable={URI:t.URI,reduce:_,foldMap:w,reduceRight:x},t.orElse=(0,l.dual)(2,(function(e,r){return(0,t.isNone)(e)?r():e})),t.altW=t.orElse,t.alt=t.orElse,t.Alt={URI:t.URI,map:v,alt:R},t.zero=function(){return t.none},t.Zero={URI:t.URI,zero:t.zero},t.guard=(0,y.guard)(t.Zero,t.Pointed),t.Alternative={URI:t.URI,map:v,ap:b,of:t.of,alt:R,zero:t.zero},t.extend=function(e){return function(r){return(0,t.isNone)(r)?t.none:(0,t.some)(e(r))}},t.Extend={URI:t.URI,map:v,extend:j},t.compact=(0,t.flatMap)(l.identity);var E=(0,h.separated)(t.none,t.none);t.separate=function(e){return(0,t.isNone)(e)?E:(0,h.separated)((0,t.getLeft)(e.value),(0,t.getRight)(e.value))},t.Compactable={URI:t.URI,compact:t.compact,separate:t.separate},t.filter=function(e){return function(r){return(0,t.isNone)(r)?t.none:e(r.value)?r:t.none}},t.filterMap=function(e){return function(r){return(0,t.isNone)(r)?t.none:e(r.value)}},t.partition=function(e){return function(t){return(0,h.separated)($(t,(0,m.not)(e)),$(t,e))}},t.partitionMap=function(e){return(0,l.flow)((0,t.map)(e),t.separate)},t.Filterable={URI:t.URI,map:v,compact:t.compact,separate:t.separate,filter:$,filterMap:S,partition:O,partitionMap:k},t.traverse=function(e){return function(r){return function(n){return(0,t.isNone)(n)?e.of(t.none):e.map(r(n.value),t.some)}}},t.sequence=function(e){return function(r){return(0,t.isNone)(r)?e.of(t.none):e.map(r.value,t.some)}},t.Traversable={URI:t.URI,map:v,reduce:_,foldMap:w,reduceRight:x,traverse:A,sequence:t.sequence};var I=(0,g.witherDefault)(t.Traversable,t.Compactable),M=(0,g.wiltDefault)(t.Traversable,t.Compactable);t.wither=function(e){var t=I(e);return function(e){return function(r){return t(r,e)}}},t.wilt=function(e){var t=M(e);return function(e){return function(r){return t(r,e)}}},t.Witherable={URI:t.URI,map:v,reduce:_,foldMap:w,reduceRight:x,traverse:A,sequence:t.sequence,compact:t.compact,separate:t.separate,filter:$,filterMap:S,partition:O,partitionMap:k,wither:I,wilt:M},t.throwError=function(){return t.none},t.MonadThrow={URI:t.URI,map:v,ap:b,of:t.of,chain:t.flatMap,throwError:t.throwError},t.fromEither=t.getRight,t.FromEither={URI:t.URI,fromEither:t.fromEither},t.isSome=p.isSome,t.isNone=function(e){return"None"===e._tag},t.matchW=function(e,r){return function(n){return(0,t.isNone)(n)?e():r(n.value)}},t.foldW=t.matchW,t.match=t.matchW,t.fold=t.match,t.getOrElseW=function(e){return function(r){return(0,t.isNone)(r)?e():r.value}},t.getOrElse=t.getOrElseW,t.flap=(0,f.flap)(t.Functor),t.apFirst=(0,a.apFirst)(t.Apply),t.apSecond=(0,a.apSecond)(t.Apply),t.flatten=t.compact,t.tap=(0,l.dual)(2,u.tap(t.Chain)),t.tapEither=(0,l.dual)(2,(0,c.tapEither)(t.FromEither,t.Chain)),t.duplicate=(0,t.extend)(l.identity),t.fromEitherK=(0,c.fromEitherK)(t.FromEither),t.chainEitherK=(0,c.chainEitherK)(t.FromEither,t.Chain),t.chainFirstEitherK=t.tapEither,t.fromNullable=function(e){return null==e?t.none:(0,t.some)(e)},t.tryCatch=function(e){try{return(0,t.some)(e())}catch(e){return t.none}},t.tryCatchK=function(e){return function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return(0,t.tryCatch)((function(){return e.apply(void 0,r)}))}},t.fromNullableK=function(e){return(0,l.flow)(e,t.fromNullable)},t.chainNullableK=function(e){return function(r){return(0,t.isNone)(r)?t.none:(0,t.fromNullable)(e(r.value))}},t.toNullable=(0,t.match)(l.constNull,l.identity),t.toUndefined=(0,t.match)(l.constUndefined,l.identity),t.exists=function(e){return function(r){return!(0,t.isNone)(r)&&e(r.value)}},t.Do=(0,t.of)(p.emptyRecord),t.bindTo=(0,f.bindTo)(t.Functor);var T=(0,f.let)(t.Functor);t.let=T,t.bind=u.bind(t.Chain),t.apS=(0,a.apS)(t.Apply),t.ApT=(0,t.of)(p.emptyReadonlyArray),t.traverseReadonlyNonEmptyArrayWithIndex=function(e){return function(r){var n=e(0,p.head(r));if((0,t.isNone)(n))return t.none;for(var s=[n.value],o=1;o<r.length;o++){var i=e(o,r[o]);if((0,t.isNone)(i))return t.none;s.push(i.value)}return(0,t.some)(s)}},t.traverseReadonlyArrayWithIndex=function(e){var r=(0,t.traverseReadonlyNonEmptyArrayWithIndex)(e);return function(e){return p.isNonEmpty(e)?r(e):t.ApT}},t.traverseArrayWithIndex=t.traverseReadonlyArrayWithIndex,t.traverseArray=function(e){return(0,t.traverseReadonlyArrayWithIndex)((function(t,r){return e(r)}))},t.sequenceArray=(0,t.traverseArray)(l.identity),t.chain=t.flatMap,t.chainFirst=t.tap,t.mapNullable=t.chainNullableK,t.option={URI:t.URI,map:v,of:t.of,ap:b,chain:t.flatMap,reduce:_,foldMap:w,reduceRight:x,traverse:A,sequence:t.sequence,zero:t.zero,alt:R,extend:j,compact:t.compact,separate:t.separate,filter:$,filterMap:S,partition:O,partitionMap:k,wither:I,wilt:M,throwError:t.throwError},t.getApplySemigroup=(0,a.getApplySemigroup)(t.Apply),t.getApplyMonoid=(0,i.getApplicativeMonoid)(t.Applicative),t.getFirstMonoid=function(){return(0,t.getMonoid)((0,d.first)())},t.getLastMonoid=function(){return(0,t.getMonoid)((0,d.last)())}},788:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ordDate=t.ordNumber=t.ordString=t.ordBoolean=t.ord=t.getDualOrd=t.getTupleOrd=t.between=t.clamp=t.max=t.min=t.geq=t.leq=t.gt=t.lt=t.equals=t.trivial=t.Contravariant=t.getMonoid=t.getSemigroup=t.URI=t.contramap=t.reverse=t.tuple=t.fromCompare=t.equalsDefault=void 0;var n=r(5921),s=r(3643);t.equalsDefault=function(e){return function(t,r){return t===r||0===e(t,r)}},t.fromCompare=function(e){return{equals:(0,t.equalsDefault)(e),compare:function(t,r){return t===r?0:e(t,r)}}},t.tuple=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(0,t.fromCompare)((function(t,r){for(var n=0;n<e.length-1;n++){var s=e[n].compare(t[n],r[n]);if(0!==s)return s}return e[n].compare(t[n],r[n])}))},t.reverse=function(e){return(0,t.fromCompare)((function(t,r){return e.compare(r,t)}))},t.contramap=function(e){return function(r){return(0,t.fromCompare)((function(t,n){return r.compare(e(t),e(n))}))}},t.URI="Ord",t.getSemigroup=function(){return{concat:function(e,r){return(0,t.fromCompare)((function(t,n){var s=e.compare(t,n);return 0!==s?s:r.compare(t,n)}))}}},t.getMonoid=function(){return{concat:(0,t.getSemigroup)().concat,empty:(0,t.fromCompare)((function(){return 0}))}},t.Contravariant={URI:t.URI,contramap:function(e,r){return(0,s.pipe)(e,(0,t.contramap)(r))}},t.trivial={equals:s.constTrue,compare:(0,s.constant)(0)},t.equals=function(e){return function(t){return function(r){return r===t||0===e.compare(r,t)}}},t.lt=function(e){return function(t,r){return-1===e.compare(t,r)}},t.gt=function(e){return function(t,r){return 1===e.compare(t,r)}},t.leq=function(e){return function(t,r){return 1!==e.compare(t,r)}},t.geq=function(e){return function(t,r){return-1!==e.compare(t,r)}},t.min=function(e){return function(t,r){return t===r||e.compare(t,r)<1?t:r}},t.max=function(e){return function(t,r){return t===r||e.compare(t,r)>-1?t:r}},t.clamp=function(e){var r=(0,t.min)(e),n=(0,t.max)(e);return function(e,t){return function(s){return n(r(s,t),e)}}},t.between=function(e){var r=(0,t.lt)(e),n=(0,t.gt)(e);return function(e,t){return function(s){return!r(s,e)&&!n(s,t)}}},t.getTupleOrd=t.tuple,t.getDualOrd=t.reverse,t.ord=t.Contravariant;var o={equals:n.eqStrict.equals,compare:function(e,t){return e<t?-1:e>t?1:0}};t.ordBoolean=o,t.ordString=o,t.ordNumber=o,t.ordDate=(0,s.pipe)(t.ordNumber,(0,t.contramap)((function(e){return e.valueOf()})))},9918:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.and=t.or=t.not=t.Contravariant=t.getMonoidAll=t.getSemigroupAll=t.getMonoidAny=t.getSemigroupAny=t.URI=t.contramap=void 0;var n=r(3643);t.contramap=function(e){return function(t){return(0,n.flow)(e,t)}},t.URI="Predicate",t.getSemigroupAny=function(){return{concat:function(e,r){return(0,n.pipe)(e,(0,t.or)(r))}}},t.getMonoidAny=function(){return{concat:(0,t.getSemigroupAny)().concat,empty:n.constFalse}},t.getSemigroupAll=function(){return{concat:function(e,r){return(0,n.pipe)(e,(0,t.and)(r))}}},t.getMonoidAll=function(){return{concat:(0,t.getSemigroupAll)().concat,empty:n.constTrue}},t.Contravariant={URI:t.URI,contramap:function(e,r){return(0,n.pipe)(e,(0,t.contramap)(r))}},t.not=function(e){return function(t){return!e(t)}},t.or=function(e){return function(t){return function(r){return t(r)||e(r)}}},t.and=function(e){return function(t){return function(r){return t(r)&&e(r)}}}},8524:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,s=0,o=t.length;s<o;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.traverse=t.reduceRightWithIndex=t.foldMapWithIndex=t.reduceWithIndex=t.reduceRight=t.foldMap=t.reduce=t.mapWithIndex=t.map=t.flatten=t.duplicate=t.extend=t.flatMap=t.ap=t.alt=t.altW=t.of=t.chunksOf=t.splitAt=t.chop=t.chainWithIndex=t.intersperse=t.prependAll=t.unzip=t.zipWith=t.modifyAt=t.updateAt=t.sort=t.groupBy=t.reverse=t.fromArray=t.unappend=t.unprepend=t.range=t.replicate=t.makeBy=t.fromReadonlyArray=t.rotate=t.union=t.sortBy=t.uniq=t.unsafeUpdateAt=t.unsafeInsertAt=t.append=t.appendW=t.prepend=t.prependW=t.isOutOfBound=t.isNonEmpty=t.empty=void 0,t.insertAt=t.snoc=t.unsnoc=t.uncons=t.filterWithIndex=t.chain=t.intercalate=t.updateLast=t.modifyLast=t.updateHead=t.modifyHead=t.matchRight=t.matchLeft=t.concatAll=t.max=t.min=t.init=t.last=t.tail=t.head=t.apS=t.bind=t.let=t.bindTo=t.Do=t.Comonad=t.Alt=t.TraversableWithIndex=t.Traversable=t.FoldableWithIndex=t.Foldable=t.Monad=t.chainFirst=t.Chain=t.Applicative=t.apSecond=t.apFirst=t.Apply=t.FunctorWithIndex=t.Pointed=t.flap=t.Functor=t.getUnionSemigroup=t.getEq=t.getSemigroup=t.getShow=t.URI=t.extract=t.traverseWithIndex=t.sequence=void 0,t.readonlyNonEmptyArray=t.fold=t.prependToAll=void 0,t.concatW=h,t.concat=g,t.group=y,t.zip=function e(r,n){return void 0===n?function(t){return e(t,r)}:(0,t.zipWith)(r,n,(function(e,t){return[e,t]}))},t.groupSort=function(e){var r=(0,t.sort)(e),n=y(e);return function(e){return(0,t.isNonEmpty)(e)?n(r(e)):t.empty}},t.filter=function(e){return(0,t.filterWithIndex)((function(t,r){return e(r)}))},t.cons=function(e,r){return void 0===r?(0,t.prepend)(e):(0,l.pipe)(r,(0,t.prepend)(e))};var a=r(3871),u=r(7910),c=r(5921),l=r(3643),f=r(1124),p=o(r(174)),m=r(788),d=o(r(6598));function h(e){return function(t){return t.concat(e)}}function g(e,t){return t?e.concat(t):function(t){return t.concat(e)}}function y(e){return function(r){var n=r.length;if(0===n)return t.empty;for(var s=[],o=r[0],i=[o],a=1;a<n;a++){var u=r[a];e.equals(u,o)?i.push(u):(s.push(i),i=[o=u])}return s.push(i),s}}t.empty=p.emptyReadonlyArray,t.isNonEmpty=p.isNonEmpty,t.isOutOfBound=function(e,t){return e<0||e>=t.length},t.prependW=function(e){return function(t){return i([e],t,!0)}},t.prepend=t.prependW,t.appendW=function(e){return function(t){return i(i([],t,!0),[e],!1)}},t.append=t.appendW,t.unsafeInsertAt=function(e,r,n){if((0,t.isNonEmpty)(n)){var s=p.fromReadonlyNonEmptyArray(n);return s.splice(e,0,r),s}return[r]},t.unsafeUpdateAt=function(e,t,r){if(r[e]===t)return r;var n=p.fromReadonlyNonEmptyArray(r);return n[e]=t,n},t.uniq=function(e){return function(r){if(1===r.length)return r;for(var n=[(0,t.head)(r)],s=function(t){n.every((function(r){return!e.equals(r,t)}))&&n.push(t)},o=0,i=(0,t.tail)(r);o<i.length;o++)s(i[o]);return n}},t.sortBy=function(e){if((0,t.isNonEmpty)(e)){var r=(0,m.getMonoid)();return(0,t.sort)(e.reduce(r.concat,r.empty))}return l.identity},t.union=function(e){var r=(0,t.uniq)(e);return function(e){return function(t){return r((0,l.pipe)(t,g(e)))}}},t.rotate=function(e){return function(r){var n=r.length,s=Math.round(e)%n;if((0,t.isOutOfBound)(Math.abs(s),r)||0===s)return r;if(s<0){var o=(0,t.splitAt)(-s)(r),i=o[0],a=o[1];return(0,l.pipe)(a,g(i))}return(0,t.rotate)(s-n)(r)}},t.fromReadonlyArray=function(e){return(0,t.isNonEmpty)(e)?p.some(e):p.none},t.makeBy=function(e){return function(t){for(var r=Math.max(0,Math.floor(t)),n=[e(0)],s=1;s<r;s++)n.push(e(s));return n}},t.replicate=function(e){return(0,t.makeBy)((function(){return e}))},t.range=function(e,r){return e<=r?(0,t.makeBy)((function(t){return e+t}))(r-e+1):[e]},t.unprepend=function(e){return[(0,t.head)(e),(0,t.tail)(e)]},t.unappend=function(e){return[(0,t.init)(e),(0,t.last)(e)]},t.fromArray=function(e){return(0,t.fromReadonlyArray)(e.slice())},t.reverse=function(e){return 1===e.length?e:i([(0,t.last)(e)],e.slice(0,-1).reverse(),!0)},t.groupBy=function(e){return function(t){for(var r={},n=0,s=t;n<s.length;n++){var o=s[n],i=e(o);p.has.call(r,i)?r[i].push(o):r[i]=[o]}return r}},t.sort=function(e){return function(t){return 1===t.length?t:t.slice().sort(e.compare)}},t.updateAt=function(e,r){return(0,t.modifyAt)(e,(function(){return r}))},t.modifyAt=function(e,r){return function(n){return(0,t.isOutOfBound)(e,n)?p.none:p.some((0,t.unsafeUpdateAt)(e,r(n[e]),n))}},t.zipWith=function(e,t,r){for(var n=[r(e[0],t[0])],s=Math.min(e.length,t.length),o=1;o<s;o++)n[o]=r(e[o],t[o]);return n},t.unzip=function(e){for(var t=[e[0][0]],r=[e[0][1]],n=1;n<e.length;n++)t[n]=e[n][0],r[n]=e[n][1];return[t,r]},t.prependAll=function(e){return function(t){for(var r=[e,t[0]],n=1;n<t.length;n++)r.push(e,t[n]);return r}},t.intersperse=function(e){return function(r){var n=(0,t.tail)(r);return(0,t.isNonEmpty)(n)?(0,l.pipe)(n,(0,t.prependAll)(e),(0,t.prepend)((0,t.head)(r))):r}},t.chainWithIndex=function(e){return function(r){for(var n=p.fromReadonlyNonEmptyArray(e(0,(0,t.head)(r))),s=1;s<r.length;s++)for(var o=e(s,r[s]),i=0;i<o.length;i++)n.push(o[i]);return n}},t.chop=function(e){return function(r){for(var n=e(r),s=[n[0]],o=n[1];(0,t.isNonEmpty)(o);){var i=e(o),a=i[0],u=i[1];s.push(a),o=u}return s}},t.splitAt=function(e){return function(r){var n=Math.max(1,e);return n>=r.length?[r,t.empty]:[(0,l.pipe)(r.slice(1,n),(0,t.prepend)((0,t.head)(r))),r.slice(n)]}},t.chunksOf=function(e){return(0,t.chop)((0,t.splitAt)(e))};var v=function(e,r){return(0,l.pipe)(e,(0,t.map)(r))},b=function(e,r){return(0,l.pipe)(e,(0,t.mapWithIndex)(r))},_=function(e,r){return(0,l.pipe)(e,(0,t.ap)(r))},w=function(e,r){return(0,l.pipe)(e,(0,t.extend)(r))},x=function(e,r,n){return(0,l.pipe)(e,(0,t.reduce)(r,n))},A=function(e){var r=(0,t.foldMap)(e);return function(e,t){return(0,l.pipe)(e,r(t))}},R=function(e,r,n){return(0,l.pipe)(e,(0,t.reduceRight)(r,n))},$=function(e){var r=(0,t.traverse)(e);return function(e,t){return(0,l.pipe)(e,r(t))}},S=function(e,r){return(0,l.pipe)(e,(0,t.alt)(r))},j=function(e,r,n){return(0,l.pipe)(e,(0,t.reduceWithIndex)(r,n))},O=function(e){var r=(0,t.foldMapWithIndex)(e);return function(e,t){return(0,l.pipe)(e,r(t))}},k=function(e,r,n){return(0,l.pipe)(e,(0,t.reduceRightWithIndex)(r,n))},E=function(e){var r=(0,t.traverseWithIndex)(e);return function(e,t){return(0,l.pipe)(e,r(t))}};t.of=p.singleton,t.altW=function(e){return function(t){return(0,l.pipe)(t,h(e()))}},t.alt=t.altW,t.ap=function(e){return(0,t.flatMap)((function(r){return(0,l.pipe)(e,(0,t.map)(r))}))},t.flatMap=(0,l.dual)(2,(function(e,r){return(0,l.pipe)(e,(0,t.chainWithIndex)((function(e,t){return r(t,e)})))})),t.extend=function(e){return function(r){for(var n=(0,t.tail)(r),s=[e(r)];(0,t.isNonEmpty)(n);)s.push(e(n)),n=(0,t.tail)(n);return s}},t.duplicate=(0,t.extend)(l.identity),t.flatten=(0,t.flatMap)(l.identity),t.map=function(e){return(0,t.mapWithIndex)((function(t,r){return e(r)}))},t.mapWithIndex=function(e){return function(r){for(var n=[e(0,(0,t.head)(r))],s=1;s<r.length;s++)n.push(e(s,r[s]));return n}},t.reduce=function(e,r){return(0,t.reduceWithIndex)(e,(function(e,t,n){return r(t,n)}))},t.foldMap=function(e){return function(t){return function(r){return r.slice(1).reduce((function(r,n){return e.concat(r,t(n))}),t(r[0]))}}},t.reduceRight=function(e,r){return(0,t.reduceRightWithIndex)(e,(function(e,t,n){return r(t,n)}))},t.reduceWithIndex=function(e,t){return function(r){return r.reduce((function(e,r,n){return t(n,e,r)}),e)}},t.foldMapWithIndex=function(e){return function(t){return function(r){return r.slice(1).reduce((function(r,n,s){return e.concat(r,t(s+1,n))}),t(0,r[0]))}}},t.reduceRightWithIndex=function(e,t){return function(r){return r.reduceRight((function(e,r,n){return t(n,r,e)}),e)}},t.traverse=function(e){var r=(0,t.traverseWithIndex)(e);return function(e){return r((function(t,r){return e(r)}))}},t.sequence=function(e){return(0,t.traverseWithIndex)(e)(l.SK)},t.traverseWithIndex=function(e){return function(r){return function(n){for(var s=e.map(r(0,(0,t.head)(n)),t.of),o=1;o<n.length;o++)s=e.ap(e.map(s,(function(e){return function(r){return(0,l.pipe)(e,(0,t.append)(r))}})),r(o,n[o]));return s}}},t.extract=p.head,t.URI="ReadonlyNonEmptyArray",t.getShow=function(e){return{show:function(t){return"[".concat(t.map(e.show).join(", "),"]")}}},t.getSemigroup=function(){return{concat:g}},t.getEq=function(e){return(0,c.fromEquals)((function(t,r){return t.length===r.length&&t.every((function(t,n){return e.equals(t,r[n])}))}))},t.getUnionSemigroup=function(e){var r=(0,t.union)(e);return{concat:function(e,t){return r(t)(e)}}},t.Functor={URI:t.URI,map:v},t.flap=(0,f.flap)(t.Functor),t.Pointed={URI:t.URI,of:t.of},t.FunctorWithIndex={URI:t.URI,map:v,mapWithIndex:b},t.Apply={URI:t.URI,map:v,ap:_},t.apFirst=(0,a.apFirst)(t.Apply),t.apSecond=(0,a.apSecond)(t.Apply),t.Applicative={URI:t.URI,map:v,ap:_,of:t.of},t.Chain={URI:t.URI,map:v,ap:_,chain:t.flatMap},t.chainFirst=(0,u.chainFirst)(t.Chain),t.Monad={URI:t.URI,map:v,ap:_,of:t.of,chain:t.flatMap},t.Foldable={URI:t.URI,reduce:x,foldMap:A,reduceRight:R},t.FoldableWithIndex={URI:t.URI,reduce:x,foldMap:A,reduceRight:R,reduceWithIndex:j,foldMapWithIndex:O,reduceRightWithIndex:k},t.Traversable={URI:t.URI,map:v,reduce:x,foldMap:A,reduceRight:R,traverse:$,sequence:t.sequence},t.TraversableWithIndex={URI:t.URI,map:v,mapWithIndex:b,reduce:x,foldMap:A,reduceRight:R,traverse:$,sequence:t.sequence,reduceWithIndex:j,foldMapWithIndex:O,reduceRightWithIndex:k,traverseWithIndex:E},t.Alt={URI:t.URI,map:v,alt:S},t.Comonad={URI:t.URI,map:v,extend:w,extract:t.extract},t.Do=(0,t.of)(p.emptyRecord),t.bindTo=(0,f.bindTo)(t.Functor);var I=(0,f.let)(t.Functor);t.let=I,t.bind=(0,u.bind)(t.Chain),t.apS=(0,a.apS)(t.Apply),t.head=t.extract,t.tail=p.tail,t.last=function(e){return e[e.length-1]},t.init=function(e){return e.slice(0,-1)},t.min=function(e){var t=d.min(e);return function(e){return e.reduce(t.concat)}},t.max=function(e){var t=d.max(e);return function(e){return e.reduce(t.concat)}},t.concatAll=function(e){return function(t){return t.reduce(e.concat)}},t.matchLeft=function(e){return function(r){return e((0,t.head)(r),(0,t.tail)(r))}},t.matchRight=function(e){return function(r){return e((0,t.init)(r),(0,t.last)(r))}},t.modifyHead=function(e){return function(r){return i([e((0,t.head)(r))],(0,t.tail)(r),!0)}},t.updateHead=function(e){return(0,t.modifyHead)((function(){return e}))},t.modifyLast=function(e){return function(r){return(0,l.pipe)((0,t.init)(r),(0,t.append)(e((0,t.last)(r))))}},t.updateLast=function(e){return(0,t.modifyLast)((function(){return e}))},t.intercalate=function(e){var r=(0,t.concatAll)(e);return function(e){return(0,l.flow)((0,t.intersperse)(e),r)}},t.chain=t.flatMap,t.filterWithIndex=function(e){return function(r){return(0,t.fromReadonlyArray)(r.filter((function(t,r){return e(r,t)})))}},t.uncons=t.unprepend,t.unsnoc=t.unappend,t.snoc=function(e,t){return(0,l.pipe)(e,g([t]))},t.insertAt=function(e,r){return function(n){return e<0||e>n.length?p.none:p.some((0,t.unsafeInsertAt)(e,r,n))}},t.prependToAll=t.prependAll,t.fold=t.concatAll,t.readonlyNonEmptyArray={URI:t.URI,of:t.of,map:v,mapWithIndex:b,ap:_,chain:t.flatMap,extend:w,extract:t.extract,reduce:x,foldMap:A,reduceRight:R,traverse:$,sequence:t.sequence,reduceWithIndex:j,foldMapWithIndex:O,reduceRightWithIndex:k,traverseWithIndex:E,alt:S}},6598:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.semigroupProduct=t.semigroupSum=t.semigroupString=t.getFunctionSemigroup=t.semigroupAny=t.semigroupAll=t.getIntercalateSemigroup=t.getMeetSemigroup=t.getJoinSemigroup=t.getDualSemigroup=t.getStructSemigroup=t.getTupleSemigroup=t.getFirstSemigroup=t.getLastSemigroup=t.getObjectSemigroup=t.semigroupVoid=t.concatAll=t.last=t.first=t.intercalate=t.tuple=t.struct=t.reverse=t.constant=t.max=t.min=void 0,t.fold=function(e){var r=(0,t.concatAll)(e);return function(e,t){return void 0===t?r(e):r(e)(t)}};var i=r(3643),a=o(r(174)),u=o(r(1066)),c=o(r(788));t.min=function(e){return{concat:c.min(e)}},t.max=function(e){return{concat:c.max(e)}},t.constant=function(e){return{concat:function(){return e}}},t.reverse=u.reverse,t.struct=function(e){return{concat:function(t,r){var n={};for(var s in e)a.has.call(e,s)&&(n[s]=e[s].concat(t[s],r[s]));return n}}},t.tuple=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return{concat:function(t,r){return e.map((function(e,n){return e.concat(t[n],r[n])}))}}},t.intercalate=function(e){return function(t){return{concat:function(r,n){return t.concat(r,t.concat(e,n))}}}},t.first=function(){return{concat:i.identity}},t.last=function(){return{concat:function(e,t){return t}}},t.concatAll=u.concatAll,t.semigroupVoid=(0,t.constant)(void 0),t.getObjectSemigroup=function(){return{concat:function(e,t){return Object.assign({},e,t)}}},t.getLastSemigroup=t.last,t.getFirstSemigroup=t.first,t.getTupleSemigroup=t.tuple,t.getStructSemigroup=t.struct,t.getDualSemigroup=t.reverse,t.getJoinSemigroup=t.max,t.getMeetSemigroup=t.min,t.getIntercalateSemigroup=t.intercalate,t.semigroupAll={concat:function(e,t){return e&&t}},t.semigroupAny={concat:function(e,t){return e||t}},t.getFunctionSemigroup=i.getSemigroup,t.semigroupString={concat:function(e,t){return e+t}},t.semigroupSum={concat:function(e,t){return e+t}},t.semigroupProduct={concat:function(e,t){return e*t}}},4276:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.right=t.left=t.flap=t.Functor=t.Bifunctor=t.URI=t.bimap=t.mapLeft=t.map=t.separated=void 0;var n=r(3643),s=r(1124);t.separated=function(e,t){return{left:e,right:t}},t.map=function(e){return function(r){return(0,t.separated)((0,t.left)(r),e((0,t.right)(r)))}},t.mapLeft=function(e){return function(r){return(0,t.separated)(e((0,t.left)(r)),(0,t.right)(r))}},t.bimap=function(e,r){return function(n){return(0,t.separated)(e((0,t.left)(n)),r((0,t.right)(n)))}},t.URI="Separated",t.Bifunctor={URI:t.URI,mapLeft:function(e,r){return(0,n.pipe)(e,(0,t.mapLeft)(r))},bimap:function(e,r,s){return(0,n.pipe)(e,(0,t.bimap)(r,s))}},t.Functor={URI:t.URI,map:function(e,r){return(0,n.pipe)(e,(0,t.map)(r))}},t.flap=(0,s.flap)(t.Functor),t.left=function(e){return e.left},t.right=function(e){return e.right}},8084:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.wiltDefault=function(e,t){return function(r){var n=e.traverse(r);return function(e,s){return r.map(n(e,s),t.separate)}}},t.witherDefault=function(e,t){return function(r){var n=e.traverse(r);return function(e,s){return r.map(n(e,s),t.compact)}}},t.filterE=function(e){return function(t){var r=e.wither(t);return function(e){return function(n){return r(n,(function(r){return t.map(e(r),(function(e){return e?i.some(r):i.none}))}))}}}};var i=o(r(174))},9641:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.guard=function(e,t){return function(r){return r?t.of(void 0):e.zero()}}},3643:function(e,t){"use strict";var r=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,s=0,o=t.length;s<o;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))};function n(e){return e}function s(e){return function(){return e}}function o(e,t,r,n,s,o,i,a,u){switch(arguments.length){case 1:return e;case 2:return function(){return t(e.apply(this,arguments))};case 3:return function(){return r(t(e.apply(this,arguments)))};case 4:return function(){return n(r(t(e.apply(this,arguments))))};case 5:return function(){return s(n(r(t(e.apply(this,arguments)))))};case 6:return function(){return o(s(n(r(t(e.apply(this,arguments))))))};case 7:return function(){return i(o(s(n(r(t(e.apply(this,arguments)))))))};case 8:return function(){return a(i(o(s(n(r(t(e.apply(this,arguments))))))))};case 9:return function(){return u(a(i(o(s(n(r(t(e.apply(this,arguments)))))))))}}}function i(e){throw new Error("Called `absurd` function which should be uncallable")}Object.defineProperty(t,"__esModule",{value:!0}),t.dual=t.getEndomorphismMonoid=t.SK=t.hole=t.constVoid=t.constUndefined=t.constNull=t.constFalse=t.constTrue=t.unsafeCoerce=t.apply=t.getRing=t.getSemiring=t.getMonoid=t.getSemigroup=t.getBooleanAlgebra=void 0,t.identity=n,t.constant=s,t.flip=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return t.length>1?e(t[1],t[0]):function(r){return e(r)(t[0])}}},t.flow=o,t.tuple=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e},t.increment=function(e){return e+1},t.decrement=function(e){return e-1},t.absurd=i,t.tupled=function(e){return function(t){return e.apply(void 0,t)}},t.untupled=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e(t)}},t.pipe=function(e,t,r,n,s,o,i,a,u){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return r(t(e));case 4:return n(r(t(e)));case 5:return s(n(r(t(e))));case 6:return o(s(n(r(t(e)))));case 7:return i(o(s(n(r(t(e))))));case 8:return a(i(o(s(n(r(t(e)))))));case 9:return u(a(i(o(s(n(r(t(e))))))));default:for(var c=arguments[0],l=1;l<arguments.length;l++)c=arguments[l](c);return c}},t.not=function(e){return function(t){return!e(t)}},t.getBooleanAlgebra=function(e){return function(){return{meet:function(t,r){return function(n){return e.meet(t(n),r(n))}},join:function(t,r){return function(n){return e.join(t(n),r(n))}},zero:function(){return e.zero},one:function(){return e.one},implies:function(t,r){return function(n){return e.implies(t(n),r(n))}},not:function(t){return function(r){return e.not(t(r))}}}}},t.getSemigroup=function(e){return function(){return{concat:function(t,r){return function(n){return e.concat(t(n),r(n))}}}}},t.getMonoid=function(e){var r=(0,t.getSemigroup)(e);return function(){return{concat:r().concat,empty:function(){return e.empty}}}},t.getSemiring=function(e){return{add:function(t,r){return function(n){return e.add(t(n),r(n))}},zero:function(){return e.zero},mul:function(t,r){return function(n){return e.mul(t(n),r(n))}},one:function(){return e.one}}},t.getRing=function(e){var r=(0,t.getSemiring)(e);return{add:r.add,mul:r.mul,one:r.one,zero:r.zero,sub:function(t,r){return function(n){return e.sub(t(n),r(n))}}}},t.apply=function(e){return function(t){return t(e)}},t.unsafeCoerce=n,t.constTrue=s(!0),t.constFalse=s(!1),t.constNull=s(null),t.constUndefined=s(void 0),t.constVoid=t.constUndefined,t.hole=i,t.SK=function(e,t){return t},t.getEndomorphismMonoid=function(){return{concat:function(e,t){return o(e,t)},empty:n}},t.dual=function(e,t){var n="number"==typeof e?function(t){return t.length>=e}:e;return function(){var e=Array.from(arguments);return n(arguments)?t.apply(this,e):function(n){return t.apply(void 0,r([n],e,!1))}}}},174:function(e,t,r){"use strict";var n=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,s=0,o=t.length;s<o;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.flatMapReader=t.flatMapTask=t.flatMapIO=t.flatMapEither=t.flatMapOption=t.flatMapNullable=t.liftOption=t.liftNullable=t.fromReadonlyNonEmptyArray=t.has=t.emptyRecord=t.emptyReadonlyArray=t.tail=t.head=t.isNonEmpty=t.singleton=t.right=t.left=t.isRight=t.isLeft=t.some=t.none=t.isSome=t.isNone=void 0;var s=r(3643);t.isNone=function(e){return"None"===e._tag},t.isSome=function(e){return"Some"===e._tag},t.none={_tag:"None"},t.some=function(e){return{_tag:"Some",value:e}},t.isLeft=function(e){return"Left"===e._tag},t.isRight=function(e){return"Right"===e._tag},t.left=function(e){return{_tag:"Left",left:e}},t.right=function(e){return{_tag:"Right",right:e}},t.singleton=function(e){return[e]},t.isNonEmpty=function(e){return e.length>0},t.head=function(e){return e[0]},t.tail=function(e){return e.slice(1)},t.emptyReadonlyArray=[],t.emptyRecord={},t.has=Object.prototype.hasOwnProperty,t.fromReadonlyNonEmptyArray=function(e){return n([e[0]],e.slice(1),!0)},t.liftNullable=function(e){return function(r,n){return function(){for(var s=[],o=0;o<arguments.length;o++)s[o]=arguments[o];var i=r.apply(void 0,s);return e.fromEither(null==i?(0,t.left)(n.apply(void 0,s)):(0,t.right)(i))}}},t.liftOption=function(e){return function(r,n){return function(){for(var s=[],o=0;o<arguments.length;o++)s[o]=arguments[o];var i=r.apply(void 0,s);return e.fromEither((0,t.isNone)(i)?(0,t.left)(n.apply(void 0,s)):(0,t.right)(i.value))}}},t.flatMapNullable=function(e,r){return(0,s.dual)(3,(function(n,s,o){return r.flatMap(n,(0,t.liftNullable)(e)(s,o))}))},t.flatMapOption=function(e,r){return(0,s.dual)(3,(function(n,s,o){return r.flatMap(n,(0,t.liftOption)(e)(s,o))}))},t.flatMapEither=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromEither(n(t))}))}))},t.flatMapIO=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromIO(n(t))}))}))},t.flatMapTask=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromTask(n(t))}))}))},t.flatMapReader=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromReader(n(t))}))}))}},5254:(e,t,r)=>{"use strict";t.Im=t.Kg=t.Ie=t.YI=t.Eq=void 0;r(8524);t.Eq={equals:function(e,t){return e===t}},t.YI={concat:function(e,t){return e+t}},t.Ie="",t.YI.concat,t.Ie,t.Eq.equals,t.Kg=function(e){return"string"==typeof e},t.Im=function(e){return 0===e.length}},6075:e=>{var t;self,t=()=>{return e={7629:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(9474),i=r(1687),a=r(8652),u=r(8160),c=r(3292),l=r(6354),f=r(8901),p=r(9708),m=r(6914),d=r(2294),h=r(6133),g=r(1152),y=r(8863),v=r(2036),b={Base:class{constructor(e){this.type=e,this.$_root=null,this._definition={},this._reset()}_reset(){this._ids=new d.Ids,this._preferences=null,this._refs=new h.Manager,this._cache=null,this._valids=null,this._invalids=null,this._flags={},this._rules=[],this._singleRules=new Map,this.$_terms={},this.$_temp={ruleset:null,whens:{}}}describe(){return n("function"==typeof p.describe,"Manifest functionality disabled"),p.describe(this)}allow(...e){return u.verifyFlat(e,"allow"),this._values(e,"_valids")}alter(e){n(e&&"object"==typeof e&&!Array.isArray(e),"Invalid targets argument"),n(!this._inRuleset(),"Cannot set alterations inside a ruleset");const t=this.clone();t.$_terms.alterations=t.$_terms.alterations||[];for(const r in e){const s=e[r];n("function"==typeof s,"Alteration adjuster for",r,"must be a function"),t.$_terms.alterations.push({target:r,adjuster:s})}return t.$_temp.ruleset=!1,t}artifact(e){return n(void 0!==e,"Artifact cannot be undefined"),n(!this._cache,"Cannot set an artifact with a rule cache"),this.$_setFlag("artifact",e)}cast(e){return n(!1===e||"string"==typeof e,"Invalid to value"),n(!1===e||this._definition.cast[e],"Type",this.type,"does not support casting to",e),this.$_setFlag("cast",!1===e?void 0:e)}default(e,t){return this._default("default",e,t)}description(e){return n(e&&"string"==typeof e,"Description must be a non-empty string"),this.$_setFlag("description",e)}empty(e){const t=this.clone();return void 0!==e&&(e=t.$_compile(e,{override:!1})),t.$_setFlag("empty",e,{clone:!1})}error(e){return n(e,"Missing error"),n(e instanceof Error||"function"==typeof e,"Must provide a valid Error object or a function"),this.$_setFlag("error",e)}example(e,t={}){return n(void 0!==e,"Missing example"),u.assertOptions(t,["override"]),this._inner("examples",e,{single:!0,override:t.override})}external(e,t){return"object"==typeof e&&(n(!t,"Cannot combine options with description"),t=e.description,e=e.method),n("function"==typeof e,"Method must be a function"),n(void 0===t||t&&"string"==typeof t,"Description must be a non-empty string"),this._inner("externals",{method:e,description:t},{single:!0})}failover(e,t){return this._default("failover",e,t)}forbidden(){return this.presence("forbidden")}id(e){return e?(n("string"==typeof e,"id must be a non-empty string"),n(/^[^\.]+$/.test(e),"id cannot contain period character"),this.$_setFlag("id",e)):this.$_setFlag("id",void 0)}invalid(...e){return this._values(e,"_invalids")}label(e){return n(e&&"string"==typeof e,"Label name must be a non-empty string"),this.$_setFlag("label",e)}meta(e){return n(void 0!==e,"Meta cannot be undefined"),this._inner("metas",e,{single:!0})}note(...e){n(e.length,"Missing notes");for(const t of e)n(t&&"string"==typeof t,"Notes must be non-empty strings");return this._inner("notes",e)}only(e=!0){return n("boolean"==typeof e,"Invalid mode:",e),this.$_setFlag("only",e)}optional(){return this.presence("optional")}prefs(e){n(e,"Missing preferences"),n(void 0===e.context,"Cannot override context"),n(void 0===e.externals,"Cannot override externals"),n(void 0===e.warnings,"Cannot override warnings"),n(void 0===e.debug,"Cannot override debug"),u.checkPreferences(e);const t=this.clone();return t._preferences=u.preferences(t._preferences,e),t}presence(e){return n(["optional","required","forbidden"].includes(e),"Unknown presence mode",e),this.$_setFlag("presence",e)}raw(e=!0){return this.$_setFlag("result",e?"raw":void 0)}result(e){return n(["raw","strip"].includes(e),"Unknown result mode",e),this.$_setFlag("result",e)}required(){return this.presence("required")}strict(e){const t=this.clone(),r=void 0!==e&&!e;return t._preferences=u.preferences(t._preferences,{convert:r}),t}strip(e=!0){return this.$_setFlag("result",e?"strip":void 0)}tag(...e){n(e.length,"Missing tags");for(const t of e)n(t&&"string"==typeof t,"Tags must be non-empty strings");return this._inner("tags",e)}unit(e){return n(e&&"string"==typeof e,"Unit name must be a non-empty string"),this.$_setFlag("unit",e)}valid(...e){u.verifyFlat(e,"valid");const t=this.allow(...e);return t.$_setFlag("only",!!t._valids,{clone:!1}),t}when(e,t){const r=this.clone();r.$_terms.whens||(r.$_terms.whens=[]);const s=c.when(r,e,t);if(!["any","link"].includes(r.type)){const e=s.is?[s]:s.switch;for(const t of e)n(!t.then||"any"===t.then.type||t.then.type===r.type,"Cannot combine",r.type,"with",t.then&&t.then.type),n(!t.otherwise||"any"===t.otherwise.type||t.otherwise.type===r.type,"Cannot combine",r.type,"with",t.otherwise&&t.otherwise.type)}return r.$_terms.whens.push(s),r.$_mutateRebuild()}cache(e){n(!this._inRuleset(),"Cannot set caching inside a ruleset"),n(!this._cache,"Cannot override schema cache"),n(void 0===this._flags.artifact,"Cannot cache a rule with an artifact");const t=this.clone();return t._cache=e||a.provider.provision(),t.$_temp.ruleset=!1,t}clone(){const e=Object.create(Object.getPrototypeOf(this));return this._assign(e)}concat(e){n(u.isSchema(e),"Invalid schema object"),n("any"===this.type||"any"===e.type||e.type===this.type,"Cannot merge type",this.type,"with another type:",e.type),n(!this._inRuleset(),"Cannot concatenate onto a schema with open ruleset"),n(!e._inRuleset(),"Cannot concatenate a schema with open ruleset");let t=this.clone();if("any"===this.type&&"any"!==e.type){const r=e.clone();for(const e of Object.keys(t))"type"!==e&&(r[e]=t[e]);t=r}t._ids.concat(e._ids),t._refs.register(e,h.toSibling),t._preferences=t._preferences?u.preferences(t._preferences,e._preferences):e._preferences,t._valids=v.merge(t._valids,e._valids,e._invalids),t._invalids=v.merge(t._invalids,e._invalids,e._valids);for(const r of e._singleRules.keys())t._singleRules.has(r)&&(t._rules=t._rules.filter((e=>e.keep||e.name!==r)),t._singleRules.delete(r));for(const r of e._rules)e._definition.rules[r.method].multi||t._singleRules.set(r.name,r),t._rules.push(r);if(t._flags.empty&&e._flags.empty){t._flags.empty=t._flags.empty.concat(e._flags.empty);const r=Object.assign({},e._flags);delete r.empty,i(t._flags,r)}else if(e._flags.empty){t._flags.empty=e._flags.empty;const r=Object.assign({},e._flags);delete r.empty,i(t._flags,r)}else i(t._flags,e._flags);for(const r in e.$_terms){const n=e.$_terms[r];n?t.$_terms[r]?t.$_terms[r]=t.$_terms[r].concat(n):t.$_terms[r]=n.slice():t.$_terms[r]||(t.$_terms[r]=n)}return this.$_root._tracer&&this.$_root._tracer._combine(t,[this,e]),t.$_mutateRebuild()}extend(e){return n(!e.base,"Cannot extend type with another base"),f.type(this,e)}extract(e){return e=Array.isArray(e)?e:e.split("."),this._ids.reach(e)}fork(e,t){n(!this._inRuleset(),"Cannot fork inside a ruleset");let r=this;for(let n of[].concat(e))n=Array.isArray(n)?n:n.split("."),r=r._ids.fork(n,t,r);return r.$_temp.ruleset=!1,r}rule(e){const t=this._definition;u.assertOptions(e,Object.keys(t.modifiers)),n(!1!==this.$_temp.ruleset,"Cannot apply rules to empty ruleset or the last rule added does not support rule properties");const r=null===this.$_temp.ruleset?this._rules.length-1:this.$_temp.ruleset;n(r>=0&&r<this._rules.length,"Cannot apply rules to empty ruleset");const o=this.clone();for(let i=r;i<o._rules.length;++i){const r=o._rules[i],a=s(r);for(const s in e)t.modifiers[s](a,e[s]),n(a.name===r.name,"Cannot change rule name");o._rules[i]=a,o._singleRules.get(a.name)===r&&o._singleRules.set(a.name,a)}return o.$_temp.ruleset=!1,o.$_mutateRebuild()}get ruleset(){n(!this._inRuleset(),"Cannot start a new ruleset without closing the previous one");const e=this.clone();return e.$_temp.ruleset=e._rules.length,e}get $(){return this.ruleset}tailor(e){e=[].concat(e),n(!this._inRuleset(),"Cannot tailor inside a ruleset");let t=this;if(this.$_terms.alterations)for(const{target:r,adjuster:s}of this.$_terms.alterations)e.includes(r)&&(t=s(t),n(u.isSchema(t),"Alteration adjuster for",r,"failed to return a schema object"));return t=t.$_modify({each:t=>t.tailor(e),ref:!1}),t.$_temp.ruleset=!1,t.$_mutateRebuild()}tracer(){return g.location?g.location(this):this}validate(e,t){return y.entry(e,this,t)}validateAsync(e,t){return y.entryAsync(e,this,t)}$_addRule(e){"string"==typeof e&&(e={name:e}),n(e&&"object"==typeof e,"Invalid options"),n(e.name&&"string"==typeof e.name,"Invalid rule name");for(const t in e)n("_"!==t[0],"Cannot set private rule properties");const t=Object.assign({},e);t._resolve=[],t.method=t.method||t.name;const r=this._definition.rules[t.method],s=t.args;n(r,"Unknown rule",t.method);const o=this.clone();if(s){n(1===Object.keys(s).length||Object.keys(s).length===this._definition.rules[t.name].args.length,"Invalid rule definition for",this.type,t.name);for(const e in s){let i=s[e];if(r.argsByName){const a=r.argsByName.get(e);if(a.ref&&u.isResolvable(i))t._resolve.push(e),o.$_mutateRegister(i);else if(a.normalize&&(i=a.normalize(i),s[e]=i),a.assert){const t=u.validateArg(i,e,a);n(!t,t,"or reference")}}void 0!==i?s[e]=i:delete s[e]}}return r.multi||(o._ruleRemove(t.name,{clone:!1}),o._singleRules.set(t.name,t)),!1===o.$_temp.ruleset&&(o.$_temp.ruleset=null),r.priority?o._rules.unshift(t):o._rules.push(t),o}$_compile(e,t){return c.schema(this.$_root,e,t)}$_createError(e,t,r,n,s,o={}){const i=!1!==o.flags?this._flags:{},a=o.messages?m.merge(this._definition.messages,o.messages):this._definition.messages;return new l.Report(e,t,r,i,a,n,s)}$_getFlag(e){return this._flags[e]}$_getRule(e){return this._singleRules.get(e)}$_mapLabels(e){return e=Array.isArray(e)?e:e.split("."),this._ids.labels(e)}$_match(e,t,r,n){(r=Object.assign({},r)).abortEarly=!0,r._externals=!1,t.snapshot();const s=!y.validate(e,this,t,r,n).errors;return t.restore(),s}$_modify(e){return u.assertOptions(e,["each","once","ref","schema"]),d.schema(this,e)||this}$_mutateRebuild(){return n(!this._inRuleset(),"Cannot add this rule inside a ruleset"),this._refs.reset(),this._ids.reset(),this.$_modify({each:(e,{source:t,name:r,path:n,key:s})=>{const o=this._definition[t][r]&&this._definition[t][r].register;!1!==o&&this.$_mutateRegister(e,{family:o,key:s})}}),this._definition.rebuild&&this._definition.rebuild(this),this.$_temp.ruleset=!1,this}$_mutateRegister(e,{family:t,key:r}={}){this._refs.register(e,t),this._ids.register(e,{key:r})}$_property(e){return this._definition.properties[e]}$_reach(e){return this._ids.reach(e)}$_rootReferences(){return this._refs.roots()}$_setFlag(e,t,r={}){n("_"===e[0]||!this._inRuleset(),"Cannot set flag inside a ruleset");const s=this._definition.flags[e]||{};if(o(t,s.default)&&(t=void 0),o(t,this._flags[e]))return this;const i=!1!==r.clone?this.clone():this;return void 0!==t?(i._flags[e]=t,i.$_mutateRegister(t)):delete i._flags[e],"_"!==e[0]&&(i.$_temp.ruleset=!1),i}$_parent(e,...t){return this[e][u.symbols.parent].call(this,...t)}$_validate(e,t,r){return y.validate(e,this,t,r)}_assign(e){e.type=this.type,e.$_root=this.$_root,e.$_temp=Object.assign({},this.$_temp),e.$_temp.whens={},e._ids=this._ids.clone(),e._preferences=this._preferences,e._valids=this._valids&&this._valids.clone(),e._invalids=this._invalids&&this._invalids.clone(),e._rules=this._rules.slice(),e._singleRules=s(this._singleRules,{shallow:!0}),e._refs=this._refs.clone(),e._flags=Object.assign({},this._flags),e._cache=null,e.$_terms={};for(const t in this.$_terms)e.$_terms[t]=this.$_terms[t]?this.$_terms[t].slice():null;e.$_super={};for(const t in this.$_super)e.$_super[t]=this._super[t].bind(e);return e}_bare(){const e=this.clone();e._reset();const t=e._definition.terms;for(const r in t){const n=t[r];e.$_terms[r]=n.init}return e.$_mutateRebuild()}_default(e,t,r={}){return u.assertOptions(r,"literal"),n(void 0!==t,"Missing",e,"value"),n("function"==typeof t||!r.literal,"Only function value supports literal option"),"function"==typeof t&&r.literal&&(t={[u.symbols.literal]:!0,literal:t}),this.$_setFlag(e,t)}_generate(e,t,r){if(!this.$_terms.whens)return{schema:this};const n=[],s=[];for(let o=0;o<this.$_terms.whens.length;++o){const i=this.$_terms.whens[o];if(i.concat){n.push(i.concat),s.push(`${o}.concat`);continue}const a=i.ref?i.ref.resolve(e,t,r):e,u=i.is?[i]:i.switch,c=s.length;for(let c=0;c<u.length;++c){const{is:l,then:f,otherwise:p}=u[c],m=`${o}${i.switch?"."+c:""}`;if(l.$_match(a,t.nest(l,`${m}.is`),r)){if(f){const o=t.localize([...t.path,`${m}.then`],t.ancestors,t.schemas),{schema:i,id:a}=f._generate(e,o,r);n.push(i),s.push(`${m}.then${a?`(${a})`:""}`);break}}else if(p){const o=t.localize([...t.path,`${m}.otherwise`],t.ancestors,t.schemas),{schema:i,id:a}=p._generate(e,o,r);n.push(i),s.push(`${m}.otherwise${a?`(${a})`:""}`);break}}if(i.break&&s.length>c)break}const o=s.join(", ");if(t.mainstay.tracer.debug(t,"rule","when",o),!o)return{schema:this};if(!t.mainstay.tracer.active&&this.$_temp.whens[o])return{schema:this.$_temp.whens[o],id:o};let i=this;this._definition.generate&&(i=this._definition.generate(this,e,t,r));for(const e of n)i=i.concat(e);return this.$_root._tracer&&this.$_root._tracer._combine(i,[this,...n]),this.$_temp.whens[o]=i,{schema:i,id:o}}_inner(e,t,r={}){n(!this._inRuleset(),`Cannot set ${e} inside a ruleset`);const s=this.clone();return s.$_terms[e]&&!r.override||(s.$_terms[e]=[]),r.single?s.$_terms[e].push(t):s.$_terms[e].push(...t),s.$_temp.ruleset=!1,s}_inRuleset(){return null!==this.$_temp.ruleset&&!1!==this.$_temp.ruleset}_ruleRemove(e,t={}){if(!this._singleRules.has(e))return this;const r=!1!==t.clone?this.clone():this;r._singleRules.delete(e);const n=[];for(let t=0;t<r._rules.length;++t){const s=r._rules[t];s.name!==e||s.keep?n.push(s):r._inRuleset()&&t<r.$_temp.ruleset&&--r.$_temp.ruleset}return r._rules=n,r}_values(e,t){u.verifyFlat(e,t.slice(1,-1));const r=this.clone(),s=e[0]===u.symbols.override;if(s&&(e=e.slice(1)),!r[t]&&e.length?r[t]=new v:s&&(r[t]=e.length?new v:null,r.$_mutateRebuild()),!r[t])return r;s&&r[t].override();for(const s of e){n(void 0!==s,"Cannot call allow/valid/invalid with undefined"),n(s!==u.symbols.override,"Override must be the first value");const e="_invalids"===t?"_valids":"_invalids";r[e]&&(r[e].remove(s),r[e].length||(n("_valids"===t||!r._flags.only,"Setting invalid value",s,"leaves schema rejecting all values due to previous valid rule"),r[e]=null)),r[t].add(s,r._refs)}return r}}};b.Base.prototype[u.symbols.any]={version:u.version,compile:c.compile,root:"$_root"},b.Base.prototype.isImmutable=!0,b.Base.prototype.deny=b.Base.prototype.invalid,b.Base.prototype.disallow=b.Base.prototype.invalid,b.Base.prototype.equal=b.Base.prototype.valid,b.Base.prototype.exist=b.Base.prototype.required,b.Base.prototype.not=b.Base.prototype.invalid,b.Base.prototype.options=b.Base.prototype.prefs,b.Base.prototype.preferences=b.Base.prototype.prefs,e.exports=new b.Base},8652:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(8160),i={max:1e3,supported:new Set(["undefined","boolean","number","string"])};t.provider={provision:e=>new i.Cache(e)},i.Cache=class{constructor(e={}){o.assertOptions(e,["max"]),n(void 0===e.max||e.max&&e.max>0&&isFinite(e.max),"Invalid max cache size"),this._max=e.max||i.max,this._map=new Map,this._list=new i.List}get length(){return this._map.size}set(e,t){if(null!==e&&!i.supported.has(typeof e))return;let r=this._map.get(e);if(r)return r.value=t,void this._list.first(r);r=this._list.unshift({key:e,value:t}),this._map.set(e,r),this._compact()}get(e){const t=this._map.get(e);if(t)return this._list.first(t),s(t.value)}_compact(){if(this._map.size>this._max){const e=this._list.pop();this._map.delete(e.key)}}},i.List=class{constructor(){this.tail=null,this.head=null}unshift(e){return e.next=null,e.prev=this.head,this.head&&(this.head.next=e),this.head=e,this.tail||(this.tail=e),e}first(e){e!==this.head&&(this._remove(e),this.unshift(e))}pop(){return this._remove(this.tail)}_remove(e){const{next:t,prev:r}=e;return t.prev=r,r&&(r.next=t),e===this.tail&&(this.tail=t),e.prev=null,e.next=null,e}}},8160:(e,t,r)=>{"use strict";const n=r(375),s=r(7916),o=r(5934);let i,a;const u={isoDate:/^(?:[-+]\d{2})?(?:\d{4}(?!\d{2}\b))(?:(-?)(?:(?:0[1-9]|1[0-2])(?:\1(?:[12]\d|0[1-9]|3[01]))?|W(?:[0-4]\d|5[0-2])(?:-?[1-7])?|(?:00[1-9]|0[1-9]\d|[12]\d{2}|3(?:[0-5]\d|6[1-6])))(?![T]$|[T][\d]+Z$)(?:[T\s](?:(?:(?:[01]\d|2[0-3])(?:(:?)[0-5]\d)?|24\:?00)(?:[.,]\d+(?!:))?)(?:\2[0-5]\d(?:[.,]\d+)?)?(?:[Z]|(?:[+-])(?:[01]\d|2[0-3])(?::?[0-5]\d)?)?)?)?$/};t.version=o.version,t.defaults={abortEarly:!0,allowUnknown:!1,artifacts:!1,cache:!0,context:null,convert:!0,dateFormat:"iso",errors:{escapeHtml:!1,label:"path",language:null,render:!0,stack:!1,wrap:{label:'"',array:"[]"}},externals:!0,messages:{},nonEnumerables:!1,noDefaults:!1,presence:"optional",skipFunctions:!1,stripUnknown:!1,warnings:!1},t.symbols={any:Symbol.for("@hapi/joi/schema"),arraySingle:Symbol("arraySingle"),deepDefault:Symbol("deepDefault"),errors:Symbol("errors"),literal:Symbol("literal"),override:Symbol("override"),parent:Symbol("parent"),prefs:Symbol("prefs"),ref:Symbol("ref"),template:Symbol("template"),values:Symbol("values")},t.assertOptions=function(e,t,r="Options"){n(e&&"object"==typeof e&&!Array.isArray(e),"Options must be of type object");const s=Object.keys(e).filter((e=>!t.includes(e)));n(0===s.length,`${r} contain unknown keys: ${s}`)},t.checkPreferences=function(e){a=a||r(3378);const t=a.preferences.validate(e);if(t.error)throw new s([t.error.details[0].message])},t.compare=function(e,t,r){switch(r){case"=":return e===t;case">":return e>t;case"<":return e<t;case">=":return e>=t;case"<=":return e<=t}},t.default=function(e,t){return void 0===e?t:e},t.isIsoDate=function(e){return u.isoDate.test(e)},t.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.isResolvable=function(e){return!!e&&(e[t.symbols.ref]||e[t.symbols.template])},t.isSchema=function(e,r={}){const s=e&&e[t.symbols.any];return!!s&&(n(r.legacy||s.version===t.version,"Cannot mix different versions of joi schemas"),!0)},t.isValues=function(e){return e[t.symbols.values]},t.limit=function(e){return Number.isSafeInteger(e)&&e>=0},t.preferences=function(e,n){i=i||r(6914),e=e||{},n=n||{};const s=Object.assign({},e,n);return n.errors&&e.errors&&(s.errors=Object.assign({},e.errors,n.errors),s.errors.wrap=Object.assign({},e.errors.wrap,n.errors.wrap)),n.messages&&(s.messages=i.compile(n.messages,e.messages)),delete s[t.symbols.prefs],s},t.tryWithPath=function(e,t,r={}){try{return e()}catch(e){throw void 0!==e.path?e.path=t+"."+e.path:e.path=t,r.append&&(e.message=`${e.message} (${e.path})`),e}},t.validateArg=function(e,r,{assert:n,message:s}){if(t.isSchema(n)){const t=n.validate(e);if(!t.error)return;return t.error.message}if(!n(e))return r?`${r} ${s}`:s},t.verifyFlat=function(e,t){for(const r of e)n(!Array.isArray(r),"Method no longer accepts array arguments:",t)}},3292:(e,t,r)=>{"use strict";const n=r(375),s=r(8160),o=r(6133),i={};t.schema=function(e,t,r={}){s.assertOptions(r,["appendPath","override"]);try{return i.schema(e,t,r)}catch(e){throw r.appendPath&&void 0!==e.path&&(e.message=`${e.message} (${e.path})`),e}},i.schema=function(e,t,r){n(void 0!==t,"Invalid undefined schema"),Array.isArray(t)&&(n(t.length,"Invalid empty array schema"),1===t.length&&(t=t[0]));const o=(t,...n)=>!1!==r.override?t.valid(e.override,...n):t.valid(...n);if(i.simple(t))return o(e,t);if("function"==typeof t)return e.custom(t);if(n("object"==typeof t,"Invalid schema content:",typeof t),s.isResolvable(t))return o(e,t);if(s.isSchema(t))return t;if(Array.isArray(t)){for(const r of t)if(!i.simple(r))return e.alternatives().try(...t);return o(e,...t)}return t instanceof RegExp?e.string().regex(t):t instanceof Date?o(e.date(),t):(n(Object.getPrototypeOf(t)===Object.getPrototypeOf({}),"Schema can only contain plain objects"),e.object().keys(t))},t.ref=function(e,t){return o.isRef(e)?e:o.create(e,t)},t.compile=function(e,r,o={}){s.assertOptions(o,["legacy"]);const a=r&&r[s.symbols.any];if(a)return n(o.legacy||a.version===s.version,"Cannot mix different versions of joi schemas:",a.version,s.version),r;if("object"!=typeof r||!o.legacy)return t.schema(e,r,{appendPath:!0});const u=i.walk(r);return u?u.compile(u.root,r):t.schema(e,r,{appendPath:!0})},i.walk=function(e){if("object"!=typeof e)return null;if(Array.isArray(e)){for(const t of e){const e=i.walk(t);if(e)return e}return null}const t=e[s.symbols.any];if(t)return{root:e[t.root],compile:t.compile};n(Object.getPrototypeOf(e)===Object.getPrototypeOf({}),"Schema can only contain plain objects");for(const t in e){const r=i.walk(e[t]);if(r)return r}return null},i.simple=function(e){return null===e||["boolean","string","number"].includes(typeof e)},t.when=function(e,r,a){if(void 0===a&&(n(r&&"object"==typeof r,"Missing options"),a=r,r=o.create(".")),Array.isArray(a)&&(a={switch:a}),s.assertOptions(a,["is","not","then","otherwise","switch","break"]),s.isSchema(r))return n(void 0===a.is,'"is" can not be used with a schema condition'),n(void 0===a.not,'"not" can not be used with a schema condition'),n(void 0===a.switch,'"switch" can not be used with a schema condition'),i.condition(e,{is:r,then:a.then,otherwise:a.otherwise,break:a.break});if(n(o.isRef(r)||"string"==typeof r,"Invalid condition:",r),n(void 0===a.not||void 0===a.is,'Cannot combine "is" with "not"'),void 0===a.switch){let u=a;void 0!==a.not&&(u={is:a.not,then:a.otherwise,otherwise:a.then,break:a.break});let c=void 0!==u.is?e.$_compile(u.is):e.$_root.invalid(null,!1,0,"").required();return n(void 0!==u.then||void 0!==u.otherwise,'options must have at least one of "then", "otherwise", or "switch"'),n(void 0===u.break||void 0===u.then||void 0===u.otherwise,"Cannot specify then, otherwise, and break all together"),void 0===a.is||o.isRef(a.is)||s.isSchema(a.is)||(c=c.required()),i.condition(e,{ref:t.ref(r),is:c,then:u.then,otherwise:u.otherwise,break:u.break})}n(Array.isArray(a.switch),'"switch" must be an array'),n(void 0===a.is,'Cannot combine "switch" with "is"'),n(void 0===a.not,'Cannot combine "switch" with "not"'),n(void 0===a.then,'Cannot combine "switch" with "then"');const u={ref:t.ref(r),switch:[],break:a.break};for(let t=0;t<a.switch.length;++t){const r=a.switch[t],i=t===a.switch.length-1;s.assertOptions(r,i?["is","then","otherwise"]:["is","then"]),n(void 0!==r.is,'Switch statement missing "is"'),n(void 0!==r.then,'Switch statement missing "then"');const c={is:e.$_compile(r.is),then:e.$_compile(r.then)};if(o.isRef(r.is)||s.isSchema(r.is)||(c.is=c.is.required()),i){n(void 0===a.otherwise||void 0===r.otherwise,'Cannot specify "otherwise" inside and outside a "switch"');const t=void 0!==a.otherwise?a.otherwise:r.otherwise;void 0!==t&&(n(void 0===u.break,"Cannot specify both otherwise and break"),c.otherwise=e.$_compile(t))}u.switch.push(c)}return u},i.condition=function(e,t){for(const r of["then","otherwise"])void 0===t[r]?delete t[r]:t[r]=e.$_compile(t[r]);return t}},6354:(e,t,r)=>{"use strict";const n=r(5688),s=r(8160),o=r(3328);t.Report=class{constructor(e,r,n,s,o,i,a){if(this.code=e,this.flags=s,this.messages=o,this.path=i.path,this.prefs=a,this.state=i,this.value=r,this.message=null,this.template=null,this.local=n||{},this.local.label=t.label(this.flags,this.state,this.prefs,this.messages),void 0===this.value||this.local.hasOwnProperty("value")||(this.local.value=this.value),this.path.length){const e=this.path[this.path.length-1];"object"!=typeof e&&(this.local.key=e)}}_setTemplate(e){if(this.template=e,!this.flags.label&&0===this.path.length){const e=this._template(this.template,"root");e&&(this.local.label=e)}}toString(){if(this.message)return this.message;const e=this.code;if(!this.prefs.errors.render)return this.code;const t=this._template(this.template)||this._template(this.prefs.messages)||this._template(this.messages);return void 0===t?`Error code "${e}" is not defined, your custom type is missing the correct messages definition`:(this.message=t.render(this.value,this.state,this.prefs,this.local,{errors:this.prefs.errors,messages:[this.prefs.messages,this.messages]}),this.prefs.errors.label||(this.message=this.message.replace(/^"" /,"").trim()),this.message)}_template(e,r){return t.template(this.value,e,r||this.code,this.state,this.prefs)}},t.path=function(e){let t="";for(const r of e)"object"!=typeof r&&("string"==typeof r?(t&&(t+="."),t+=r):t+=`[${r}]`);return t},t.template=function(e,t,r,n,i){if(!t)return;if(o.isTemplate(t))return"root"!==r?t:null;let a=i.errors.language;if(s.isResolvable(a)&&(a=a.resolve(e,n,i)),a&&t[a]){if(void 0!==t[a][r])return t[a][r];if(void 0!==t[a]["*"])return t[a]["*"]}return t[r]?t[r]:t["*"]},t.label=function(e,r,n,s){if(!n.errors.label)return"";if(e.label)return e.label;let o=r.path;return"key"===n.errors.label&&r.path.length>1&&(o=r.path.slice(-1)),t.path(o)||t.template(null,n.messages,"root",r,n)||s&&t.template(null,s,"root",r,n)||"value"},t.process=function(e,r,n){if(!e)return null;const{override:s,message:o,details:i}=t.details(e);if(s)return s;if(n.errors.stack)return new t.ValidationError(o,i,r);const a=Error.stackTraceLimit;Error.stackTraceLimit=0;const u=new t.ValidationError(o,i,r);return Error.stackTraceLimit=a,u},t.details=function(e,t={}){let r=[];const n=[];for(const s of e){if(s instanceof Error){if(!1!==t.override)return{override:s};const e=s.toString();r.push(e),n.push({message:e,type:"override",context:{error:s}});continue}const e=s.toString();r.push(e),n.push({message:e,path:s.path.filter((e=>"object"!=typeof e)),type:s.code,context:s.local})}return r.length>1&&(r=[...new Set(r)]),{message:r.join(". "),details:n}},t.ValidationError=class extends Error{constructor(e,t,r){super(e),this._original=r,this.details=t}static isError(e){return e instanceof t.ValidationError}},t.ValidationError.prototype.isJoi=!0,t.ValidationError.prototype.name="ValidationError",t.ValidationError.prototype.annotate=n.error},8901:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(8160),i=r(6914),a={};t.type=function(e,t){const r=Object.getPrototypeOf(e),u=s(r),c=e._assign(Object.create(u)),l=Object.assign({},t);delete l.base,u._definition=l;const f=r._definition||{};l.messages=i.merge(f.messages,l.messages),l.properties=Object.assign({},f.properties,l.properties),c.type=l.type,l.flags=Object.assign({},f.flags,l.flags);const p=Object.assign({},f.terms);if(l.terms)for(const e in l.terms){const t=l.terms[e];n(void 0===c.$_terms[e],"Invalid term override for",l.type,e),c.$_terms[e]=t.init,p[e]=t}l.terms=p,l.args||(l.args=f.args),l.prepare=a.prepare(l.prepare,f.prepare),l.coerce&&("function"==typeof l.coerce&&(l.coerce={method:l.coerce}),l.coerce.from&&!Array.isArray(l.coerce.from)&&(l.coerce={method:l.coerce.method,from:[].concat(l.coerce.from)})),l.coerce=a.coerce(l.coerce,f.coerce),l.validate=a.validate(l.validate,f.validate);const m=Object.assign({},f.rules);if(l.rules)for(const e in l.rules){const t=l.rules[e];n("object"==typeof t,"Invalid rule definition for",l.type,e);let r=t.method;if(void 0===r&&(r=function(){return this.$_addRule(e)}),r&&(n(!u[e],"Rule conflict in",l.type,e),u[e]=r),n(!m[e],"Rule conflict in",l.type,e),m[e]=t,t.alias){const e=[].concat(t.alias);for(const r of e)u[r]=t.method}t.args&&(t.argsByName=new Map,t.args=t.args.map((e=>("string"==typeof e&&(e={name:e}),n(!t.argsByName.has(e.name),"Duplicated argument name",e.name),o.isSchema(e.assert)&&(e.assert=e.assert.strict().label(e.name)),t.argsByName.set(e.name,e),e))))}l.rules=m;const d=Object.assign({},f.modifiers);if(l.modifiers)for(const e in l.modifiers){n(!u[e],"Rule conflict in",l.type,e);const t=l.modifiers[e];n("function"==typeof t,"Invalid modifier definition for",l.type,e);const r=function(t){return this.rule({[e]:t})};u[e]=r,d[e]=t}if(l.modifiers=d,l.overrides){u._super=r,c.$_super={};for(const e in l.overrides)n(r[e],"Cannot override missing",e),l.overrides[e][o.symbols.parent]=r[e],c.$_super[e]=r[e].bind(c);Object.assign(u,l.overrides)}l.cast=Object.assign({},f.cast,l.cast);const h=Object.assign({},f.manifest,l.manifest);return h.build=a.build(l.manifest&&l.manifest.build,f.manifest&&f.manifest.build),l.manifest=h,l.rebuild=a.rebuild(l.rebuild,f.rebuild),c},a.build=function(e,t){return e&&t?function(r,n){return t(e(r,n),n)}:e||t},a.coerce=function(e,t){return e&&t?{from:e.from&&t.from?[...new Set([...e.from,...t.from])]:null,method(r,n){let s;if((!t.from||t.from.includes(typeof r))&&(s=t.method(r,n),s)){if(s.errors||void 0===s.value)return s;r=s.value}if(!e.from||e.from.includes(typeof r)){const t=e.method(r,n);if(t)return t}return s}}:e||t},a.prepare=function(e,t){return e&&t?function(r,n){const s=e(r,n);if(s){if(s.errors||void 0===s.value)return s;r=s.value}return t(r,n)||s}:e||t},a.rebuild=function(e,t){return e&&t?function(r){t(r),e(r)}:e||t},a.validate=function(e,t){return e&&t?function(r,n){const s=t(r,n);if(s){if(s.errors&&(!Array.isArray(s.errors)||s.errors.length))return s;r=s.value}return e(r,n)||s}:e||t}},5107:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(8652),i=r(8160),a=r(3292),u=r(6354),c=r(8901),l=r(9708),f=r(6133),p=r(3328),m=r(1152);let d;const h={types:{alternatives:r(4946),any:r(8068),array:r(546),boolean:r(4937),date:r(7500),function:r(390),link:r(8785),number:r(3832),object:r(8966),string:r(7417),symbol:r(8826)},aliases:{alt:"alternatives",bool:"boolean",func:"function"},root:function(){const e={_types:new Set(Object.keys(h.types))};for(const t of e._types)e[t]=function(...e){return n(!e.length||["alternatives","link","object"].includes(t),"The",t,"type does not allow arguments"),h.generate(this,h.types[t],e)};for(const t of["allow","custom","disallow","equal","exist","forbidden","invalid","not","only","optional","options","prefs","preferences","required","strip","valid","when"])e[t]=function(...e){return this.any()[t](...e)};Object.assign(e,h.methods);for(const t in h.aliases){const r=h.aliases[t];e[t]=e[r]}return e.x=e.expression,m.setup&&m.setup(e),e}};h.methods={ValidationError:u.ValidationError,version:i.version,cache:o.provider,assert(e,t,...r){h.assert(e,t,!0,r)},attempt:(e,t,...r)=>h.assert(e,t,!1,r),build(e){return n("function"==typeof l.build,"Manifest functionality disabled"),l.build(this,e)},checkPreferences(e){i.checkPreferences(e)},compile(e,t){return a.compile(this,e,t)},defaults(e){n("function"==typeof e,"modifier must be a function");const t=Object.assign({},this);for(const r of t._types){const s=e(t[r]());n(i.isSchema(s),"modifier must return a valid schema object"),t[r]=function(...e){return h.generate(this,s,e)}}return t},expression:(...e)=>new p(...e),extend(...e){i.verifyFlat(e,"extend"),d=d||r(3378),n(e.length,"You need to provide at least one extension"),this.assert(e,d.extensions);const t=Object.assign({},this);t._types=new Set(t._types);for(let r of e){"function"==typeof r&&(r=r(t)),this.assert(r,d.extension);const e=h.expandExtension(r,t);for(const r of e){n(void 0===t[r.type]||t._types.has(r.type),"Cannot override name",r.type);const e=r.base||this.any(),s=c.type(e,r);t._types.add(r.type),t[r.type]=function(...e){return h.generate(this,s,e)}}}return t},isError:u.ValidationError.isError,isExpression:p.isTemplate,isRef:f.isRef,isSchema:i.isSchema,in:(...e)=>f.in(...e),override:i.symbols.override,ref:(...e)=>f.create(...e),types(){const e={};for(const t of this._types)e[t]=this[t]();for(const t in h.aliases)e[t]=this[t]();return e}},h.assert=function(e,t,r,n){const o=n[0]instanceof Error||"string"==typeof n[0]?n[0]:null,a=null!==o?n[1]:n[0],c=t.validate(e,i.preferences({errors:{stack:!0}},a||{}));let l=c.error;if(!l)return c.value;if(o instanceof Error)throw o;const f=r&&"function"==typeof l.annotate?l.annotate():l.message;throw l instanceof u.ValidationError==0&&(l=s(l)),l.message=o?`${o} ${f}`:f,l},h.generate=function(e,t,r){return n(e,"Must be invoked on a Joi instance."),t.$_root=e,t._definition.args&&r.length?t._definition.args(t,...r):t},h.expandExtension=function(e,t){if("string"==typeof e.type)return[e];const r=[];for(const n of t._types)if(e.type.test(n)){const s=Object.assign({},e);s.type=n,s.base=t[n](),r.push(s)}return r},e.exports=h.root()},6914:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(3328);t.compile=function(e,t){if("string"==typeof e)return n(!t,"Cannot set single message string"),new o(e);if(o.isTemplate(e))return n(!t,"Cannot set single message template"),e;n("object"==typeof e&&!Array.isArray(e),"Invalid message options"),t=t?s(t):{};for(let r in e){const s=e[r];if("root"===r||o.isTemplate(s)){t[r]=s;continue}if("string"==typeof s){t[r]=new o(s);continue}n("object"==typeof s&&!Array.isArray(s),"Invalid message for",r);const i=r;for(r in t[i]=t[i]||{},s){const e=s[r];"root"===r||o.isTemplate(e)?t[i][r]=e:(n("string"==typeof e,"Invalid message for",r,"in",i),t[i][r]=new o(e))}}return t},t.decompile=function(e){const t={};for(let r in e){const n=e[r];if("root"===r){t.root=n;continue}if(o.isTemplate(n)){t[r]=n.describe({compact:!0});continue}const s=r;for(r in t[s]={},n){const e=n[r];"root"!==r?t[s][r]=e.describe({compact:!0}):t[s].root=e}}return t},t.merge=function(e,r){if(!e)return t.compile(r);if(!r)return e;if("string"==typeof r)return new o(r);if(o.isTemplate(r))return r;const i=s(e);for(let e in r){const t=r[e];if("root"===e||o.isTemplate(t)){i[e]=t;continue}if("string"==typeof t){i[e]=new o(t);continue}n("object"==typeof t&&!Array.isArray(t),"Invalid message for",e);const s=e;for(e in i[s]=i[s]||{},t){const r=t[e];"root"===e||o.isTemplate(r)?i[s][e]=r:(n("string"==typeof r,"Invalid message for",e,"in",s),i[s][e]=new o(r))}}return i}},2294:(e,t,r)=>{"use strict";const n=r(375),s=r(8160),o=r(6133),i={};t.Ids=i.Ids=class{constructor(){this._byId=new Map,this._byKey=new Map,this._schemaChain=!1}clone(){const e=new i.Ids;return e._byId=new Map(this._byId),e._byKey=new Map(this._byKey),e._schemaChain=this._schemaChain,e}concat(e){e._schemaChain&&(this._schemaChain=!0);for(const[t,r]of e._byId.entries())n(!this._byKey.has(t),"Schema id conflicts with existing key:",t),this._byId.set(t,r);for(const[t,r]of e._byKey.entries())n(!this._byId.has(t),"Schema key conflicts with existing id:",t),this._byKey.set(t,r)}fork(e,t,r){const o=this._collect(e);o.push({schema:r});const a=o.shift();let u={id:a.id,schema:t(a.schema)};n(s.isSchema(u.schema),"adjuster function failed to return a joi schema type");for(const e of o)u={id:e.id,schema:i.fork(e.schema,u.id,u.schema)};return u.schema}labels(e,t=[]){const r=e[0],n=this._get(r);if(!n)return[...t,...e].join(".");const s=e.slice(1);return t=[...t,n.schema._flags.label||r],s.length?n.schema._ids.labels(s,t):t.join(".")}reach(e,t=[]){const r=e[0],s=this._get(r);n(s,"Schema does not contain path",[...t,...e].join("."));const o=e.slice(1);return o.length?s.schema._ids.reach(o,[...t,r]):s.schema}register(e,{key:t}={}){if(!e||!s.isSchema(e))return;(e.$_property("schemaChain")||e._ids._schemaChain)&&(this._schemaChain=!0);const r=e._flags.id;if(r){const t=this._byId.get(r);n(!t||t.schema===e,"Cannot add different schemas with the same id:",r),n(!this._byKey.has(r),"Schema id conflicts with existing key:",r),this._byId.set(r,{schema:e,id:r})}t&&(n(!this._byKey.has(t),"Schema already contains key:",t),n(!this._byId.has(t),"Schema key conflicts with existing id:",t),this._byKey.set(t,{schema:e,id:t}))}reset(){this._byId=new Map,this._byKey=new Map,this._schemaChain=!1}_collect(e,t=[],r=[]){const s=e[0],o=this._get(s);n(o,"Schema does not contain path",[...t,...e].join(".")),r=[o,...r];const i=e.slice(1);return i.length?o.schema._ids._collect(i,[...t,s],r):r}_get(e){return this._byId.get(e)||this._byKey.get(e)}},i.fork=function(e,r,n){const s=t.schema(e,{each:(e,{key:t})=>{if(r===(e._flags.id||t))return n},ref:!1});return s?s.$_mutateRebuild():e},t.schema=function(e,t){let r;for(const n in e._flags){if("_"===n[0])continue;const s=i.scan(e._flags[n],{source:"flags",name:n},t);void 0!==s&&(r=r||e.clone(),r._flags[n]=s)}for(let n=0;n<e._rules.length;++n){const s=e._rules[n],o=i.scan(s.args,{source:"rules",name:s.name},t);if(void 0!==o){r=r||e.clone();const t=Object.assign({},s);t.args=o,r._rules[n]=t,r._singleRules.get(s.name)===s&&r._singleRules.set(s.name,t)}}for(const n in e.$_terms){if("_"===n[0])continue;const s=i.scan(e.$_terms[n],{source:"terms",name:n},t);void 0!==s&&(r=r||e.clone(),r.$_terms[n]=s)}return r},i.scan=function(e,t,r,n,a){const u=n||[];if(null===e||"object"!=typeof e)return;let c;if(Array.isArray(e)){for(let n=0;n<e.length;++n){const s="terms"===t.source&&"keys"===t.name&&e[n].key,o=i.scan(e[n],t,r,[n,...u],s);void 0!==o&&(c=c||e.slice(),c[n]=o)}return c}if(!1!==r.schema&&s.isSchema(e)||!1!==r.ref&&o.isRef(e)){const n=r.each(e,{...t,path:u,key:a});if(n===e)return;return n}for(const n in e){if("_"===n[0])continue;const s=i.scan(e[n],t,r,[n,...u],a);void 0!==s&&(c=c||Object.assign({},e),c[n]=s)}return c}},6133:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(9621),i=r(8160);let a;const u={symbol:Symbol("ref"),defaults:{adjust:null,in:!1,iterables:null,map:null,separator:".",type:"value"}};t.create=function(e,t={}){n("string"==typeof e,"Invalid reference key:",e),i.assertOptions(t,["adjust","ancestor","in","iterables","map","prefix","render","separator"]),n(!t.prefix||"object"==typeof t.prefix,"options.prefix must be of type object");const r=Object.assign({},u.defaults,t);delete r.prefix;const s=r.separator,o=u.context(e,s,t.prefix);if(r.type=o.type,e=o.key,"value"===r.type)if(o.root&&(n(!s||e[0]!==s,"Cannot specify relative path with root prefix"),r.ancestor="root",e||(e=null)),s&&s===e)e=null,r.ancestor=0;else if(void 0!==r.ancestor)n(!s||!e||e[0]!==s,"Cannot combine prefix with ancestor option");else{const[t,n]=u.ancestor(e,s);n&&""===(e=e.slice(n))&&(e=null),r.ancestor=t}return r.path=s?null===e?[]:e.split(s):[e],new u.Ref(r)},t.in=function(e,r={}){return t.create(e,{...r,in:!0})},t.isRef=function(e){return!!e&&!!e[i.symbols.ref]},u.Ref=class{constructor(e){n("object"==typeof e,"Invalid reference construction"),i.assertOptions(e,["adjust","ancestor","in","iterables","map","path","render","separator","type","depth","key","root","display"]),n([!1,void 0].includes(e.separator)||"string"==typeof e.separator&&1===e.separator.length,"Invalid separator"),n(!e.adjust||"function"==typeof e.adjust,"options.adjust must be a function"),n(!e.map||Array.isArray(e.map),"options.map must be an array"),n(!e.map||!e.adjust,"Cannot set both map and adjust options"),Object.assign(this,u.defaults,e),n("value"===this.type||void 0===this.ancestor,"Non-value references cannot reference ancestors"),Array.isArray(this.map)&&(this.map=new Map(this.map)),this.depth=this.path.length,this.key=this.path.length?this.path.join(this.separator):null,this.root=this.path[0],this.updateDisplay()}resolve(e,t,r,s,o={}){return n(!this.in||o.in,"Invalid in() reference usage"),"global"===this.type?this._resolve(r.context,t,o):"local"===this.type?this._resolve(s,t,o):this.ancestor?"root"===this.ancestor?this._resolve(t.ancestors[t.ancestors.length-1],t,o):(n(this.ancestor<=t.ancestors.length,"Invalid reference exceeds the schema root:",this.display),this._resolve(t.ancestors[this.ancestor-1],t,o)):this._resolve(e,t,o)}_resolve(e,t,r){let n;if("value"===this.type&&t.mainstay.shadow&&!1!==r.shadow&&(n=t.mainstay.shadow.get(this.absolute(t))),void 0===n&&(n=o(e,this.path,{iterables:this.iterables,functions:!0})),this.adjust&&(n=this.adjust(n)),this.map){const e=this.map.get(n);void 0!==e&&(n=e)}return t.mainstay&&t.mainstay.tracer.resolve(t,this,n),n}toString(){return this.display}absolute(e){return[...e.path.slice(0,-this.ancestor),...this.path]}clone(){return new u.Ref(this)}describe(){const e={path:this.path};"value"!==this.type&&(e.type=this.type),"."!==this.separator&&(e.separator=this.separator),"value"===this.type&&1!==this.ancestor&&(e.ancestor=this.ancestor),this.map&&(e.map=[...this.map]);for(const t of["adjust","iterables","render"])null!==this[t]&&void 0!==this[t]&&(e[t]=this[t]);return!1!==this.in&&(e.in=!0),{ref:e}}updateDisplay(){const e=null!==this.key?this.key:"";if("value"!==this.type)return void(this.display=`ref:${this.type}:${e}`);if(!this.separator)return void(this.display=`ref:${e}`);if(!this.ancestor)return void(this.display=`ref:${this.separator}${e}`);if("root"===this.ancestor)return void(this.display=`ref:root:${e}`);if(1===this.ancestor)return void(this.display=`ref:${e||".."}`);const t=new Array(this.ancestor+1).fill(this.separator).join("");this.display=`ref:${t}${e||""}`}},u.Ref.prototype[i.symbols.ref]=!0,t.build=function(e){return"value"===(e=Object.assign({},u.defaults,e)).type&&void 0===e.ancestor&&(e.ancestor=1),new u.Ref(e)},u.context=function(e,t,r={}){if(e=e.trim(),r){const n=void 0===r.global?"$":r.global;if(n!==t&&e.startsWith(n))return{key:e.slice(n.length),type:"global"};const s=void 0===r.local?"#":r.local;if(s!==t&&e.startsWith(s))return{key:e.slice(s.length),type:"local"};const o=void 0===r.root?"/":r.root;if(o!==t&&e.startsWith(o))return{key:e.slice(o.length),type:"value",root:!0}}return{key:e,type:"value"}},u.ancestor=function(e,t){if(!t)return[1,0];if(e[0]!==t)return[1,0];if(e[1]!==t)return[0,1];let r=2;for(;e[r]===t;)++r;return[r-1,r]},t.toSibling=0,t.toParent=1,t.Manager=class{constructor(){this.refs=[]}register(e,n){if(e)if(n=void 0===n?t.toParent:n,Array.isArray(e))for(const t of e)this.register(t,n);else if(i.isSchema(e))for(const t of e._refs.refs)t.ancestor-n>=0&&this.refs.push({ancestor:t.ancestor-n,root:t.root});else t.isRef(e)&&"value"===e.type&&e.ancestor-n>=0&&this.refs.push({ancestor:e.ancestor-n,root:e.root}),a=a||r(3328),a.isTemplate(e)&&this.register(e.refs(),n)}get length(){return this.refs.length}clone(){const e=new t.Manager;return e.refs=s(this.refs),e}reset(){this.refs=[]}roots(){return this.refs.filter((e=>!e.ancestor)).map((e=>e.root))}}},3378:(e,t,r)=>{"use strict";const n=r(5107),s={};s.wrap=n.string().min(1).max(2).allow(!1),t.preferences=n.object({allowUnknown:n.boolean(),abortEarly:n.boolean(),artifacts:n.boolean(),cache:n.boolean(),context:n.object(),convert:n.boolean(),dateFormat:n.valid("date","iso","string","time","utc"),debug:n.boolean(),errors:{escapeHtml:n.boolean(),label:n.valid("path","key",!1),language:[n.string(),n.object().ref()],render:n.boolean(),stack:n.boolean(),wrap:{label:s.wrap,array:s.wrap,string:s.wrap}},externals:n.boolean(),messages:n.object(),noDefaults:n.boolean(),nonEnumerables:n.boolean(),presence:n.valid("required","optional","forbidden"),skipFunctions:n.boolean(),stripUnknown:n.object({arrays:n.boolean(),objects:n.boolean()}).or("arrays","objects").allow(!0,!1),warnings:n.boolean()}).strict(),s.nameRx=/^[a-zA-Z0-9]\w*$/,s.rule=n.object({alias:n.array().items(n.string().pattern(s.nameRx)).single(),args:n.array().items(n.string(),n.object({name:n.string().pattern(s.nameRx).required(),ref:n.boolean(),assert:n.alternatives([n.function(),n.object().schema()]).conditional("ref",{is:!0,then:n.required()}),normalize:n.function(),message:n.string().when("assert",{is:n.function(),then:n.required()})})),convert:n.boolean(),manifest:n.boolean(),method:n.function().allow(!1),multi:n.boolean(),validate:n.function()}),t.extension=n.object({type:n.alternatives([n.string(),n.object().regex()]).required(),args:n.function(),cast:n.object().pattern(s.nameRx,n.object({from:n.function().maxArity(1).required(),to:n.function().minArity(1).maxArity(2).required()})),base:n.object().schema().when("type",{is:n.object().regex(),then:n.forbidden()}),coerce:[n.function().maxArity(3),n.object({method:n.function().maxArity(3).required(),from:n.array().items(n.string()).single()})],flags:n.object().pattern(s.nameRx,n.object({setter:n.string(),default:n.any()})),manifest:{build:n.function().arity(2)},messages:[n.object(),n.string()],modifiers:n.object().pattern(s.nameRx,n.function().minArity(1).maxArity(2)),overrides:n.object().pattern(s.nameRx,n.function()),prepare:n.function().maxArity(3),rebuild:n.function().arity(1),rules:n.object().pattern(s.nameRx,s.rule),terms:n.object().pattern(s.nameRx,n.object({init:n.array().allow(null).required(),manifest:n.object().pattern(/.+/,[n.valid("schema","single"),n.object({mapped:n.object({from:n.string().required(),to:n.string().required()}).required()})])})),validate:n.function().maxArity(3)}).strict(),t.extensions=n.array().items(n.object(),n.function().arity(1)).strict(),s.desc={buffer:n.object({buffer:n.string()}),func:n.object({function:n.function().required(),options:{literal:!0}}),override:n.object({override:!0}),ref:n.object({ref:n.object({type:n.valid("value","global","local"),path:n.array().required(),separator:n.string().length(1).allow(!1),ancestor:n.number().min(0).integer().allow("root"),map:n.array().items(n.array().length(2)).min(1),adjust:n.function(),iterables:n.boolean(),in:n.boolean(),render:n.boolean()}).required()}),regex:n.object({regex:n.string().min(3)}),special:n.object({special:n.valid("deep").required()}),template:n.object({template:n.string().required(),options:n.object()}),value:n.object({value:n.alternatives([n.object(),n.array()]).required()})},s.desc.entity=n.alternatives([n.array().items(n.link("...")),n.boolean(),n.function(),n.number(),n.string(),s.desc.buffer,s.desc.func,s.desc.ref,s.desc.regex,s.desc.special,s.desc.template,s.desc.value,n.link("/")]),s.desc.values=n.array().items(null,n.boolean(),n.function(),n.number().allow(1/0,-1/0),n.string().allow(""),n.symbol(),s.desc.buffer,s.desc.func,s.desc.override,s.desc.ref,s.desc.regex,s.desc.template,s.desc.value),s.desc.messages=n.object().pattern(/.+/,[n.string(),s.desc.template,n.object().pattern(/.+/,[n.string(),s.desc.template])]),t.description=n.object({type:n.string().required(),flags:n.object({cast:n.string(),default:n.any(),description:n.string(),empty:n.link("/"),failover:s.desc.entity,id:n.string(),label:n.string(),only:!0,presence:["optional","required","forbidden"],result:["raw","strip"],strip:n.boolean(),unit:n.string()}).unknown(),preferences:{allowUnknown:n.boolean(),abortEarly:n.boolean(),artifacts:n.boolean(),cache:n.boolean(),convert:n.boolean(),dateFormat:["date","iso","string","time","utc"],errors:{escapeHtml:n.boolean(),label:["path","key"],language:[n.string(),s.desc.ref],wrap:{label:s.wrap,array:s.wrap}},externals:n.boolean(),messages:s.desc.messages,noDefaults:n.boolean(),nonEnumerables:n.boolean(),presence:["required","optional","forbidden"],skipFunctions:n.boolean(),stripUnknown:n.object({arrays:n.boolean(),objects:n.boolean()}).or("arrays","objects").allow(!0,!1),warnings:n.boolean()},allow:s.desc.values,invalid:s.desc.values,rules:n.array().min(1).items({name:n.string().required(),args:n.object().min(1),keep:n.boolean(),message:[n.string(),s.desc.messages],warn:n.boolean()}),keys:n.object().pattern(/.*/,n.link("/")),link:s.desc.ref}).pattern(/^[a-z]\w*$/,n.any())},493:(e,t,r)=>{"use strict";const n=r(8571),s=r(9621),o=r(8160),i={value:Symbol("value")};e.exports=i.State=class{constructor(e,t,r){this.path=e,this.ancestors=t,this.mainstay=r.mainstay,this.schemas=r.schemas,this.debug=null}localize(e,t=null,r=null){const n=new i.State(e,t,this);return r&&n.schemas&&(n.schemas=[i.schemas(r),...n.schemas]),n}nest(e,t){const r=new i.State(this.path,this.ancestors,this);return r.schemas=r.schemas&&[i.schemas(e),...r.schemas],r.debug=t,r}shadow(e,t){this.mainstay.shadow=this.mainstay.shadow||new i.Shadow,this.mainstay.shadow.set(this.path,e,t)}snapshot(){this.mainstay.shadow&&(this._snapshot=n(this.mainstay.shadow.node(this.path))),this.mainstay.snapshot()}restore(){this.mainstay.shadow&&(this.mainstay.shadow.override(this.path,this._snapshot),this._snapshot=void 0),this.mainstay.restore()}commit(){this.mainstay.shadow&&(this.mainstay.shadow.override(this.path,this._snapshot),this._snapshot=void 0),this.mainstay.commit()}},i.schemas=function(e){return o.isSchema(e)?{schema:e}:e},i.Shadow=class{constructor(){this._values=null}set(e,t,r){if(!e.length)return;if("strip"===r&&"number"==typeof e[e.length-1])return;this._values=this._values||new Map;let n=this._values;for(let t=0;t<e.length;++t){const r=e[t];let s=n.get(r);s||(s=new Map,n.set(r,s)),n=s}n[i.value]=t}get(e){const t=this.node(e);if(t)return t[i.value]}node(e){if(this._values)return s(this._values,e,{iterables:!0})}override(e,t){if(!this._values)return;const r=e.slice(0,-1),n=e[e.length-1],o=s(this._values,r,{iterables:!0});t?o.set(n,t):o&&o.delete(n)}}},3328:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(5277),i=r(1447),a=r(8160),u=r(6354),c=r(6133),l={symbol:Symbol("template"),opens:new Array(1e3).join("\0"),closes:new Array(1e3).join(""),dateFormat:{date:Date.prototype.toDateString,iso:Date.prototype.toISOString,string:Date.prototype.toString,time:Date.prototype.toTimeString,utc:Date.prototype.toUTCString}};e.exports=l.Template=class{constructor(e,t){if(n("string"==typeof e,"Template source must be a string"),n(!e.includes("\0")&&!e.includes(""),"Template source cannot contain reserved control characters"),this.source=e,this.rendered=e,this._template=null,t){const{functions:e,...r}=t;this._settings=Object.keys(r).length?s(r):void 0,this._functions=e,this._functions&&(n(Object.keys(this._functions).every((e=>"string"==typeof e)),"Functions keys must be strings"),n(Object.values(this._functions).every((e=>"function"==typeof e)),"Functions values must be functions"))}else this._settings=void 0,this._functions=void 0;this._parse()}_parse(){if(!this.source.includes("{"))return;const e=l.encode(this.source),t=l.split(e);let r=!1;const n=[],s=t.shift();s&&n.push(s);for(const e of t){const t="{"!==e[0],s=t?"}":"}}",o=e.indexOf(s);if(-1===o||"{"===e[1]){n.push(`{${l.decode(e)}`);continue}let i=e.slice(t?0:1,o);const a=":"===i[0];a&&(i=i.slice(1));const u=this._ref(l.decode(i),{raw:t,wrapped:a});n.push(u),"string"!=typeof u&&(r=!0);const c=e.slice(o+s.length);c&&n.push(l.decode(c))}r?this._template=n:this.rendered=n.join("")}static date(e,t){return l.dateFormat[t.dateFormat].call(e)}describe(e={}){if(!this._settings&&e.compact)return this.source;const t={template:this.source};return this._settings&&(t.options=this._settings),this._functions&&(t.functions=this._functions),t}static build(e){return new l.Template(e.template,e.options||e.functions?{...e.options,functions:e.functions}:void 0)}isDynamic(){return!!this._template}static isTemplate(e){return!!e&&!!e[a.symbols.template]}refs(){if(!this._template)return;const e=[];for(const t of this._template)"string"!=typeof t&&e.push(...t.refs);return e}resolve(e,t,r,n){return this._template&&1===this._template.length?this._part(this._template[0],e,t,r,n,{}):this.render(e,t,r,n)}_part(e,...t){return e.ref?e.ref.resolve(...t):e.formula.evaluate(t)}render(e,t,r,n,s={}){if(!this.isDynamic())return this.rendered;const i=[];for(const a of this._template)if("string"==typeof a)i.push(a);else{const u=this._part(a,e,t,r,n,s),c=l.stringify(u,e,t,r,n,s);if(void 0!==c){const e=a.raw||!1===(s.errors&&s.errors.escapeHtml)?c:o(c);i.push(l.wrap(e,a.wrapped&&r.errors.wrap.label))}}return i.join("")}_ref(e,{raw:t,wrapped:r}){const n=[],s=e=>{const t=c.create(e,this._settings);return n.push(t),e=>{const r=t.resolve(...e);return void 0!==r?r:null}};try{const t=this._functions?{...l.functions,...this._functions}:l.functions;var o=new i.Parser(e,{reference:s,functions:t,constants:l.constants})}catch(t){throw t.message=`Invalid template variable "${e}" fails due to: ${t.message}`,t}if(o.single){if("reference"===o.single.type){const e=n[0];return{ref:e,raw:t,refs:n,wrapped:r||"local"===e.type&&"label"===e.key}}return l.stringify(o.single.value)}return{formula:o,raw:t,refs:n}}toString(){return this.source}},l.Template.prototype[a.symbols.template]=!0,l.Template.prototype.isImmutable=!0,l.encode=function(e){return e.replace(/\\(\{+)/g,((e,t)=>l.opens.slice(0,t.length))).replace(/\\(\}+)/g,((e,t)=>l.closes.slice(0,t.length)))},l.decode=function(e){return e.replace(/\u0000/g,"{").replace(/\u0001/g,"}")},l.split=function(e){const t=[];let r="";for(let n=0;n<e.length;++n){const s=e[n];if("{"===s){let s="";for(;n+1<e.length&&"{"===e[n+1];)s+="{",++n;t.push(r),r=s}else r+=s}return t.push(r),t},l.wrap=function(e,t){return t?1===t.length?`${t}${e}${t}`:`${t[0]}${e}${t[1]}`:e},l.stringify=function(e,t,r,n,s,o={}){const i=typeof e,a=n&&n.errors&&n.errors.wrap||{};let u=!1;if(c.isRef(e)&&e.render&&(u=e.in,e=e.resolve(t,r,n,s,{in:e.in,...o})),null===e)return"null";if("string"===i)return l.wrap(e,o.arrayItems&&a.string);if("number"===i||"function"===i||"symbol"===i)return e.toString();if("object"!==i)return JSON.stringify(e);if(e instanceof Date)return l.Template.date(e,n);if(e instanceof Map){const t=[];for(const[r,n]of e.entries())t.push(`${r.toString()} -> ${n.toString()}`);e=t}if(!Array.isArray(e))return e.toString();const f=[];for(const i of e)f.push(l.stringify(i,t,r,n,s,{arrayItems:!0,...o}));return l.wrap(f.join(", "),!u&&a.array)},l.constants={true:!0,false:!1,null:null,second:1e3,minute:6e4,hour:36e5,day:864e5},l.functions={if:(e,t,r)=>e?t:r,length:e=>"string"==typeof e?e.length:e&&"object"==typeof e?Array.isArray(e)?e.length:Object.keys(e).length:null,msg(e){const[t,r,n,s,o]=this,i=o.messages;if(!i)return"";const a=u.template(t,i[0],e,r,n)||u.template(t,i[1],e,r,n);return a?a.render(t,r,n,s,o):""},number:e=>"number"==typeof e?e:"string"==typeof e?parseFloat(e):"boolean"==typeof e?e?1:0:e instanceof Date?e.getTime():null}},4946:(e,t,r)=>{"use strict";const n=r(375),s=r(1687),o=r(8068),i=r(8160),a=r(3292),u=r(6354),c=r(6133),l={};e.exports=o.extend({type:"alternatives",flags:{match:{default:"any"}},terms:{matches:{init:[],register:c.toSibling}},args:(e,...t)=>1===t.length&&Array.isArray(t[0])?e.try(...t[0]):e.try(...t),validate(e,t){const{schema:r,error:n,state:o,prefs:i}=t;if(r._flags.match){const t=[],a=[];for(let n=0;n<r.$_terms.matches.length;++n){const s=r.$_terms.matches[n],u=o.nest(s.schema,`match.${n}`);u.snapshot();const c=s.schema.$_validate(e,u,i);c.errors?(a.push(c.errors),u.restore()):(t.push(c.value),u.commit())}if(0===t.length)return{errors:n("alternatives.any",{details:a.map((e=>u.details(e,{override:!1})))})};if("one"===r._flags.match)return 1===t.length?{value:t[0]}:{errors:n("alternatives.one")};if(t.length!==r.$_terms.matches.length)return{errors:n("alternatives.all",{details:a.map((e=>u.details(e,{override:!1})))})};const c=e=>e.$_terms.matches.some((e=>"object"===e.schema.type||"alternatives"===e.schema.type&&c(e.schema)));return c(r)?{value:t.reduce(((e,t)=>s(e,t,{mergeArrays:!1})))}:{value:t[t.length-1]}}const a=[];for(let t=0;t<r.$_terms.matches.length;++t){const n=r.$_terms.matches[t];if(n.schema){const r=o.nest(n.schema,`match.${t}`);r.snapshot();const s=n.schema.$_validate(e,r,i);if(!s.errors)return r.commit(),s;r.restore(),a.push({schema:n.schema,reports:s.errors});continue}const s=n.ref?n.ref.resolve(e,o,i):e,u=n.is?[n]:n.switch;for(let r=0;r<u.length;++r){const a=u[r],{is:c,then:l,otherwise:f}=a,p=`match.${t}${n.switch?"."+r:""}`;if(c.$_match(s,o.nest(c,`${p}.is`),i)){if(l)return l.$_validate(e,o.nest(l,`${p}.then`),i)}else if(f)return f.$_validate(e,o.nest(f,`${p}.otherwise`),i)}}return l.errors(a,t)},rules:{conditional:{method(e,t){n(!this._flags._endedSwitch,"Unreachable condition"),n(!this._flags.match,"Cannot combine match mode",this._flags.match,"with conditional rule"),n(void 0===t.break,"Cannot use break option with alternatives conditional");const r=this.clone(),s=a.when(r,e,t),o=s.is?[s]:s.switch;for(const e of o)if(e.then&&e.otherwise){r.$_setFlag("_endedSwitch",!0,{clone:!1});break}return r.$_terms.matches.push(s),r.$_mutateRebuild()}},match:{method(e){if(n(["any","one","all"].includes(e),"Invalid alternatives match mode",e),"any"!==e)for(const t of this.$_terms.matches)n(t.schema,"Cannot combine match mode",e,"with conditional rules");return this.$_setFlag("match",e)}},try:{method(...e){n(e.length,"Missing alternative schemas"),i.verifyFlat(e,"try"),n(!this._flags._endedSwitch,"Unreachable condition");const t=this.clone();for(const r of e)t.$_terms.matches.push({schema:t.$_compile(r)});return t.$_mutateRebuild()}}},overrides:{label(e){return this.$_parent("label",e).$_modify({each:(t,r)=>"is"!==r.path[0]&&"string"!=typeof t._flags.label?t.label(e):void 0,ref:!1})}},rebuild(e){e.$_modify({each:t=>{i.isSchema(t)&&"array"===t.type&&e.$_setFlag("_arrayItems",!0,{clone:!1})}})},manifest:{build(e,t){if(t.matches)for(const r of t.matches){const{schema:t,ref:n,is:s,not:o,then:i,otherwise:a}=r;e=t?e.try(t):n?e.conditional(n,{is:s,then:i,not:o,otherwise:a,switch:r.switch}):e.conditional(s,{then:i,otherwise:a})}return e}},messages:{"alternatives.all":"{{#label}} does not match all of the required types","alternatives.any":"{{#label}} does not match any of the allowed types","alternatives.match":"{{#label}} does not match any of the allowed types","alternatives.one":"{{#label}} matches more than one allowed type","alternatives.types":"{{#label}} must be one of {{#types}}"}}),l.errors=function(e,{error:t,state:r}){if(!e.length)return{errors:t("alternatives.any")};if(1===e.length)return{errors:e[0].reports};const n=new Set,s=[];for(const{reports:o,schema:i}of e){if(o.length>1)return l.unmatched(e,t);const a=o[0];if(a instanceof u.Report==0)return l.unmatched(e,t);if(a.state.path.length!==r.path.length){s.push({type:i.type,report:a});continue}if("any.only"===a.code){for(const e of a.local.valids)n.add(e);continue}const[c,f]=a.code.split(".");"base"!==f?s.push({type:i.type,report:a}):"object.base"===a.code?n.add(a.local.type):n.add(c)}return s.length?1===s.length?{errors:s[0].report}:l.unmatched(e,t):{errors:t("alternatives.types",{types:[...n]})}},l.unmatched=function(e,t){const r=[];for(const t of e)r.push(...t.reports);return{errors:t("alternatives.match",u.details(r,{override:!1}))}}},8068:(e,t,r)=>{"use strict";const n=r(375),s=r(7629),o=r(8160),i=r(6914);e.exports=s.extend({type:"any",flags:{only:{default:!1}},terms:{alterations:{init:null},examples:{init:null},externals:{init:null},metas:{init:[]},notes:{init:[]},shared:{init:null},tags:{init:[]},whens:{init:null}},rules:{custom:{method(e,t){return n("function"==typeof e,"Method must be a function"),n(void 0===t||t&&"string"==typeof t,"Description must be a non-empty string"),this.$_addRule({name:"custom",args:{method:e,description:t}})},validate(e,t,{method:r}){try{return r(e,t)}catch(e){return t.error("any.custom",{error:e})}},args:["method","description"],multi:!0},messages:{method(e){return this.prefs({messages:e})}},shared:{method(e){n(o.isSchema(e)&&e._flags.id,"Schema must be a schema with an id");const t=this.clone();return t.$_terms.shared=t.$_terms.shared||[],t.$_terms.shared.push(e),t.$_mutateRegister(e),t}},warning:{method(e,t){return n(e&&"string"==typeof e,"Invalid warning code"),this.$_addRule({name:"warning",args:{code:e,local:t},warn:!0})},validate:(e,t,{code:r,local:n})=>t.error(r,n),args:["code","local"],multi:!0}},modifiers:{keep(e,t=!0){e.keep=t},message(e,t){e.message=i.compile(t)},warn(e,t=!0){e.warn=t}},manifest:{build(e,t){for(const r in t){const n=t[r];if(["examples","externals","metas","notes","tags"].includes(r))for(const t of n)e=e[r.slice(0,-1)](t);else if("alterations"!==r)if("whens"!==r){if("shared"===r)for(const t of n)e=e.shared(t)}else for(const t of n){const{ref:r,is:n,not:s,then:o,otherwise:i,concat:a}=t;e=a?e.concat(a):r?e.when(r,{is:n,not:s,then:o,otherwise:i,switch:t.switch,break:t.break}):e.when(n,{then:o,otherwise:i,break:t.break})}else{const t={};for(const{target:e,adjuster:r}of n)t[e]=r;e=e.alter(t)}}return e}},messages:{"any.custom":"{{#label}} failed custom validation because {{#error.message}}","any.default":"{{#label}} threw an error when running default method","any.failover":"{{#label}} threw an error when running failover method","any.invalid":"{{#label}} contains an invalid value","any.only":'{{#label}} must be {if(#valids.length == 1, "", "one of ")}{{#valids}}',"any.ref":"{{#label}} {{#arg}} references {{:#ref}} which {{#reason}}","any.required":"{{#label}} is required","any.unknown":"{{#label}} is not allowed"}})},546:(e,t,r)=>{"use strict";const n=r(375),s=r(9474),o=r(9621),i=r(8068),a=r(8160),u=r(3292),c={};e.exports=i.extend({type:"array",flags:{single:{default:!1},sparse:{default:!1}},terms:{items:{init:[],manifest:"schema"},ordered:{init:[],manifest:"schema"},_exclusions:{init:[]},_inclusions:{init:[]},_requireds:{init:[]}},coerce:{from:"object",method(e,{schema:t,state:r,prefs:n}){if(!Array.isArray(e))return;const s=t.$_getRule("sort");return s?c.sort(t,e,s.args.options,r,n):void 0}},validate(e,{schema:t,error:r}){if(!Array.isArray(e)){if(t._flags.single){const t=[e];return t[a.symbols.arraySingle]=!0,{value:t}}return{errors:r("array.base")}}if(t.$_getRule("items")||t.$_terms.externals)return{value:e.slice()}},rules:{has:{method(e){e=this.$_compile(e,{appendPath:!0});const t=this.$_addRule({name:"has",args:{schema:e}});return t.$_mutateRegister(e),t},validate(e,{state:t,prefs:r,error:n},{schema:s}){const o=[e,...t.ancestors];for(let n=0;n<e.length;++n){const i=t.localize([...t.path,n],o,s);if(s.$_match(e[n],i,r))return e}const i=s._flags.label;return i?n("array.hasKnown",{patternLabel:i}):n("array.hasUnknown",null)},multi:!0},items:{method(...e){a.verifyFlat(e,"items");const t=this.$_addRule("items");for(let r=0;r<e.length;++r){const n=a.tryWithPath((()=>this.$_compile(e[r])),r,{append:!0});t.$_terms.items.push(n)}return t.$_mutateRebuild()},validate(e,{schema:t,error:r,state:n,prefs:s,errorsArray:o}){const i=t.$_terms._requireds.slice(),u=t.$_terms.ordered.slice(),l=[...t.$_terms._inclusions,...i],f=!e[a.symbols.arraySingle];delete e[a.symbols.arraySingle];const p=o();let m=e.length;for(let o=0;o<m;++o){const a=e[o];let d=!1,h=!1;const g=f?o:new Number(o),y=[...n.path,g];if(!t._flags.sparse&&void 0===a){if(p.push(r("array.sparse",{key:g,path:y,pos:o,value:void 0},n.localize(y))),s.abortEarly)return p;u.shift();continue}const v=[e,...n.ancestors];for(const e of t.$_terms._exclusions)if(e.$_match(a,n.localize(y,v,e),s,{presence:"ignore"})){if(p.push(r("array.excludes",{pos:o,value:a},n.localize(y))),s.abortEarly)return p;d=!0,u.shift();break}if(d)continue;if(t.$_terms.ordered.length){if(u.length){const i=u.shift(),l=i.$_validate(a,n.localize(y,v,i),s);if(l.errors){if(p.push(...l.errors),s.abortEarly)return p}else if("strip"===i._flags.result)c.fastSplice(e,o),--o,--m;else{if(!t._flags.sparse&&void 0===l.value){if(p.push(r("array.sparse",{key:g,path:y,pos:o,value:void 0},n.localize(y))),s.abortEarly)return p;continue}e[o]=l.value}continue}if(!t.$_terms.items.length){if(p.push(r("array.orderedLength",{pos:o,limit:t.$_terms.ordered.length})),s.abortEarly)return p;break}}const b=[];let _=i.length;for(let u=0;u<_;++u){const l=n.localize(y,v,i[u]);l.snapshot();const f=i[u].$_validate(a,l,s);if(b[u]=f,!f.errors){if(l.commit(),e[o]=f.value,h=!0,c.fastSplice(i,u),--u,--_,!t._flags.sparse&&void 0===f.value&&(p.push(r("array.sparse",{key:g,path:y,pos:o,value:void 0},n.localize(y))),s.abortEarly))return p;break}l.restore()}if(h)continue;const w=s.stripUnknown&&!!s.stripUnknown.arrays||!1;_=l.length;for(const u of l){let l;const f=i.indexOf(u);if(-1!==f)l=b[f];else{const i=n.localize(y,v,u);if(i.snapshot(),l=u.$_validate(a,i,s),!l.errors){i.commit(),"strip"===u._flags.result?(c.fastSplice(e,o),--o,--m):t._flags.sparse||void 0!==l.value?e[o]=l.value:(p.push(r("array.sparse",{key:g,path:y,pos:o,value:void 0},n.localize(y))),d=!0),h=!0;break}i.restore()}if(1===_){if(w){c.fastSplice(e,o),--o,--m,h=!0;break}if(p.push(...l.errors),s.abortEarly)return p;d=!0;break}}if(!d&&(t.$_terms._inclusions.length||t.$_terms._requireds.length)&&!h){if(w){c.fastSplice(e,o),--o,--m;continue}if(p.push(r("array.includes",{pos:o,value:a},n.localize(y))),s.abortEarly)return p}}return i.length&&c.fillMissedErrors(t,p,i,e,n,s),u.length&&(c.fillOrderedErrors(t,p,u,e,n,s),p.length||c.fillDefault(u,e,n,s)),p.length?p:e},priority:!0,manifest:!1},length:{method(e){return this.$_addRule({name:"length",args:{limit:e},operator:"="})},validate:(e,t,{limit:r},{name:n,operator:s,args:o})=>a.compare(e.length,r,s)?e:t.error("array."+n,{limit:o.limit,value:e}),args:[{name:"limit",ref:!0,assert:a.limit,message:"must be a positive integer"}]},max:{method(e){return this.$_addRule({name:"max",method:"length",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"length",args:{limit:e},operator:">="})}},ordered:{method(...e){a.verifyFlat(e,"ordered");const t=this.$_addRule("items");for(let r=0;r<e.length;++r){const n=a.tryWithPath((()=>this.$_compile(e[r])),r,{append:!0});c.validateSingle(n,t),t.$_mutateRegister(n),t.$_terms.ordered.push(n)}return t.$_mutateRebuild()}},single:{method(e){const t=void 0===e||!!e;return n(!t||!this._flags._arrayItems,"Cannot specify single rule when array has array items"),this.$_setFlag("single",t)}},sort:{method(e={}){a.assertOptions(e,["by","order"]);const t={order:e.order||"ascending"};return e.by&&(t.by=u.ref(e.by,{ancestor:0}),n(!t.by.ancestor,"Cannot sort by ancestor")),this.$_addRule({name:"sort",args:{options:t}})},validate(e,{error:t,state:r,prefs:n,schema:s},{options:o}){const{value:i,errors:a}=c.sort(s,e,o,r,n);if(a)return a;for(let r=0;r<e.length;++r)if(e[r]!==i[r])return t("array.sort",{order:o.order,by:o.by?o.by.key:"value"});return e},convert:!0},sparse:{method(e){const t=void 0===e||!!e;return this._flags.sparse===t?this:(t?this.clone():this.$_addRule("items")).$_setFlag("sparse",t,{clone:!1})}},unique:{method(e,t={}){n(!e||"function"==typeof e||"string"==typeof e,"comparator must be a function or a string"),a.assertOptions(t,["ignoreUndefined","separator"]);const r={name:"unique",args:{options:t,comparator:e}};if(e)if("string"==typeof e){const n=a.default(t.separator,".");r.path=n?e.split(n):[e]}else r.comparator=e;return this.$_addRule(r)},validate(e,{state:t,error:r,schema:i},{comparator:a,options:u},{comparator:c,path:l}){const f={string:Object.create(null),number:Object.create(null),undefined:Object.create(null),boolean:Object.create(null),bigint:Object.create(null),object:new Map,function:new Map,custom:new Map},p=c||s,m=u.ignoreUndefined;for(let s=0;s<e.length;++s){const i=l?o(e[s],l):e[s],u=c?f.custom:f[typeof i];if(n(u,"Failed to find unique map container for type",typeof i),u instanceof Map){const n=u.entries();let o;for(;!(o=n.next()).done;)if(p(o.value[0],i)){const n=t.localize([...t.path,s],[e,...t.ancestors]),i={pos:s,value:e[s],dupePos:o.value[1],dupeValue:e[o.value[1]]};return l&&(i.path=a),r("array.unique",i,n)}u.set(i,s)}else{if((!m||void 0!==i)&&void 0!==u[i]){const n={pos:s,value:e[s],dupePos:u[i],dupeValue:e[u[i]]};return l&&(n.path=a),r("array.unique",n,t.localize([...t.path,s],[e,...t.ancestors]))}u[i]=s}}return e},args:["comparator","options"],multi:!0}},cast:{set:{from:Array.isArray,to:(e,t)=>new Set(e)}},rebuild(e){e.$_terms._inclusions=[],e.$_terms._exclusions=[],e.$_terms._requireds=[];for(const t of e.$_terms.items)c.validateSingle(t,e),"required"===t._flags.presence?e.$_terms._requireds.push(t):"forbidden"===t._flags.presence?e.$_terms._exclusions.push(t):e.$_terms._inclusions.push(t);for(const t of e.$_terms.ordered)c.validateSingle(t,e)},manifest:{build:(e,t)=>(t.items&&(e=e.items(...t.items)),t.ordered&&(e=e.ordered(...t.ordered)),e)},messages:{"array.base":"{{#label}} must be an array","array.excludes":"{{#label}} contains an excluded value","array.hasKnown":"{{#label}} does not contain at least one required match for type {:#patternLabel}","array.hasUnknown":"{{#label}} does not contain at least one required match","array.includes":"{{#label}} does not match any of the allowed types","array.includesRequiredBoth":"{{#label}} does not contain {{#knownMisses}} and {{#unknownMisses}} other required value(s)","array.includesRequiredKnowns":"{{#label}} does not contain {{#knownMisses}}","array.includesRequiredUnknowns":"{{#label}} does not contain {{#unknownMisses}} required value(s)","array.length":"{{#label}} must contain {{#limit}} items","array.max":"{{#label}} must contain less than or equal to {{#limit}} items","array.min":"{{#label}} must contain at least {{#limit}} items","array.orderedLength":"{{#label}} must contain at most {{#limit}} items","array.sort":"{{#label}} must be sorted in {#order} order by {{#by}}","array.sort.mismatching":"{{#label}} cannot be sorted due to mismatching types","array.sort.unsupported":"{{#label}} cannot be sorted due to unsupported type {#type}","array.sparse":"{{#label}} must not be a sparse array item","array.unique":"{{#label}} contains a duplicate value"}}),c.fillMissedErrors=function(e,t,r,n,s,o){const i=[];let a=0;for(const e of r){const t=e._flags.label;t?i.push(t):++a}i.length?a?t.push(e.$_createError("array.includesRequiredBoth",n,{knownMisses:i,unknownMisses:a},s,o)):t.push(e.$_createError("array.includesRequiredKnowns",n,{knownMisses:i},s,o)):t.push(e.$_createError("array.includesRequiredUnknowns",n,{unknownMisses:a},s,o))},c.fillOrderedErrors=function(e,t,r,n,s,o){const i=[];for(const e of r)"required"===e._flags.presence&&i.push(e);i.length&&c.fillMissedErrors(e,t,i,n,s,o)},c.fillDefault=function(e,t,r,n){const s=[];let o=!0;for(let i=e.length-1;i>=0;--i){const a=e[i],u=[t,...r.ancestors],c=a.$_validate(void 0,r.localize(r.path,u,a),n).value;if(o){if(void 0===c)continue;o=!1}s.unshift(c)}s.length&&t.push(...s)},c.fastSplice=function(e,t){let r=t;for(;r<e.length;)e[r++]=e[r];--e.length},c.validateSingle=function(e,t){("array"===e.type||e._flags._arrayItems)&&(n(!t._flags.single,"Cannot specify array item with single rule enabled"),t.$_setFlag("_arrayItems",!0,{clone:!1}))},c.sort=function(e,t,r,n,s){const o="ascending"===r.order?1:-1,i=-1*o,a=o,u=(u,l)=>{let f=c.compare(u,l,i,a);if(null!==f)return f;if(r.by&&(u=r.by.resolve(u,n,s),l=r.by.resolve(l,n,s)),f=c.compare(u,l,i,a),null!==f)return f;const p=typeof u;if(p!==typeof l)throw e.$_createError("array.sort.mismatching",t,null,n,s);if("number"!==p&&"string"!==p)throw e.$_createError("array.sort.unsupported",t,{type:p},n,s);return"number"===p?(u-l)*o:u<l?i:a};try{return{value:t.slice().sort(u)}}catch(e){return{errors:e}}},c.compare=function(e,t,r,n){return e===t?0:void 0===e?1:void 0===t?-1:null===e?n:null===t?r:null}},4937:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),o=r(8160),i=r(2036),a={isBool:function(e){return"boolean"==typeof e}};e.exports=s.extend({type:"boolean",flags:{sensitive:{default:!1}},terms:{falsy:{init:null,manifest:"values"},truthy:{init:null,manifest:"values"}},coerce(e,{schema:t}){if("boolean"!=typeof e){if("string"==typeof e){const r=t._flags.sensitive?e:e.toLowerCase();e="true"===r||"false"!==r&&e}return"boolean"!=typeof e&&(e=t.$_terms.truthy&&t.$_terms.truthy.has(e,null,null,!t._flags.sensitive)||(!t.$_terms.falsy||!t.$_terms.falsy.has(e,null,null,!t._flags.sensitive))&&e),{value:e}}},validate(e,{error:t}){if("boolean"!=typeof e)return{value:e,errors:t("boolean.base")}},rules:{truthy:{method(...e){o.verifyFlat(e,"truthy");const t=this.clone();t.$_terms.truthy=t.$_terms.truthy||new i;for(let r=0;r<e.length;++r){const s=e[r];n(void 0!==s,"Cannot call truthy with undefined"),t.$_terms.truthy.add(s)}return t}},falsy:{method(...e){o.verifyFlat(e,"falsy");const t=this.clone();t.$_terms.falsy=t.$_terms.falsy||new i;for(let r=0;r<e.length;++r){const s=e[r];n(void 0!==s,"Cannot call falsy with undefined"),t.$_terms.falsy.add(s)}return t}},sensitive:{method(e=!0){return this.$_setFlag("sensitive",e)}}},cast:{number:{from:a.isBool,to:(e,t)=>e?1:0},string:{from:a.isBool,to:(e,t)=>e?"true":"false"}},manifest:{build:(e,t)=>(t.truthy&&(e=e.truthy(...t.truthy)),t.falsy&&(e=e.falsy(...t.falsy)),e)},messages:{"boolean.base":"{{#label}} must be a boolean"}})},7500:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),o=r(8160),i=r(3328),a={isDate:function(e){return e instanceof Date}};e.exports=s.extend({type:"date",coerce:{from:["number","string"],method:(e,{schema:t})=>({value:a.parse(e,t._flags.format)||e})},validate(e,{schema:t,error:r,prefs:n}){if(e instanceof Date&&!isNaN(e.getTime()))return;const s=t._flags.format;return n.convert&&s&&"string"==typeof e?{value:e,errors:r("date.format",{format:s})}:{value:e,errors:r("date.base")}},rules:{compare:{method:!1,validate(e,t,{date:r},{name:n,operator:s,args:i}){const a="now"===r?Date.now():r.getTime();return o.compare(e.getTime(),a,s)?e:t.error("date."+n,{limit:i.date,value:e})},args:[{name:"date",ref:!0,normalize:e=>"now"===e?e:a.parse(e),assert:e=>null!==e,message:"must have a valid date format"}]},format:{method(e){return n(["iso","javascript","unix"].includes(e),"Unknown date format",e),this.$_setFlag("format",e)}},greater:{method(e){return this.$_addRule({name:"greater",method:"compare",args:{date:e},operator:">"})}},iso:{method(){return this.format("iso")}},less:{method(e){return this.$_addRule({name:"less",method:"compare",args:{date:e},operator:"<"})}},max:{method(e){return this.$_addRule({name:"max",method:"compare",args:{date:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"compare",args:{date:e},operator:">="})}},timestamp:{method(e="javascript"){return n(["javascript","unix"].includes(e),'"type" must be one of "javascript, unix"'),this.format(e)}}},cast:{number:{from:a.isDate,to:(e,t)=>e.getTime()},string:{from:a.isDate,to:(e,{prefs:t})=>i.date(e,t)}},messages:{"date.base":"{{#label}} must be a valid date","date.format":'{{#label}} must be in {msg("date.format." + #format) || #format} format',"date.greater":"{{#label}} must be greater than {{:#limit}}","date.less":"{{#label}} must be less than {{:#limit}}","date.max":"{{#label}} must be less than or equal to {{:#limit}}","date.min":"{{#label}} must be greater than or equal to {{:#limit}}","date.format.iso":"ISO 8601 date","date.format.javascript":"timestamp or number of milliseconds","date.format.unix":"timestamp or number of seconds"}}),a.parse=function(e,t){if(e instanceof Date)return e;if("string"!=typeof e&&(isNaN(e)||!isFinite(e)))return null;if(/^\s*$/.test(e))return null;if("iso"===t)return o.isIsoDate(e)?a.date(e.toString()):null;const r=e;if("string"==typeof e&&/^[+-]?\d+(\.\d+)?$/.test(e)&&(e=parseFloat(e)),t){if("javascript"===t)return a.date(1*e);if("unix"===t)return a.date(1e3*e);if("string"==typeof r)return null}return a.date(e)},a.date=function(e){const t=new Date(e);return isNaN(t.getTime())?null:t}},390:(e,t,r)=>{"use strict";const n=r(375),s=r(7824);e.exports=s.extend({type:"function",properties:{typeof:"function"},rules:{arity:{method(e){return n(Number.isSafeInteger(e)&&e>=0,"n must be a positive integer"),this.$_addRule({name:"arity",args:{n:e}})},validate:(e,t,{n:r})=>e.length===r?e:t.error("function.arity",{n:r})},class:{method(){return this.$_addRule("class")},validate:(e,t)=>/^\s*class\s/.test(e.toString())?e:t.error("function.class",{value:e})},minArity:{method(e){return n(Number.isSafeInteger(e)&&e>0,"n must be a strict positive integer"),this.$_addRule({name:"minArity",args:{n:e}})},validate:(e,t,{n:r})=>e.length>=r?e:t.error("function.minArity",{n:r})},maxArity:{method(e){return n(Number.isSafeInteger(e)&&e>=0,"n must be a positive integer"),this.$_addRule({name:"maxArity",args:{n:e}})},validate:(e,t,{n:r})=>e.length<=r?e:t.error("function.maxArity",{n:r})}},messages:{"function.arity":"{{#label}} must have an arity of {{#n}}","function.class":"{{#label}} must be a class","function.maxArity":"{{#label}} must have an arity lesser or equal to {{#n}}","function.minArity":"{{#label}} must have an arity greater or equal to {{#n}}"}})},7824:(e,t,r)=>{"use strict";const n=r(978),s=r(375),o=r(8571),i=r(3652),a=r(8068),u=r(8160),c=r(3292),l=r(6354),f=r(6133),p=r(3328),m={renameDefaults:{alias:!1,multiple:!1,override:!1}};e.exports=a.extend({type:"_keys",properties:{typeof:"object"},flags:{unknown:{default:void 0}},terms:{dependencies:{init:null},keys:{init:null,manifest:{mapped:{from:"schema",to:"key"}}},patterns:{init:null},renames:{init:null}},args:(e,t)=>e.keys(t),validate(e,{schema:t,error:r,state:n,prefs:s}){if(!e||typeof e!==t.$_property("typeof")||Array.isArray(e))return{value:e,errors:r("object.base",{type:t.$_property("typeof")})};if(!(t.$_terms.renames||t.$_terms.dependencies||t.$_terms.keys||t.$_terms.patterns||t.$_terms.externals))return;e=m.clone(e,s);const o=[];if(t.$_terms.renames&&!m.rename(t,e,n,s,o))return{value:e,errors:o};if(!t.$_terms.keys&&!t.$_terms.patterns&&!t.$_terms.dependencies)return{value:e,errors:o};const i=new Set(Object.keys(e));if(t.$_terms.keys){const r=[e,...n.ancestors];for(const a of t.$_terms.keys){const t=a.key,u=e[t];i.delete(t);const c=n.localize([...n.path,t],r,a),l=a.schema.$_validate(u,c,s);if(l.errors){if(s.abortEarly)return{value:e,errors:l.errors};void 0!==l.value&&(e[t]=l.value),o.push(...l.errors)}else"strip"===a.schema._flags.result||void 0===l.value&&void 0!==u?delete e[t]:void 0!==l.value&&(e[t]=l.value)}}if(i.size||t._flags._hasPatternMatch){const r=m.unknown(t,e,i,o,n,s);if(r)return r}if(t.$_terms.dependencies)for(const r of t.$_terms.dependencies){if(null!==r.key&&!1===m.isPresent(r.options)(r.key.resolve(e,n,s,null,{shadow:!1})))continue;const i=m.dependencies[r.rel](t,r,e,n,s);if(i){const r=t.$_createError(i.code,e,i.context,n,s);if(s.abortEarly)return{value:e,errors:r};o.push(r)}}return{value:e,errors:o}},rules:{and:{method(...e){return u.verifyFlat(e,"and"),m.dependency(this,"and",null,e)}},append:{method(e){return null==e||0===Object.keys(e).length?this:this.keys(e)}},assert:{method(e,t,r){p.isTemplate(e)||(e=c.ref(e)),s(void 0===r||"string"==typeof r,"Message must be a string"),t=this.$_compile(t,{appendPath:!0});const n=this.$_addRule({name:"assert",args:{subject:e,schema:t,message:r}});return n.$_mutateRegister(e),n.$_mutateRegister(t),n},validate(e,{error:t,prefs:r,state:n},{subject:s,schema:o,message:i}){const a=s.resolve(e,n,r),u=f.isRef(s)?s.absolute(n):[];return o.$_match(a,n.localize(u,[e,...n.ancestors],o),r)?e:t("object.assert",{subject:s,message:i})},args:["subject","schema","message"],multi:!0},instance:{method(e,t){return s("function"==typeof e,"constructor must be a function"),t=t||e.name,this.$_addRule({name:"instance",args:{constructor:e,name:t}})},validate:(e,t,{constructor:r,name:n})=>e instanceof r?e:t.error("object.instance",{type:n,value:e}),args:["constructor","name"]},keys:{method(e){s(void 0===e||"object"==typeof e,"Object schema must be a valid object"),s(!u.isSchema(e),"Object schema cannot be a joi schema");const t=this.clone();if(e)if(Object.keys(e).length){t.$_terms.keys=t.$_terms.keys?t.$_terms.keys.filter((t=>!e.hasOwnProperty(t.key))):new m.Keys;for(const r in e)u.tryWithPath((()=>t.$_terms.keys.push({key:r,schema:this.$_compile(e[r])})),r)}else t.$_terms.keys=new m.Keys;else t.$_terms.keys=null;return t.$_mutateRebuild()}},length:{method(e){return this.$_addRule({name:"length",args:{limit:e},operator:"="})},validate:(e,t,{limit:r},{name:n,operator:s,args:o})=>u.compare(Object.keys(e).length,r,s)?e:t.error("object."+n,{limit:o.limit,value:e}),args:[{name:"limit",ref:!0,assert:u.limit,message:"must be a positive integer"}]},max:{method(e){return this.$_addRule({name:"max",method:"length",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"length",args:{limit:e},operator:">="})}},nand:{method(...e){return u.verifyFlat(e,"nand"),m.dependency(this,"nand",null,e)}},or:{method(...e){return u.verifyFlat(e,"or"),m.dependency(this,"or",null,e)}},oxor:{method(...e){return m.dependency(this,"oxor",null,e)}},pattern:{method(e,t,r={}){const n=e instanceof RegExp;n||(e=this.$_compile(e,{appendPath:!0})),s(void 0!==t,"Invalid rule"),u.assertOptions(r,["fallthrough","matches"]),n&&s(!e.flags.includes("g")&&!e.flags.includes("y"),"pattern should not use global or sticky mode"),t=this.$_compile(t,{appendPath:!0});const o=this.clone();o.$_terms.patterns=o.$_terms.patterns||[];const i={[n?"regex":"schema"]:e,rule:t};return r.matches&&(i.matches=this.$_compile(r.matches),"array"!==i.matches.type&&(i.matches=i.matches.$_root.array().items(i.matches)),o.$_mutateRegister(i.matches),o.$_setFlag("_hasPatternMatch",!0,{clone:!1})),r.fallthrough&&(i.fallthrough=!0),o.$_terms.patterns.push(i),o.$_mutateRegister(t),o}},ref:{method(){return this.$_addRule("ref")},validate:(e,t)=>f.isRef(e)?e:t.error("object.refType",{value:e})},regex:{method(){return this.$_addRule("regex")},validate:(e,t)=>e instanceof RegExp?e:t.error("object.regex",{value:e})},rename:{method(e,t,r={}){s("string"==typeof e||e instanceof RegExp,"Rename missing the from argument"),s("string"==typeof t||t instanceof p,"Invalid rename to argument"),s(t!==e,"Cannot rename key to same name:",e),u.assertOptions(r,["alias","ignoreUndefined","override","multiple"]);const o=this.clone();o.$_terms.renames=o.$_terms.renames||[];for(const t of o.$_terms.renames)s(t.from!==e,"Cannot rename the same key multiple times");return t instanceof p&&o.$_mutateRegister(t),o.$_terms.renames.push({from:e,to:t,options:n(m.renameDefaults,r)}),o}},schema:{method(e="any"){return this.$_addRule({name:"schema",args:{type:e}})},validate:(e,t,{type:r})=>!u.isSchema(e)||"any"!==r&&e.type!==r?t.error("object.schema",{type:r}):e},unknown:{method(e){return this.$_setFlag("unknown",!1!==e)}},with:{method(e,t,r={}){return m.dependency(this,"with",e,t,r)}},without:{method(e,t,r={}){return m.dependency(this,"without",e,t,r)}},xor:{method(...e){return u.verifyFlat(e,"xor"),m.dependency(this,"xor",null,e)}}},overrides:{default(e,t){return void 0===e&&(e=u.symbols.deepDefault),this.$_parent("default",e,t)}},rebuild(e){if(e.$_terms.keys){const t=new i.Sorter;for(const r of e.$_terms.keys)u.tryWithPath((()=>t.add(r,{after:r.schema.$_rootReferences(),group:r.key})),r.key);e.$_terms.keys=new m.Keys(...t.nodes)}},manifest:{build(e,t){if(t.keys&&(e=e.keys(t.keys)),t.dependencies)for(const{rel:r,key:n=null,peers:s,options:o}of t.dependencies)e=m.dependency(e,r,n,s,o);if(t.patterns)for(const{regex:r,schema:n,rule:s,fallthrough:o,matches:i}of t.patterns)e=e.pattern(r||n,s,{fallthrough:o,matches:i});if(t.renames)for(const{from:r,to:n,options:s}of t.renames)e=e.rename(r,n,s);return e}},messages:{"object.and":"{{#label}} contains {{#presentWithLabels}} without its required peers {{#missingWithLabels}}","object.assert":'{{#label}} is invalid because {if(#subject.key, `"` + #subject.key + `" failed to ` + (#message || "pass the assertion test"), #message || "the assertion failed")}',"object.base":"{{#label}} must be of type {{#type}}","object.instance":"{{#label}} must be an instance of {{:#type}}","object.length":'{{#label}} must have {{#limit}} key{if(#limit == 1, "", "s")}',"object.max":'{{#label}} must have less than or equal to {{#limit}} key{if(#limit == 1, "", "s")}',"object.min":'{{#label}} must have at least {{#limit}} key{if(#limit == 1, "", "s")}',"object.missing":"{{#label}} must contain at least one of {{#peersWithLabels}}","object.nand":"{{:#mainWithLabel}} must not exist simultaneously with {{#peersWithLabels}}","object.oxor":"{{#label}} contains a conflict between optional exclusive peers {{#peersWithLabels}}","object.pattern.match":"{{#label}} keys failed to match pattern requirements","object.refType":"{{#label}} must be a Joi reference","object.regex":"{{#label}} must be a RegExp object","object.rename.multiple":"{{#label}} cannot rename {{:#from}} because multiple renames are disabled and another key was already renamed to {{:#to}}","object.rename.override":"{{#label}} cannot rename {{:#from}} because override is disabled and target {{:#to}} exists","object.schema":"{{#label}} must be a Joi schema of {{#type}} type","object.unknown":"{{#label}} is not allowed","object.with":"{{:#mainWithLabel}} missing required peer {{:#peerWithLabel}}","object.without":"{{:#mainWithLabel}} conflict with forbidden peer {{:#peerWithLabel}}","object.xor":"{{#label}} contains a conflict between exclusive peers {{#peersWithLabels}}"}}),m.clone=function(e,t){if("object"==typeof e){if(t.nonEnumerables)return o(e,{shallow:!0});const r=Object.create(Object.getPrototypeOf(e));return Object.assign(r,e),r}const r=function(...t){return e.apply(this,t)};return r.prototype=o(e.prototype),Object.defineProperty(r,"name",{value:e.name,writable:!1}),Object.defineProperty(r,"length",{value:e.length,writable:!1}),Object.assign(r,e),r},m.dependency=function(e,t,r,n,o){s(null===r||"string"==typeof r,t,"key must be a strings"),o||(o=n.length>1&&"object"==typeof n[n.length-1]?n.pop():{}),u.assertOptions(o,["separator","isPresent"]),n=[].concat(n);const i=u.default(o.separator,"."),a=[];for(const e of n)s("string"==typeof e,t,"peers must be strings"),a.push(c.ref(e,{separator:i,ancestor:0,prefix:!1}));null!==r&&(r=c.ref(r,{separator:i,ancestor:0,prefix:!1}));const l=e.clone();return l.$_terms.dependencies=l.$_terms.dependencies||[],l.$_terms.dependencies.push(new m.Dependency(t,r,a,n,o)),l},m.dependencies={and(e,t,r,n,s){const o=[],i=[],a=t.peers.length,u=m.isPresent(t.options);for(const e of t.peers)!1===u(e.resolve(r,n,s,null,{shadow:!1}))?o.push(e.key):i.push(e.key);if(o.length!==a&&i.length!==a)return{code:"object.and",context:{present:i,presentWithLabels:m.keysToLabels(e,i),missing:o,missingWithLabels:m.keysToLabels(e,o)}}},nand(e,t,r,n,s){const o=[],i=m.isPresent(t.options);for(const e of t.peers)i(e.resolve(r,n,s,null,{shadow:!1}))&&o.push(e.key);if(o.length!==t.peers.length)return;const a=t.paths[0],u=t.paths.slice(1);return{code:"object.nand",context:{main:a,mainWithLabel:m.keysToLabels(e,a),peers:u,peersWithLabels:m.keysToLabels(e,u)}}},or(e,t,r,n,s){const o=m.isPresent(t.options);for(const e of t.peers)if(o(e.resolve(r,n,s,null,{shadow:!1})))return;return{code:"object.missing",context:{peers:t.paths,peersWithLabels:m.keysToLabels(e,t.paths)}}},oxor(e,t,r,n,s){const o=[],i=m.isPresent(t.options);for(const e of t.peers)i(e.resolve(r,n,s,null,{shadow:!1}))&&o.push(e.key);if(!o.length||1===o.length)return;const a={peers:t.paths,peersWithLabels:m.keysToLabels(e,t.paths)};return a.present=o,a.presentWithLabels=m.keysToLabels(e,o),{code:"object.oxor",context:a}},with(e,t,r,n,s){const o=m.isPresent(t.options);for(const i of t.peers)if(!1===o(i.resolve(r,n,s,null,{shadow:!1})))return{code:"object.with",context:{main:t.key.key,mainWithLabel:m.keysToLabels(e,t.key.key),peer:i.key,peerWithLabel:m.keysToLabels(e,i.key)}}},without(e,t,r,n,s){const o=m.isPresent(t.options);for(const i of t.peers)if(o(i.resolve(r,n,s,null,{shadow:!1})))return{code:"object.without",context:{main:t.key.key,mainWithLabel:m.keysToLabels(e,t.key.key),peer:i.key,peerWithLabel:m.keysToLabels(e,i.key)}}},xor(e,t,r,n,s){const o=[],i=m.isPresent(t.options);for(const e of t.peers)i(e.resolve(r,n,s,null,{shadow:!1}))&&o.push(e.key);if(1===o.length)return;const a={peers:t.paths,peersWithLabels:m.keysToLabels(e,t.paths)};return 0===o.length?{code:"object.missing",context:a}:(a.present=o,a.presentWithLabels=m.keysToLabels(e,o),{code:"object.xor",context:a})}},m.keysToLabels=function(e,t){return Array.isArray(t)?t.map((t=>e.$_mapLabels(t))):e.$_mapLabels(t)},m.isPresent=function(e){return"function"==typeof e.isPresent?e.isPresent:e=>void 0!==e},m.rename=function(e,t,r,n,s){const o={};for(const i of e.$_terms.renames){const a=[],u="string"!=typeof i.from;if(u)for(const e in t){if(void 0===t[e]&&i.options.ignoreUndefined)continue;if(e===i.to)continue;const r=i.from.exec(e);r&&a.push({from:e,to:i.to,match:r})}else!Object.prototype.hasOwnProperty.call(t,i.from)||void 0===t[i.from]&&i.options.ignoreUndefined||a.push(i);for(const c of a){const a=c.from;let l=c.to;if(l instanceof p&&(l=l.render(t,r,n,c.match)),a!==l){if(!i.options.multiple&&o[l]&&(s.push(e.$_createError("object.rename.multiple",t,{from:a,to:l,pattern:u},r,n)),n.abortEarly))return!1;if(Object.prototype.hasOwnProperty.call(t,l)&&!i.options.override&&!o[l]&&(s.push(e.$_createError("object.rename.override",t,{from:a,to:l,pattern:u},r,n)),n.abortEarly))return!1;void 0===t[a]?delete t[l]:t[l]=t[a],o[l]=!0,i.options.alias||delete t[a]}}}return!0},m.unknown=function(e,t,r,n,s,o){if(e.$_terms.patterns){let i=!1;const a=e.$_terms.patterns.map((e=>{if(e.matches)return i=!0,[]})),u=[t,...s.ancestors];for(const i of r){const c=t[i],l=[...s.path,i];for(let f=0;f<e.$_terms.patterns.length;++f){const p=e.$_terms.patterns[f];if(p.regex){const e=p.regex.test(i);if(s.mainstay.tracer.debug(s,"rule",`pattern.${f}`,e?"pass":"error"),!e)continue}else if(!p.schema.$_match(i,s.nest(p.schema,`pattern.${f}`),o))continue;r.delete(i);const m=s.localize(l,u,{schema:p.rule,key:i}),d=p.rule.$_validate(c,m,o);if(d.errors){if(o.abortEarly)return{value:t,errors:d.errors};n.push(...d.errors)}if(p.matches&&a[f].push(i),t[i]=d.value,!p.fallthrough)break}}if(i)for(let r=0;r<a.length;++r){const i=a[r];if(!i)continue;const c=e.$_terms.patterns[r].matches,f=s.localize(s.path,u,c),p=c.$_validate(i,f,o);if(p.errors){const r=l.details(p.errors,{override:!1});r.matches=i;const a=e.$_createError("object.pattern.match",t,r,s,o);if(o.abortEarly)return{value:t,errors:a};n.push(a)}}}if(r.size&&(e.$_terms.keys||e.$_terms.patterns)){if(o.stripUnknown&&void 0===e._flags.unknown||o.skipFunctions){const e=!(!o.stripUnknown||!0!==o.stripUnknown&&!o.stripUnknown.objects);for(const n of r)e?(delete t[n],r.delete(n)):"function"==typeof t[n]&&r.delete(n)}if(!u.default(e._flags.unknown,o.allowUnknown))for(const i of r){const r=s.localize([...s.path,i],[]),a=e.$_createError("object.unknown",t[i],{child:i},r,o,{flags:!1});if(o.abortEarly)return{value:t,errors:a};n.push(a)}}},m.Dependency=class{constructor(e,t,r,n,s){this.rel=e,this.key=t,this.peers=r,this.paths=n,this.options=s}describe(){const e={rel:this.rel,peers:this.paths};return null!==this.key&&(e.key=this.key.key),"."!==this.peers[0].separator&&(e.options={...e.options,separator:this.peers[0].separator}),this.options.isPresent&&(e.options={...e.options,isPresent:this.options.isPresent}),e}},m.Keys=class extends Array{concat(e){const t=this.slice(),r=new Map;for(let e=0;e<t.length;++e)r.set(t[e].key,e);for(const n of e){const e=n.key,s=r.get(e);void 0!==s?t[s]={key:e,schema:t[s].schema.concat(n.schema)}:t.push(n)}return t}}},8785:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),o=r(8160),i=r(3292),a=r(6354),u={};e.exports=s.extend({type:"link",properties:{schemaChain:!0},terms:{link:{init:null,manifest:"single",register:!1}},args:(e,t)=>e.ref(t),validate(e,{schema:t,state:r,prefs:s}){n(t.$_terms.link,"Uninitialized link schema");const o=u.generate(t,e,r,s),i=t.$_terms.link[0].ref;return o.$_validate(e,r.nest(o,`link:${i.display}:${o.type}`),s)},generate:(e,t,r,n)=>u.generate(e,t,r,n),rules:{ref:{method(e){n(!this.$_terms.link,"Cannot reinitialize schema"),e=i.ref(e),n("value"===e.type||"local"===e.type,"Invalid reference type:",e.type),n("local"===e.type||"root"===e.ancestor||e.ancestor>0,"Link cannot reference itself");const t=this.clone();return t.$_terms.link=[{ref:e}],t}},relative:{method(e=!0){return this.$_setFlag("relative",e)}}},overrides:{concat(e){n(this.$_terms.link,"Uninitialized link schema"),n(o.isSchema(e),"Invalid schema object"),n("link"!==e.type,"Cannot merge type link with another link");const t=this.clone();return t.$_terms.whens||(t.$_terms.whens=[]),t.$_terms.whens.push({concat:e}),t.$_mutateRebuild()}},manifest:{build:(e,t)=>(n(t.link,"Invalid link description missing link"),e.ref(t.link))}}),u.generate=function(e,t,r,n){let s=r.mainstay.links.get(e);if(s)return s._generate(t,r,n).schema;const o=e.$_terms.link[0].ref,{perspective:i,path:a}=u.perspective(o,r);u.assert(i,"which is outside of schema boundaries",o,e,r,n);try{s=a.length?i.$_reach(a):i}catch(t){u.assert(!1,"to non-existing schema",o,e,r,n)}return u.assert("link"!==s.type,"which is another link",o,e,r,n),e._flags.relative||r.mainstay.links.set(e,s),s._generate(t,r,n).schema},u.perspective=function(e,t){if("local"===e.type){for(const{schema:r,key:n}of t.schemas){if((r._flags.id||n)===e.path[0])return{perspective:r,path:e.path.slice(1)};if(r.$_terms.shared)for(const t of r.$_terms.shared)if(t._flags.id===e.path[0])return{perspective:t,path:e.path.slice(1)}}return{perspective:null,path:null}}return"root"===e.ancestor?{perspective:t.schemas[t.schemas.length-1].schema,path:e.path}:{perspective:t.schemas[e.ancestor]&&t.schemas[e.ancestor].schema,path:e.path}},u.assert=function(e,t,r,s,o,i){e||n(!1,`"${a.label(s._flags,o,i)}" contains link reference "${r.display}" ${t}`)}},3832:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),o=r(8160),i={numberRx:/^\s*[+-]?(?:(?:\d+(?:\.\d*)?)|(?:\.\d+))(?:e([+-]?\d+))?\s*$/i,precisionRx:/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/,exponentialPartRegex:/[eE][+-]?\d+$/,leadingSignAndZerosRegex:/^[+-]?(0*)?/,dotRegex:/\./,trailingZerosRegex:/0+$/,decimalPlaces(e){const t=e.toString(),r=t.indexOf("."),n=t.indexOf("e");return(r<0?0:(n<0?t.length:n)-r-1)+(n<0?0:Math.max(0,-parseInt(t.slice(n+1))))}};e.exports=s.extend({type:"number",flags:{unsafe:{default:!1}},coerce:{from:"string",method(e,{schema:t,error:r}){if(!e.match(i.numberRx))return;e=e.trim();const n={value:parseFloat(e)};if(0===n.value&&(n.value=0),!t._flags.unsafe)if(e.match(/e/i)){if(i.extractSignificantDigits(e)!==i.extractSignificantDigits(String(n.value)))return n.errors=r("number.unsafe"),n}else{const t=n.value.toString();if(t.match(/e/i))return n;if(t!==i.normalizeDecimal(e))return n.errors=r("number.unsafe"),n}return n}},validate(e,{schema:t,error:r,prefs:n}){if(e===1/0||e===-1/0)return{value:e,errors:r("number.infinity")};if(!o.isNumber(e))return{value:e,errors:r("number.base")};const s={value:e};if(n.convert){const e=t.$_getRule("precision");if(e){const t=Math.pow(10,e.args.limit);s.value=Math.round(s.value*t)/t}}return 0===s.value&&(s.value=0),!t._flags.unsafe&&(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&(s.errors=r("number.unsafe")),s},rules:{compare:{method:!1,validate:(e,t,{limit:r},{name:n,operator:s,args:i})=>o.compare(e,r,s)?e:t.error("number."+n,{limit:i.limit,value:e}),args:[{name:"limit",ref:!0,assert:o.isNumber,message:"must be a number"}]},greater:{method(e){return this.$_addRule({name:"greater",method:"compare",args:{limit:e},operator:">"})}},integer:{method(){return this.$_addRule("integer")},validate:(e,t)=>Math.trunc(e)-e==0?e:t.error("number.integer")},less:{method(e){return this.$_addRule({name:"less",method:"compare",args:{limit:e},operator:"<"})}},max:{method(e){return this.$_addRule({name:"max",method:"compare",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"compare",args:{limit:e},operator:">="})}},multiple:{method(e){const t="number"==typeof e?i.decimalPlaces(e):null,r=Math.pow(10,t);return this.$_addRule({name:"multiple",args:{base:e,baseDecimalPlace:t,pfactor:r}})},validate:(e,t,{base:r,baseDecimalPlace:n,pfactor:s},o)=>i.decimalPlaces(e)>n?t.error("number.multiple",{multiple:o.args.base,value:e}):Math.round(s*e)%Math.round(s*r)==0?e:t.error("number.multiple",{multiple:o.args.base,value:e}),args:[{name:"base",ref:!0,assert:e=>"number"==typeof e&&isFinite(e)&&e>0,message:"must be a positive number"},"baseDecimalPlace","pfactor"],multi:!0},negative:{method(){return this.sign("negative")}},port:{method(){return this.$_addRule("port")},validate:(e,t)=>Number.isSafeInteger(e)&&e>=0&&e<=65535?e:t.error("number.port")},positive:{method(){return this.sign("positive")}},precision:{method(e){return n(Number.isSafeInteger(e),"limit must be an integer"),this.$_addRule({name:"precision",args:{limit:e}})},validate(e,t,{limit:r}){const n=e.toString().match(i.precisionRx);return Math.max((n[1]?n[1].length:0)-(n[2]?parseInt(n[2],10):0),0)<=r?e:t.error("number.precision",{limit:r,value:e})},convert:!0},sign:{method(e){return n(["negative","positive"].includes(e),"Invalid sign",e),this.$_addRule({name:"sign",args:{sign:e}})},validate:(e,t,{sign:r})=>"negative"===r&&e<0||"positive"===r&&e>0?e:t.error(`number.${r}`)},unsafe:{method(e=!0){return n("boolean"==typeof e,"enabled must be a boolean"),this.$_setFlag("unsafe",e)}}},cast:{string:{from:e=>"number"==typeof e,to:(e,t)=>e.toString()}},messages:{"number.base":"{{#label}} must be a number","number.greater":"{{#label}} must be greater than {{#limit}}","number.infinity":"{{#label}} cannot be infinity","number.integer":"{{#label}} must be an integer","number.less":"{{#label}} must be less than {{#limit}}","number.max":"{{#label}} must be less than or equal to {{#limit}}","number.min":"{{#label}} must be greater than or equal to {{#limit}}","number.multiple":"{{#label}} must be a multiple of {{#multiple}}","number.negative":"{{#label}} must be a negative number","number.port":"{{#label}} must be a valid port","number.positive":"{{#label}} must be a positive number","number.precision":"{{#label}} must have no more than {{#limit}} decimal places","number.unsafe":"{{#label}} must be a safe number"}}),i.extractSignificantDigits=function(e){return e.replace(i.exponentialPartRegex,"").replace(i.dotRegex,"").replace(i.trailingZerosRegex,"").replace(i.leadingSignAndZerosRegex,"")},i.normalizeDecimal=function(e){return(e=e.replace(/^\+/,"").replace(/\.0*$/,"").replace(/^(-?)\.([^\.]*)$/,"$10.$2").replace(/^(-?)0+([0-9])/,"$1$2")).includes(".")&&e.endsWith("0")&&(e=e.replace(/0+$/,"")),"-0"===e?"0":e}},8966:(e,t,r)=>{"use strict";const n=r(7824);e.exports=n.extend({type:"object",cast:{map:{from:e=>e&&"object"==typeof e,to:(e,t)=>new Map(Object.entries(e))}}})},7417:(e,t,r)=>{"use strict";const n=r(375),s=r(5380),o=r(1745),i=r(9959),a=r(6064),u=r(9926),c=r(5752),l=r(8068),f=r(8160),p={tlds:u instanceof Set&&{tlds:{allow:u,deny:null}},base64Regex:{true:{true:/^(?:[\w\-]{2}[\w\-]{2})*(?:[\w\-]{2}==|[\w\-]{3}=)?$/,false:/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/},false:{true:/^(?:[\w\-]{2}[\w\-]{2})*(?:[\w\-]{2}(==)?|[\w\-]{3}=?)?$/,false:/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}(==)?|[A-Za-z0-9+\/]{3}=?)?$/}},dataUriRegex:/^data:[\w+.-]+\/[\w+.-]+;((charset=[\w-]+|base64),)?(.*)$/,hexRegex:{withPrefix:/^0x[0-9a-f]+$/i,withOptionalPrefix:/^(?:0x)?[0-9a-f]+$/i,withoutPrefix:/^[0-9a-f]+$/i},ipRegex:i.regex({cidr:"forbidden"}).regex,isoDurationRegex:/^P(?!$)(\d+Y)?(\d+M)?(\d+W)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?$/,guidBrackets:{"{":"}","[":"]","(":")","":""},guidVersions:{uuidv1:"1",uuidv2:"2",uuidv3:"3",uuidv4:"4",uuidv5:"5",uuidv6:"6",uuidv7:"7",uuidv8:"8"},guidSeparators:new Set([void 0,!0,!1,"-",":"]),normalizationForms:["NFC","NFD","NFKC","NFKD"]};e.exports=l.extend({type:"string",flags:{insensitive:{default:!1},truncate:{default:!1}},terms:{replacements:{init:null}},coerce:{from:"string",method(e,{schema:t,state:r,prefs:n}){const s=t.$_getRule("normalize");s&&(e=e.normalize(s.args.form));const o=t.$_getRule("case");o&&(e="upper"===o.args.direction?e.toLocaleUpperCase():e.toLocaleLowerCase());const i=t.$_getRule("trim");if(i&&i.args.enabled&&(e=e.trim()),t.$_terms.replacements)for(const r of t.$_terms.replacements)e=e.replace(r.pattern,r.replacement);const a=t.$_getRule("hex");if(a&&a.args.options.byteAligned&&e.length%2!=0&&(e=`0${e}`),t.$_getRule("isoDate")){const t=p.isoDate(e);t&&(e=t)}if(t._flags.truncate){const s=t.$_getRule("max");if(s){let o=s.args.limit;if(f.isResolvable(o)&&(o=o.resolve(e,r,n),!f.limit(o)))return{value:e,errors:t.$_createError("any.ref",o,{ref:s.args.limit,arg:"limit",reason:"must be a positive integer"},r,n)};e=e.slice(0,o)}}return{value:e}}},validate(e,{schema:t,error:r}){if("string"!=typeof e)return{value:e,errors:r("string.base")};if(""===e){const n=t.$_getRule("min");if(n&&0===n.args.limit)return;return{value:e,errors:r("string.empty")}}},rules:{alphanum:{method(){return this.$_addRule("alphanum")},validate:(e,t)=>/^[a-zA-Z0-9]+$/.test(e)?e:t.error("string.alphanum")},base64:{method(e={}){return f.assertOptions(e,["paddingRequired","urlSafe"]),e={urlSafe:!1,paddingRequired:!0,...e},n("boolean"==typeof e.paddingRequired,"paddingRequired must be boolean"),n("boolean"==typeof e.urlSafe,"urlSafe must be boolean"),this.$_addRule({name:"base64",args:{options:e}})},validate:(e,t,{options:r})=>p.base64Regex[r.paddingRequired][r.urlSafe].test(e)?e:t.error("string.base64")},case:{method(e){return n(["lower","upper"].includes(e),"Invalid case:",e),this.$_addRule({name:"case",args:{direction:e}})},validate:(e,t,{direction:r})=>"lower"===r&&e===e.toLocaleLowerCase()||"upper"===r&&e===e.toLocaleUpperCase()?e:t.error(`string.${r}case`),convert:!0},creditCard:{method(){return this.$_addRule("creditCard")},validate(e,t){let r=e.length,n=0,s=1;for(;r--;){const t=e.charAt(r)*s;n+=t-9*(t>9),s^=3}return n>0&&n%10==0?e:t.error("string.creditCard")}},dataUri:{method(e={}){return f.assertOptions(e,["paddingRequired"]),e={paddingRequired:!0,...e},n("boolean"==typeof e.paddingRequired,"paddingRequired must be boolean"),this.$_addRule({name:"dataUri",args:{options:e}})},validate(e,t,{options:r}){const n=e.match(p.dataUriRegex);if(n){if(!n[2])return e;if("base64"!==n[2])return e;if(p.base64Regex[r.paddingRequired].false.test(n[3]))return e}return t.error("string.dataUri")}},domain:{method(e){e&&f.assertOptions(e,["allowFullyQualified","allowUnicode","maxDomainSegments","minDomainSegments","tlds"]);const t=p.addressOptions(e);return this.$_addRule({name:"domain",args:{options:e},address:t})},validate:(e,t,r,{address:n})=>s.isValid(e,n)?e:t.error("string.domain")},email:{method(e={}){f.assertOptions(e,["allowFullyQualified","allowUnicode","ignoreLength","maxDomainSegments","minDomainSegments","multiple","separator","tlds"]),n(void 0===e.multiple||"boolean"==typeof e.multiple,"multiple option must be an boolean");const t=p.addressOptions(e),r=new RegExp(`\\s*[${e.separator?a(e.separator):","}]\\s*`);return this.$_addRule({name:"email",args:{options:e},regex:r,address:t})},validate(e,t,{options:r},{regex:n,address:s}){const i=r.multiple?e.split(n):[e],a=[];for(const e of i)o.isValid(e,s)||a.push(e);return a.length?t.error("string.email",{value:e,invalids:a}):e}},guid:{alias:"uuid",method(e={}){f.assertOptions(e,["version","separator"]);let t="";if(e.version){const r=[].concat(e.version);n(r.length>=1,"version must have at least 1 valid version specified");const s=new Set;for(let e=0;e<r.length;++e){const o=r[e];n("string"==typeof o,"version at position "+e+" must be a string");const i=p.guidVersions[o.toLowerCase()];n(i,"version at position "+e+" must be one of "+Object.keys(p.guidVersions).join(", ")),n(!s.has(i),"version at position "+e+" must not be a duplicate"),t+=i,s.add(i)}}n(p.guidSeparators.has(e.separator),'separator must be one of true, false, "-", or ":"');const r=void 0===e.separator?"[:-]?":!0===e.separator?"[:-]":!1===e.separator?"[]?":`\\${e.separator}`,s=new RegExp(`^([\\[{\\(]?)[0-9A-F]{8}(${r})[0-9A-F]{4}\\2?[${t||"0-9A-F"}][0-9A-F]{3}\\2?[${t?"89AB":"0-9A-F"}][0-9A-F]{3}\\2?[0-9A-F]{12}([\\]}\\)]?)$`,"i");return this.$_addRule({name:"guid",args:{options:e},regex:s})},validate(e,t,r,{regex:n}){const s=n.exec(e);return s?p.guidBrackets[s[1]]!==s[s.length-1]?t.error("string.guid"):e:t.error("string.guid")}},hex:{method(e={}){return f.assertOptions(e,["byteAligned","prefix"]),e={byteAligned:!1,prefix:!1,...e},n("boolean"==typeof e.byteAligned,"byteAligned must be boolean"),n("boolean"==typeof e.prefix||"optional"===e.prefix,'prefix must be boolean or "optional"'),this.$_addRule({name:"hex",args:{options:e}})},validate:(e,t,{options:r})=>("optional"===r.prefix?p.hexRegex.withOptionalPrefix:!0===r.prefix?p.hexRegex.withPrefix:p.hexRegex.withoutPrefix).test(e)?r.byteAligned&&e.length%2!=0?t.error("string.hexAlign"):e:t.error("string.hex")},hostname:{method(){return this.$_addRule("hostname")},validate:(e,t)=>s.isValid(e,{minDomainSegments:1})||p.ipRegex.test(e)?e:t.error("string.hostname")},insensitive:{method(){return this.$_setFlag("insensitive",!0)}},ip:{method(e={}){f.assertOptions(e,["cidr","version"]);const{cidr:t,versions:r,regex:n}=i.regex(e),s=e.version?r:void 0;return this.$_addRule({name:"ip",args:{options:{cidr:t,version:s}},regex:n})},validate:(e,t,{options:r},{regex:n})=>n.test(e)?e:r.version?t.error("string.ipVersion",{value:e,cidr:r.cidr,version:r.version}):t.error("string.ip",{value:e,cidr:r.cidr})},isoDate:{method(){return this.$_addRule("isoDate")},validate:(e,{error:t})=>p.isoDate(e)?e:t("string.isoDate")},isoDuration:{method(){return this.$_addRule("isoDuration")},validate:(e,t)=>p.isoDurationRegex.test(e)?e:t.error("string.isoDuration")},length:{method(e,t){return p.length(this,"length",e,"=",t)},validate(e,t,{limit:r,encoding:n},{name:s,operator:o,args:i}){const a=!n&&e.length;return f.compare(a,r,o)?e:t.error("string."+s,{limit:i.limit,value:e,encoding:n})},args:[{name:"limit",ref:!0,assert:f.limit,message:"must be a positive integer"},"encoding"]},lowercase:{method(){return this.case("lower")}},max:{method(e,t){return p.length(this,"max",e,"<=",t)},args:["limit","encoding"]},min:{method(e,t){return p.length(this,"min",e,">=",t)},args:["limit","encoding"]},normalize:{method(e="NFC"){return n(p.normalizationForms.includes(e),"normalization form must be one of "+p.normalizationForms.join(", ")),this.$_addRule({name:"normalize",args:{form:e}})},validate:(e,{error:t},{form:r})=>e===e.normalize(r)?e:t("string.normalize",{value:e,form:r}),convert:!0},pattern:{alias:"regex",method(e,t={}){n(e instanceof RegExp,"regex must be a RegExp"),n(!e.flags.includes("g")&&!e.flags.includes("y"),"regex should not use global or sticky mode"),"string"==typeof t&&(t={name:t}),f.assertOptions(t,["invert","name"]);const r=["string.pattern",t.invert?".invert":"",t.name?".name":".base"].join("");return this.$_addRule({name:"pattern",args:{regex:e,options:t},errorCode:r})},validate:(e,t,{regex:r,options:n},{errorCode:s})=>r.test(e)^n.invert?e:t.error(s,{name:n.name,regex:r,value:e}),args:["regex","options"],multi:!0},replace:{method(e,t){"string"==typeof e&&(e=new RegExp(a(e),"g")),n(e instanceof RegExp,"pattern must be a RegExp"),n("string"==typeof t,"replacement must be a String");const r=this.clone();return r.$_terms.replacements||(r.$_terms.replacements=[]),r.$_terms.replacements.push({pattern:e,replacement:t}),r}},token:{method(){return this.$_addRule("token")},validate:(e,t)=>/^\w+$/.test(e)?e:t.error("string.token")},trim:{method(e=!0){return n("boolean"==typeof e,"enabled must be a boolean"),this.$_addRule({name:"trim",args:{enabled:e}})},validate:(e,t,{enabled:r})=>r&&e!==e.trim()?t.error("string.trim"):e,convert:!0},truncate:{method(e=!0){return n("boolean"==typeof e,"enabled must be a boolean"),this.$_setFlag("truncate",e)}},uppercase:{method(){return this.case("upper")}},uri:{method(e={}){f.assertOptions(e,["allowRelative","allowQuerySquareBrackets","domain","relativeOnly","scheme","encodeUri"]),e.domain&&f.assertOptions(e.domain,["allowFullyQualified","allowUnicode","maxDomainSegments","minDomainSegments","tlds"]);const{regex:t,scheme:r}=c.regex(e),n=e.domain?p.addressOptions(e.domain):null;return this.$_addRule({name:"uri",args:{options:e},regex:t,domain:n,scheme:r})},validate(e,t,{options:r},{regex:n,domain:o,scheme:i}){if(["http:/","https:/"].includes(e))return t.error("string.uri");let a=n.exec(e);if(!a&&t.prefs.convert&&r.encodeUri){const t=encodeURI(e);a=n.exec(t),a&&(e=t)}if(a){const n=a[1]||a[2];return!o||r.allowRelative&&!n||s.isValid(n,o)?e:t.error("string.domain",{value:n})}return r.relativeOnly?t.error("string.uriRelativeOnly"):r.scheme?t.error("string.uriCustomScheme",{scheme:i,value:e}):t.error("string.uri")}}},manifest:{build(e,t){if(t.replacements)for(const{pattern:r,replacement:n}of t.replacements)e=e.replace(r,n);return e}},messages:{"string.alphanum":"{{#label}} must only contain alpha-numeric characters","string.base":"{{#label}} must be a string","string.base64":"{{#label}} must be a valid base64 string","string.creditCard":"{{#label}} must be a credit card","string.dataUri":"{{#label}} must be a valid dataUri string","string.domain":"{{#label}} must contain a valid domain name","string.email":"{{#label}} must be a valid email","string.empty":"{{#label}} is not allowed to be empty","string.guid":"{{#label}} must be a valid GUID","string.hex":"{{#label}} must only contain hexadecimal characters","string.hexAlign":"{{#label}} hex decoded representation must be byte aligned","string.hostname":"{{#label}} must be a valid hostname","string.ip":"{{#label}} must be a valid ip address with a {{#cidr}} CIDR","string.ipVersion":"{{#label}} must be a valid ip address of one of the following versions {{#version}} with a {{#cidr}} CIDR","string.isoDate":"{{#label}} must be in iso format","string.isoDuration":"{{#label}} must be a valid ISO 8601 duration","string.length":"{{#label}} length must be {{#limit}} characters long","string.lowercase":"{{#label}} must only contain lowercase characters","string.max":"{{#label}} length must be less than or equal to {{#limit}} characters long","string.min":"{{#label}} length must be at least {{#limit}} characters long","string.normalize":"{{#label}} must be unicode normalized in the {{#form}} form","string.token":"{{#label}} must only contain alpha-numeric and underscore characters","string.pattern.base":"{{#label}} with value {:[.]} fails to match the required pattern: {{#regex}}","string.pattern.name":"{{#label}} with value {:[.]} fails to match the {{#name}} pattern","string.pattern.invert.base":"{{#label}} with value {:[.]} matches the inverted pattern: {{#regex}}","string.pattern.invert.name":"{{#label}} with value {:[.]} matches the inverted {{#name}} pattern","string.trim":"{{#label}} must not have leading or trailing whitespace","string.uri":"{{#label}} must be a valid uri","string.uriCustomScheme":"{{#label}} must be a valid uri with a scheme matching the {{#scheme}} pattern","string.uriRelativeOnly":"{{#label}} must be a valid relative uri","string.uppercase":"{{#label}} must only contain uppercase characters"}}),p.addressOptions=function(e){if(!e)return p.tlds||e;if(n(void 0===e.minDomainSegments||Number.isSafeInteger(e.minDomainSegments)&&e.minDomainSegments>0,"minDomainSegments must be a positive integer"),n(void 0===e.maxDomainSegments||Number.isSafeInteger(e.maxDomainSegments)&&e.maxDomainSegments>0,"maxDomainSegments must be a positive integer"),!1===e.tlds)return e;if(!0===e.tlds||void 0===e.tlds)return n(p.tlds,"Built-in TLD list disabled"),Object.assign({},e,p.tlds);n("object"==typeof e.tlds,"tlds must be true, false, or an object");const t=e.tlds.deny;if(t)return Array.isArray(t)&&(e=Object.assign({},e,{tlds:{deny:new Set(t)}})),n(e.tlds.deny instanceof Set,"tlds.deny must be an array, Set, or boolean"),n(!e.tlds.allow,"Cannot specify both tlds.allow and tlds.deny lists"),p.validateTlds(e.tlds.deny,"tlds.deny"),e;const r=e.tlds.allow;return r?!0===r?(n(p.tlds,"Built-in TLD list disabled"),Object.assign({},e,p.tlds)):(Array.isArray(r)&&(e=Object.assign({},e,{tlds:{allow:new Set(r)}})),n(e.tlds.allow instanceof Set,"tlds.allow must be an array, Set, or boolean"),p.validateTlds(e.tlds.allow,"tlds.allow"),e):e},p.validateTlds=function(e,t){for(const r of e)n(s.isValid(r,{minDomainSegments:1,maxDomainSegments:1}),`${t} must contain valid top level domain names`)},p.isoDate=function(e){if(!f.isIsoDate(e))return null;/.*T.*[+-]\d\d$/.test(e)&&(e+="00");const t=new Date(e);return isNaN(t.getTime())?null:t.toISOString()},p.length=function(e,t,r,s,o){return n(!o||!1,"Invalid encoding:",o),e.$_addRule({name:t,method:"length",args:{limit:r,encoding:o},operator:s})}},8826:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),o={};o.Map=class extends Map{slice(){return new o.Map(this)}},e.exports=s.extend({type:"symbol",terms:{map:{init:new o.Map}},coerce:{method(e,{schema:t,error:r}){const n=t.$_terms.map.get(e);return n&&(e=n),t._flags.only&&"symbol"!=typeof e?{value:e,errors:r("symbol.map",{map:t.$_terms.map})}:{value:e}}},validate(e,{error:t}){if("symbol"!=typeof e)return{value:e,errors:t("symbol.base")}},rules:{map:{method(e){e&&!e[Symbol.iterator]&&"object"==typeof e&&(e=Object.entries(e)),n(e&&e[Symbol.iterator],"Iterable must be an iterable or object");const t=this.clone(),r=[];for(const s of e){n(s&&s[Symbol.iterator],"Entry must be an iterable");const[e,o]=s;n("object"!=typeof e&&"function"!=typeof e&&"symbol"!=typeof e,"Key must not be of type object, function, or Symbol"),n("symbol"==typeof o,"Value must be a Symbol"),t.$_terms.map.set(e,o),r.push(o)}return t.valid(...r)}}},manifest:{build:(e,t)=>(t.map&&(e=e.map(t.map)),e)},messages:{"symbol.base":"{{#label}} must be a symbol","symbol.map":"{{#label}} must be one of {{#map}}"}})},8863:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(738),i=r(9621),a=r(8160),u=r(6354),c=r(493),l={result:Symbol("result")};t.entry=function(e,t,r){let s=a.defaults;r&&(n(void 0===r.warnings,"Cannot override warnings preference in synchronous validation"),n(void 0===r.artifacts,"Cannot override artifacts preference in synchronous validation"),s=a.preferences(a.defaults,r));const o=l.entry(e,t,s);n(!o.mainstay.externals.length,"Schema with external rules must use validateAsync()");const i={value:o.value};return o.error&&(i.error=o.error),o.mainstay.warnings.length&&(i.warning=u.details(o.mainstay.warnings)),o.mainstay.debug&&(i.debug=o.mainstay.debug),o.mainstay.artifacts&&(i.artifacts=o.mainstay.artifacts),i},t.entryAsync=async function(e,t,r){let n=a.defaults;r&&(n=a.preferences(a.defaults,r));const s=l.entry(e,t,n),o=s.mainstay;if(s.error)throw o.debug&&(s.error.debug=o.debug),s.error;if(o.externals.length){let t=s.value;const c=[];for(const s of o.externals){const f=s.state.path,p="link"===s.schema.type?o.links.get(s.schema):null;let m,d,h=t;const g=f.length?[t]:[],y=f.length?i(e,f):e;if(f.length){m=f[f.length-1];let e=t;for(const t of f.slice(0,-1))e=e[t],g.unshift(e);d=g[0],h=d[m]}try{const e=(e,t)=>(p||s.schema).$_createError(e,h,t,s.state,n),i=await s.method(h,{schema:s.schema,linked:p,state:s.state,prefs:r,original:y,error:e,errorsArray:l.errorsArray,warn:(e,t)=>o.warnings.push((p||s.schema).$_createError(e,h,t,s.state,n)),message:(e,t)=>(p||s.schema).$_createError("external",h,t,s.state,n,{messages:e})});if(void 0===i||i===h)continue;if(i instanceof u.Report){if(o.tracer.log(s.schema,s.state,"rule","external","error"),c.push(i),n.abortEarly)break;continue}if(Array.isArray(i)&&i[a.symbols.errors]){if(o.tracer.log(s.schema,s.state,"rule","external","error"),c.push(...i),n.abortEarly)break;continue}d?(o.tracer.value(s.state,"rule",h,i,"external"),d[m]=i):(o.tracer.value(s.state,"rule",t,i,"external"),t=i)}catch(e){throw n.errors.label&&(e.message+=` (${s.label})`),e}}if(s.value=t,c.length)throw s.error=u.process(c,e,n),o.debug&&(s.error.debug=o.debug),s.error}if(!n.warnings&&!n.debug&&!n.artifacts)return s.value;const c={value:s.value};return o.warnings.length&&(c.warning=u.details(o.warnings)),o.debug&&(c.debug=o.debug),o.artifacts&&(c.artifacts=o.artifacts),c},l.Mainstay=class{constructor(e,t,r){this.externals=[],this.warnings=[],this.tracer=e,this.debug=t,this.links=r,this.shadow=null,this.artifacts=null,this._snapshots=[]}snapshot(){this._snapshots.push({externals:this.externals.slice(),warnings:this.warnings.slice()})}restore(){const e=this._snapshots.pop();this.externals=e.externals,this.warnings=e.warnings}commit(){this._snapshots.pop()}},l.entry=function(e,r,n){const{tracer:s,cleanup:o}=l.tracer(r,n),i=n.debug?[]:null,a=r._ids._schemaChain?new Map:null,f=new l.Mainstay(s,i,a),p=r._ids._schemaChain?[{schema:r}]:null,m=new c([],[],{mainstay:f,schemas:p}),d=t.validate(e,r,m,n);o&&r.$_root.untrace();const h=u.process(d.errors,e,n);return{value:d.value,error:h,mainstay:f}},l.tracer=function(e,t){return e.$_root._tracer?{tracer:e.$_root._tracer._register(e)}:t.debug?(n(e.$_root.trace,"Debug mode not supported"),{tracer:e.$_root.trace()._register(e),cleanup:!0}):{tracer:l.ignore}},t.validate=function(e,t,r,n,s={}){if(t.$_terms.whens&&(t=t._generate(e,r,n).schema),t._preferences&&(n=l.prefs(t,n)),t._cache&&n.cache){const n=t._cache.get(e);if(r.mainstay.tracer.debug(r,"validate","cached",!!n),n)return n}const o=(s,o,i)=>t.$_createError(s,e,o,i||r,n),i={original:e,prefs:n,schema:t,state:r,error:o,errorsArray:l.errorsArray,warn:(e,t,n)=>r.mainstay.warnings.push(o(e,t,n)),message:(s,o)=>t.$_createError("custom",e,o,r,n,{messages:s})};r.mainstay.tracer.entry(t,r);const u=t._definition;if(u.prepare&&void 0!==e&&n.convert){const t=u.prepare(e,i);if(t){if(r.mainstay.tracer.value(r,"prepare",e,t.value),t.errors)return l.finalize(t.value,[].concat(t.errors),i);e=t.value}}if(u.coerce&&void 0!==e&&n.convert&&(!u.coerce.from||u.coerce.from.includes(typeof e))){const t=u.coerce.method(e,i);if(t){if(r.mainstay.tracer.value(r,"coerced",e,t.value),t.errors)return l.finalize(t.value,[].concat(t.errors),i);e=t.value}}const c=t._flags.empty;c&&c.$_match(l.trim(e,t),r.nest(c),a.defaults)&&(r.mainstay.tracer.value(r,"empty",e,void 0),e=void 0);const f=s.presence||t._flags.presence||(t._flags._endedSwitch?null:n.presence);if(void 0===e){if("forbidden"===f)return l.finalize(e,null,i);if("required"===f)return l.finalize(e,[t.$_createError("any.required",e,null,r,n)],i);if("optional"===f){if(t._flags.default!==a.symbols.deepDefault)return l.finalize(e,null,i);r.mainstay.tracer.value(r,"default",e,{}),e={}}}else if("forbidden"===f)return l.finalize(e,[t.$_createError("any.unknown",e,null,r,n)],i);const p=[];if(t._valids){const s=t._valids.get(e,r,n,t._flags.insensitive);if(s)return n.convert&&(r.mainstay.tracer.value(r,"valids",e,s.value),e=s.value),r.mainstay.tracer.filter(t,r,"valid",s),l.finalize(e,null,i);if(t._flags.only){const s=t.$_createError("any.only",e,{valids:t._valids.values({display:!0})},r,n);if(n.abortEarly)return l.finalize(e,[s],i);p.push(s)}}if(t._invalids){const s=t._invalids.get(e,r,n,t._flags.insensitive);if(s){r.mainstay.tracer.filter(t,r,"invalid",s);const o=t.$_createError("any.invalid",e,{invalids:t._invalids.values({display:!0})},r,n);if(n.abortEarly)return l.finalize(e,[o],i);p.push(o)}}if(u.validate){const t=u.validate(e,i);if(t&&(r.mainstay.tracer.value(r,"base",e,t.value),e=t.value,t.errors)){if(!Array.isArray(t.errors))return p.push(t.errors),l.finalize(e,p,i);if(t.errors.length)return p.push(...t.errors),l.finalize(e,p,i)}}return t._rules.length?l.rules(e,p,i):l.finalize(e,p,i)},l.rules=function(e,t,r){const{schema:n,state:s,prefs:o}=r;for(const i of n._rules){const u=n._definition.rules[i.method];if(u.convert&&o.convert){s.mainstay.tracer.log(n,s,"rule",i.name,"full");continue}let c,f=i.args;if(i._resolve.length){f=Object.assign({},f);for(const t of i._resolve){const r=u.argsByName.get(t),i=f[t].resolve(e,s,o),l=r.normalize?r.normalize(i):i,p=a.validateArg(l,null,r);if(p){c=n.$_createError("any.ref",i,{arg:t,ref:f[t],reason:p},s,o);break}f[t]=l}}c=c||u.validate(e,r,f,i);const p=l.rule(c,i);if(p.errors){if(s.mainstay.tracer.log(n,s,"rule",i.name,"error"),i.warn){s.mainstay.warnings.push(...p.errors);continue}if(o.abortEarly)return l.finalize(e,p.errors,r);t.push(...p.errors)}else s.mainstay.tracer.log(n,s,"rule",i.name,"pass"),s.mainstay.tracer.value(s,"rule",e,p.value,i.name),e=p.value}return l.finalize(e,t,r)},l.rule=function(e,t){return e instanceof u.Report?(l.error(e,t),{errors:[e],value:null}):Array.isArray(e)&&e[a.symbols.errors]?(e.forEach((e=>l.error(e,t))),{errors:e,value:null}):{errors:null,value:e}},l.error=function(e,t){return t.message&&e._setTemplate(t.message),e},l.finalize=function(e,t,r){t=t||[];const{schema:s,state:o,prefs:i}=r;if(t.length){const n=l.default("failover",void 0,t,r);void 0!==n&&(o.mainstay.tracer.value(o,"failover",e,n),e=n,t=[])}if(t.length&&s._flags.error)if("function"==typeof s._flags.error){t=s._flags.error(t),Array.isArray(t)||(t=[t]);for(const e of t)n(e instanceof Error||e instanceof u.Report,"error() must return an Error object")}else t=[s._flags.error];if(void 0===e){const n=l.default("default",e,t,r);o.mainstay.tracer.value(o,"default",e,n),e=n}if(s._flags.cast&&void 0!==e){const t=s._definition.cast[s._flags.cast];if(t.from(e)){const n=t.to(e,r);o.mainstay.tracer.value(o,"cast",e,n,s._flags.cast),e=n}}if(s.$_terms.externals&&i.externals&&!1!==i._externals)for(const{method:e}of s.$_terms.externals)o.mainstay.externals.push({method:e,schema:s,state:o,label:u.label(s._flags,o,i)});const a={value:e,errors:t.length?t:null};return s._flags.result&&(a.value="strip"===s._flags.result?void 0:r.original,o.mainstay.tracer.value(o,s._flags.result,e,a.value),o.shadow(e,s._flags.result)),s._cache&&!1!==i.cache&&!s._refs.length&&s._cache.set(r.original,a),void 0===e||a.errors||void 0===s._flags.artifact||(o.mainstay.artifacts=o.mainstay.artifacts||new Map,o.mainstay.artifacts.has(s._flags.artifact)||o.mainstay.artifacts.set(s._flags.artifact,[]),o.mainstay.artifacts.get(s._flags.artifact).push(o.path)),a},l.prefs=function(e,t){const r=t===a.defaults;return r&&e._preferences[a.symbols.prefs]?e._preferences[a.symbols.prefs]:(t=a.preferences(t,e._preferences),r&&(e._preferences[a.symbols.prefs]=t),t)},l.default=function(e,t,r,n){const{schema:o,state:i,prefs:u}=n,c=o._flags[e];if(u.noDefaults||void 0===c)return t;if(i.mainstay.tracer.log(o,i,"rule",e,"full"),!c)return c;if("function"==typeof c){const a=c.length?[s(i.ancestors[0]),n]:[];try{return c(...a)}catch(t){return void r.push(o.$_createError(`any.${e}`,null,{error:t},i,u))}}return"object"!=typeof c?c:c[a.symbols.literal]?c.literal:a.isResolvable(c)?c.resolve(t,i,u):s(c)},l.trim=function(e,t){if("string"!=typeof e)return e;const r=t.$_getRule("trim");return r&&r.args.enabled?e.trim():e},l.ignore={active:!1,debug:o,entry:o,filter:o,log:o,resolve:o,value:o},l.errorsArray=function(){const e=[];return e[a.symbols.errors]=!0,e}},2036:(e,t,r)=>{"use strict";const n=r(375),s=r(9474),o=r(8160),i={};e.exports=i.Values=class{constructor(e,t){this._values=new Set(e),this._refs=new Set(t),this._lowercase=i.lowercases(e),this._override=!1}get length(){return this._values.size+this._refs.size}add(e,t){o.isResolvable(e)?this._refs.has(e)||(this._refs.add(e),t&&t.register(e)):this.has(e,null,null,!1)||(this._values.add(e),"string"==typeof e&&this._lowercase.set(e.toLowerCase(),e))}static merge(e,t,r){if(e=e||new i.Values,t){if(t._override)return t.clone();for(const r of[...t._values,...t._refs])e.add(r)}if(r)for(const t of[...r._values,...r._refs])e.remove(t);return e.length?e:null}remove(e){o.isResolvable(e)?this._refs.delete(e):(this._values.delete(e),"string"==typeof e&&this._lowercase.delete(e.toLowerCase()))}has(e,t,r,n){return!!this.get(e,t,r,n)}get(e,t,r,n){if(!this.length)return!1;if(this._values.has(e))return{value:e};if("string"==typeof e&&e&&n){const t=this._lowercase.get(e.toLowerCase());if(t)return{value:t}}if(!this._refs.size&&"object"!=typeof e)return!1;if("object"==typeof e)for(const t of this._values)if(s(t,e))return{value:t};if(t)for(const o of this._refs){const i=o.resolve(e,t,r,null,{in:!0});if(void 0===i)continue;const a=o.in&&"object"==typeof i?Array.isArray(i)?i:Object.keys(i):[i];for(const t of a)if(typeof t==typeof e)if(n&&e&&"string"==typeof e){if(t.toLowerCase()===e.toLowerCase())return{value:t,ref:o}}else if(s(t,e))return{value:t,ref:o}}return!1}override(){this._override=!0}values(e){if(e&&e.display){const e=[];for(const t of[...this._values,...this._refs])void 0!==t&&e.push(t);return e}return Array.from([...this._values,...this._refs])}clone(){const e=new i.Values(this._values,this._refs);return e._override=this._override,e}concat(e){n(!e._override,"Cannot concat override set of values");const t=new i.Values([...this._values,...e._values],[...this._refs,...e._refs]);return t._override=this._override,t}describe(){const e=[];this._override&&e.push({override:!0});for(const t of this._values.values())e.push(t&&"object"==typeof t?{value:t}:t);for(const t of this._refs.values())e.push(t.describe());return e}},i.Values.prototype[o.symbols.values]=!0,i.Values.prototype.slice=i.Values.prototype.clone,i.lowercases=function(e){const t=new Map;if(e)for(const r of e)"string"==typeof r&&t.set(r.toLowerCase(),r);return t}},978:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(1687),i=r(9621),a={};e.exports=function(e,t,r={}){if(n(e&&"object"==typeof e,"Invalid defaults value: must be an object"),n(!t||!0===t||"object"==typeof t,"Invalid source value: must be true, falsy or an object"),n("object"==typeof r,"Invalid options: must be an object"),!t)return null;if(r.shallow)return a.applyToDefaultsWithShallow(e,t,r);const i=s(e);if(!0===t)return i;const u=void 0!==r.nullOverride&&r.nullOverride;return o(i,t,{nullOverride:u,mergeArrays:!1})},a.applyToDefaultsWithShallow=function(e,t,r){const u=r.shallow;n(Array.isArray(u),"Invalid keys");const c=new Map,l=!0===t?null:new Set;for(let r of u){r=Array.isArray(r)?r:r.split(".");const n=i(e,r);n&&"object"==typeof n?c.set(n,l&&i(t,r)||n):l&&l.add(r)}const f=s(e,{},c);if(!l)return f;for(const e of l)a.reachCopy(f,t,e);const p=void 0!==r.nullOverride&&r.nullOverride;return o(f,t,{nullOverride:p,mergeArrays:!1})},a.reachCopy=function(e,t,r){for(const e of r){if(!(e in t))return;const r=t[e];if("object"!=typeof r||null===r)return;t=r}const n=t;let s=e;for(let e=0;e<r.length-1;++e){const t=r[e];"object"!=typeof s[t]&&(s[t]={}),s=s[t]}s[r[r.length-1]]=n}},375:(e,t,r)=>{"use strict";const n=r(7916);e.exports=function(e,...t){if(!e){if(1===t.length&&t[0]instanceof Error)throw t[0];throw new n(t)}}},8571:(e,t,r)=>{"use strict";const n=r(9621),s=r(4277),o=r(7043),i={needsProtoHack:new Set([s.set,s.map,s.weakSet,s.weakMap])};e.exports=i.clone=function(e,t={},r=null){if("object"!=typeof e||null===e)return e;let n=i.clone,a=r;if(t.shallow){if(!0!==t.shallow)return i.cloneWithShallow(e,t);n=e=>e}else if(a){const t=a.get(e);if(t)return t}else a=new Map;const u=s.getInternalProto(e);if(u===s.buffer)return!1;if(u===s.date)return new Date(e.getTime());if(u===s.regex)return new RegExp(e);const c=i.base(e,u,t);if(c===e)return e;if(a&&a.set(e,c),u===s.set)for(const r of e)c.add(n(r,t,a));else if(u===s.map)for(const[r,s]of e)c.set(r,n(s,t,a));const l=o.keys(e,t);for(const r of l){if("__proto__"===r)continue;if(u===s.array&&"length"===r){c.length=e.length;continue}const o=Object.getOwnPropertyDescriptor(e,r);o?o.get||o.set?Object.defineProperty(c,r,o):o.enumerable?c[r]=n(e[r],t,a):Object.defineProperty(c,r,{enumerable:!1,writable:!0,configurable:!0,value:n(e[r],t,a)}):Object.defineProperty(c,r,{enumerable:!0,writable:!0,configurable:!0,value:n(e[r],t,a)})}return c},i.cloneWithShallow=function(e,t){const r=t.shallow;(t=Object.assign({},t)).shallow=!1;const s=new Map;for(const t of r){const r=n(e,t);"object"!=typeof r&&"function"!=typeof r||s.set(r,r)}return i.clone(e,t,s)},i.base=function(e,t,r){if(!1===r.prototype)return i.needsProtoHack.has(t)?new t.constructor:t===s.array?[]:{};const n=Object.getPrototypeOf(e);if(n&&n.isImmutable)return e;if(t===s.array){const e=[];return n!==t&&Object.setPrototypeOf(e,n),e}if(i.needsProtoHack.has(t)){const e=new n.constructor;return n!==t&&Object.setPrototypeOf(e,n),e}return Object.create(n)}},9474:(e,t,r)=>{"use strict";const n=r(4277),s={mismatched:null};e.exports=function(e,t,r){return r=Object.assign({prototype:!0},r),!!s.isDeepEqual(e,t,r,[])},s.isDeepEqual=function(e,t,r,o){if(e===t)return 0!==e||1/e==1/t;const i=typeof e;if(i!==typeof t)return!1;if(null===e||null===t)return!1;if("function"===i){if(!r.deepFunction||e.toString()!==t.toString())return!1}else if("object"!==i)return e!=e&&t!=t;const a=s.getSharedType(e,t,!!r.prototype);switch(a){case n.buffer:return!1;case n.promise:return e===t;case n.regex:return e.toString()===t.toString();case s.mismatched:return!1}for(let r=o.length-1;r>=0;--r)if(o[r].isSame(e,t))return!0;o.push(new s.SeenEntry(e,t));try{return!!s.isDeepEqualObj(a,e,t,r,o)}finally{o.pop()}},s.getSharedType=function(e,t,r){if(r)return Object.getPrototypeOf(e)!==Object.getPrototypeOf(t)?s.mismatched:n.getInternalProto(e);const o=n.getInternalProto(e);return o!==n.getInternalProto(t)?s.mismatched:o},s.valueOf=function(e){const t=e.valueOf;if(void 0===t)return e;try{return t.call(e)}catch(e){return e}},s.hasOwnEnumerableProperty=function(e,t){return Object.prototype.propertyIsEnumerable.call(e,t)},s.isSetSimpleEqual=function(e,t){for(const r of Set.prototype.values.call(e))if(!Set.prototype.has.call(t,r))return!1;return!0},s.isDeepEqualObj=function(e,t,r,o,i){const{isDeepEqual:a,valueOf:u,hasOwnEnumerableProperty:c}=s,{keys:l,getOwnPropertySymbols:f}=Object;if(e===n.array){if(!o.part){if(t.length!==r.length)return!1;for(let e=0;e<t.length;++e)if(!a(t[e],r[e],o,i))return!1;return!0}for(const e of t)for(const t of r)if(a(e,t,o,i))return!0}else if(e===n.set){if(t.size!==r.size)return!1;if(!s.isSetSimpleEqual(t,r)){const e=new Set(Set.prototype.values.call(r));for(const r of Set.prototype.values.call(t)){if(e.delete(r))continue;let t=!1;for(const n of e)if(a(r,n,o,i)){e.delete(n),t=!0;break}if(!t)return!1}}}else if(e===n.map){if(t.size!==r.size)return!1;for(const[e,n]of Map.prototype.entries.call(t)){if(void 0===n&&!Map.prototype.has.call(r,e))return!1;if(!a(n,Map.prototype.get.call(r,e),o,i))return!1}}else if(e===n.error&&(t.name!==r.name||t.message!==r.message))return!1;const p=u(t),m=u(r);if((t!==p||r!==m)&&!a(p,m,o,i))return!1;const d=l(t);if(!o.part&&d.length!==l(r).length&&!o.skip)return!1;let h=0;for(const e of d)if(o.skip&&o.skip.includes(e))void 0===r[e]&&++h;else{if(!c(r,e))return!1;if(!a(t[e],r[e],o,i))return!1}if(!o.part&&d.length-h!==l(r).length)return!1;if(!1!==o.symbols){const e=f(t),n=new Set(f(r));for(const s of e){if(!o.skip||!o.skip.includes(s))if(c(t,s)){if(!c(r,s))return!1;if(!a(t[s],r[s],o,i))return!1}else if(c(r,s))return!1;n.delete(s)}for(const e of n)if(c(r,e))return!1}return!0},s.SeenEntry=class{constructor(e,t){this.obj=e,this.ref=t}isSame(e,t){return this.obj===e&&this.ref===t}}},7916:(e,t,r)=>{"use strict";const n=r(8761);e.exports=class extends Error{constructor(e){super(e.filter((e=>""!==e)).map((e=>"string"==typeof e?e:e instanceof Error?e.message:n(e))).join(" ")||"Unknown error"),"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t.assert)}}},5277:e=>{"use strict";const t={};e.exports=function(e){if(!e)return"";let r="";for(let n=0;n<e.length;++n){const s=e.charCodeAt(n);t.isSafe(s)?r+=e[n]:r+=t.escapeHtmlChar(s)}return r},t.escapeHtmlChar=function(e){return t.namedHtml.get(e)||(e>=256?"&#"+e+";":`&#x${e.toString(16).padStart(2,"0")};`)},t.isSafe=function(e){return t.safeCharCodes.has(e)},t.namedHtml=new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[34,"&quot;"],[160,"&nbsp;"],[162,"&cent;"],[163,"&pound;"],[164,"&curren;"],[169,"&copy;"],[174,"&reg;"]]),t.safeCharCodes=function(){const e=new Set;for(let t=32;t<123;++t)(t>=97||t>=65&&t<=90||t>=48&&t<=57||32===t||46===t||44===t||45===t||58===t||95===t)&&e.add(t);return e}()},6064:e=>{"use strict";e.exports=function(e){return e.replace(/[\^\$\.\*\+\-\?\=\!\:\|\\\/\(\)\[\]\{\}\,]/g,"\\$&")}},738:e=>{"use strict";e.exports=function(){}},1687:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),o=r(7043),i={};e.exports=i.merge=function(e,t,r){if(n(e&&"object"==typeof e,"Invalid target value: must be an object"),n(null==t||"object"==typeof t,"Invalid source value: must be null, undefined, or an object"),!t)return e;if(r=Object.assign({nullOverride:!0,mergeArrays:!0},r),Array.isArray(t)){n(Array.isArray(e),"Cannot merge array onto an object"),r.mergeArrays||(e.length=0);for(let n=0;n<t.length;++n)e.push(s(t[n],{symbols:r.symbols}));return e}const a=o.keys(t,r);for(let n=0;n<a.length;++n){const o=a[n];if("__proto__"===o||!Object.prototype.propertyIsEnumerable.call(t,o))continue;const u=t[o];if(u&&"object"==typeof u){if(e[o]===u)continue;!e[o]||"object"!=typeof e[o]||Array.isArray(e[o])!==Array.isArray(u)||u instanceof Date||u instanceof RegExp?e[o]=s(u,{symbols:r.symbols}):i.merge(e[o],u,r)}else(null!=u||r.nullOverride)&&(e[o]=u)}return e}},9621:(e,t,r)=>{"use strict";const n=r(375),s={};e.exports=function(e,t,r){if(!1===t||null==t)return e;"string"==typeof(r=r||{})&&(r={separator:r});const o=Array.isArray(t);n(!o||!r.separator,"Separator option is not valid for array-based chain");const i=o?t:t.split(r.separator||".");let a=e;for(let e=0;e<i.length;++e){let o=i[e];const u=r.iterables&&s.iterables(a);if(Array.isArray(a)||"set"===u){const e=Number(o);Number.isInteger(e)&&(o=e<0?a.length+e:e)}if(!a||"function"==typeof a&&!1===r.functions||!u&&void 0===a[o]){n(!r.strict||e+1===i.length,"Missing segment",o,"in reach path ",t),n("object"==typeof a||!0===r.functions||"function"!=typeof a,"Invalid segment",o,"in reach path ",t),a=r.default;break}a=u?"set"===u?[...a][o]:a.get(o):a[o]}return a},s.iterables=function(e){return e instanceof Set?"set":e instanceof Map?"map":void 0}},8761:e=>{"use strict";e.exports=function(...e){try{return JSON.stringify(...e)}catch(e){return"[Cannot display object: "+e.message+"]"}}},4277:(e,t)=>{"use strict";const r={};t=e.exports={array:Array.prototype,buffer:!1,date:Date.prototype,error:Error.prototype,generic:Object.prototype,map:Map.prototype,promise:Promise.prototype,regex:RegExp.prototype,set:Set.prototype,weakMap:WeakMap.prototype,weakSet:WeakSet.prototype},r.typeMap=new Map([["[object Error]",t.error],["[object Map]",t.map],["[object Promise]",t.promise],["[object Set]",t.set],["[object WeakMap]",t.weakMap],["[object WeakSet]",t.weakSet]]),t.getInternalProto=function(e){if(Array.isArray(e))return t.array;if(e instanceof Date)return t.date;if(e instanceof RegExp)return t.regex;if(e instanceof Error)return t.error;const n=Object.prototype.toString.call(e);return r.typeMap.get(n)||t.generic}},7043:(e,t)=>{"use strict";t.keys=function(e,t={}){return!1!==t.symbols?Reflect.ownKeys(e):Object.getOwnPropertyNames(e)}},3652:(e,t,r)=>{"use strict";const n=r(375),s={};t.Sorter=class{constructor(){this._items=[],this.nodes=[]}add(e,t){const r=[].concat((t=t||{}).before||[]),s=[].concat(t.after||[]),o=t.group||"?",i=t.sort||0;n(!r.includes(o),`Item cannot come before itself: ${o}`),n(!r.includes("?"),"Item cannot come before unassociated items"),n(!s.includes(o),`Item cannot come after itself: ${o}`),n(!s.includes("?"),"Item cannot come after unassociated items"),Array.isArray(e)||(e=[e]);for(const t of e){const e={seq:this._items.length,sort:i,before:r,after:s,group:o,node:t};this._items.push(e)}if(!t.manual){const e=this._sort();n(e,"item","?"!==o?`added into group ${o}`:"","created a dependencies error")}return this.nodes}merge(e){Array.isArray(e)||(e=[e]);for(const t of e)if(t)for(const e of t._items)this._items.push(Object.assign({},e));this._items.sort(s.mergeSort);for(let e=0;e<this._items.length;++e)this._items[e].seq=e;const t=this._sort();return n(t,"merge created a dependencies error"),this.nodes}sort(){const e=this._sort();return n(e,"sort created a dependencies error"),this.nodes}_sort(){const e={},t=Object.create(null),r=Object.create(null);for(const n of this._items){const s=n.seq,o=n.group;r[o]=r[o]||[],r[o].push(s),e[s]=n.before;for(const e of n.after)t[e]=t[e]||[],t[e].push(s)}for(const t in e){const n=[];for(const s in e[t]){const o=e[t][s];r[o]=r[o]||[],n.push(...r[o])}e[t]=n}for(const n in t)if(r[n])for(const s of r[n])e[s].push(...t[n]);const n={};for(const t in e){const r=e[t];for(const e of r)n[e]=n[e]||[],n[e].push(t)}const s={},o=[];for(let e=0;e<this._items.length;++e){let t=e;if(n[e]){t=null;for(let e=0;e<this._items.length;++e){if(!0===s[e])continue;n[e]||(n[e]=[]);const r=n[e].length;let o=0;for(let t=0;t<r;++t)s[n[e][t]]&&++o;if(o===r){t=e;break}}}null!==t&&(s[t]=!0,o.push(t))}if(o.length!==this._items.length)return!1;const i={};for(const e of this._items)i[e.seq]=e;this._items=[],this.nodes=[];for(const e of o){const t=i[e];this.nodes.push(t.node),this._items.push(t)}return!0}},s.mergeSort=(e,t)=>e.sort===t.sort?0:e.sort<t.sort?-1:1},5380:(e,t,r)=>{"use strict";const n=r(443),s=r(2178),o={minDomainSegments:2,nonAsciiRx:/[^\x00-\x7f]/,domainControlRx:/[\x00-\x20@\:\/\\#!\$&\'\(\)\*\+,;=\?]/,tldSegmentRx:/^[a-zA-Z](?:[a-zA-Z0-9\-]*[a-zA-Z0-9])?$/,domainSegmentRx:/^[a-zA-Z0-9](?:[a-zA-Z0-9\-]*[a-zA-Z0-9])?$/,URL:n.URL||URL};t.analyze=function(e,t={}){if(!e)return s.code("DOMAIN_NON_EMPTY_STRING");if("string"!=typeof e)throw new Error("Invalid input: domain must be a string");if(e.length>256)return s.code("DOMAIN_TOO_LONG");if(o.nonAsciiRx.test(e)){if(!1===t.allowUnicode)return s.code("DOMAIN_INVALID_UNICODE_CHARS");e=e.normalize("NFC")}if(o.domainControlRx.test(e))return s.code("DOMAIN_INVALID_CHARS");e=o.punycode(e),t.allowFullyQualified&&"."===e[e.length-1]&&(e=e.slice(0,-1));const r=t.minDomainSegments||o.minDomainSegments,n=e.split(".");if(n.length<r)return s.code("DOMAIN_SEGMENTS_COUNT");if(t.maxDomainSegments&&n.length>t.maxDomainSegments)return s.code("DOMAIN_SEGMENTS_COUNT_MAX");const i=t.tlds;if(i){const e=n[n.length-1].toLowerCase();if(i.deny&&i.deny.has(e)||i.allow&&!i.allow.has(e))return s.code("DOMAIN_FORBIDDEN_TLDS")}for(let e=0;e<n.length;++e){const t=n[e];if(!t.length)return s.code("DOMAIN_EMPTY_SEGMENT");if(t.length>63)return s.code("DOMAIN_LONG_SEGMENT");if(e<n.length-1){if(!o.domainSegmentRx.test(t))return s.code("DOMAIN_INVALID_CHARS")}else if(!o.tldSegmentRx.test(t))return s.code("DOMAIN_INVALID_TLDS_CHARS")}return null},t.isValid=function(e,r){return!t.analyze(e,r)},o.punycode=function(e){e.includes("%")&&(e=e.replace(/%/g,"%25"));try{return new o.URL(`http://${e}`).host}catch(t){return e}}},1745:(e,t,r)=>{"use strict";const n=r(9848),s=r(5380),o=r(2178),i={nonAsciiRx:/[^\x00-\x7f]/,encoder:new(n.TextEncoder||TextEncoder)};t.analyze=function(e,t){return i.email(e,t)},t.isValid=function(e,t){return!i.email(e,t)},i.email=function(e,t={}){if("string"!=typeof e)throw new Error("Invalid input: email must be a string");if(!e)return o.code("EMPTY_STRING");const r=!i.nonAsciiRx.test(e);if(!r){if(!1===t.allowUnicode)return o.code("FORBIDDEN_UNICODE");e=e.normalize("NFC")}const n=e.split("@");if(2!==n.length)return n.length>2?o.code("MULTIPLE_AT_CHAR"):o.code("MISSING_AT_CHAR");const[a,u]=n;if(!a)return o.code("EMPTY_LOCAL");if(!t.ignoreLength){if(e.length>254)return o.code("ADDRESS_TOO_LONG");if(i.encoder.encode(a).length>64)return o.code("LOCAL_TOO_LONG")}return i.local(a,r)||s.analyze(u,t)},i.local=function(e,t){const r=e.split(".");for(const e of r){if(!e.length)return o.code("EMPTY_LOCAL_SEGMENT");if(t){if(!i.atextRx.test(e))return o.code("INVALID_LOCAL_CHARS")}else for(const t of e){if(i.atextRx.test(t))continue;const e=i.binary(t);if(!i.atomRx.test(e))return o.code("INVALID_LOCAL_CHARS")}}},i.binary=function(e){return Array.from(i.encoder.encode(e)).map((e=>String.fromCharCode(e))).join("")},i.atextRx=/^[\w!#\$%&'\*\+\-/=\?\^`\{\|\}~]+$/,i.atomRx=new RegExp(["(?:[\\xc2-\\xdf][\\x80-\\xbf])","(?:\\xe0[\\xa0-\\xbf][\\x80-\\xbf])|(?:[\\xe1-\\xec][\\x80-\\xbf]{2})|(?:\\xed[\\x80-\\x9f][\\x80-\\xbf])|(?:[\\xee-\\xef][\\x80-\\xbf]{2})","(?:\\xf0[\\x90-\\xbf][\\x80-\\xbf]{2})|(?:[\\xf1-\\xf3][\\x80-\\xbf]{3})|(?:\\xf4[\\x80-\\x8f][\\x80-\\xbf]{2})"].join("|"))},2178:(e,t)=>{"use strict";t.codes={EMPTY_STRING:"Address must be a non-empty string",FORBIDDEN_UNICODE:"Address contains forbidden Unicode characters",MULTIPLE_AT_CHAR:"Address cannot contain more than one @ character",MISSING_AT_CHAR:"Address must contain one @ character",EMPTY_LOCAL:"Address local part cannot be empty",ADDRESS_TOO_LONG:"Address too long",LOCAL_TOO_LONG:"Address local part too long",EMPTY_LOCAL_SEGMENT:"Address local part contains empty dot-separated segment",INVALID_LOCAL_CHARS:"Address local part contains invalid character",DOMAIN_NON_EMPTY_STRING:"Domain must be a non-empty string",DOMAIN_TOO_LONG:"Domain too long",DOMAIN_INVALID_UNICODE_CHARS:"Domain contains forbidden Unicode characters",DOMAIN_INVALID_CHARS:"Domain contains invalid character",DOMAIN_INVALID_TLDS_CHARS:"Domain contains invalid tld character",DOMAIN_SEGMENTS_COUNT:"Domain lacks the minimum required number of segments",DOMAIN_SEGMENTS_COUNT_MAX:"Domain contains too many segments",DOMAIN_FORBIDDEN_TLDS:"Domain uses forbidden TLD",DOMAIN_EMPTY_SEGMENT:"Domain contains empty dot-separated segment",DOMAIN_LONG_SEGMENT:"Domain contains dot-separated segment that is too long"},t.code=function(e){return{code:e,error:t.codes[e]}}},9959:(e,t,r)=>{"use strict";const n=r(375),s=r(5752);t.regex=function(e={}){n(void 0===e.cidr||"string"==typeof e.cidr,"options.cidr must be a string");const t=e.cidr?e.cidr.toLowerCase():"optional";n(["required","optional","forbidden"].includes(t),"options.cidr must be one of required, optional, forbidden"),n(void 0===e.version||"string"==typeof e.version||Array.isArray(e.version),"options.version must be a string or an array of string");let r=e.version||["ipv4","ipv6","ipvfuture"];Array.isArray(r)||(r=[r]),n(r.length>=1,"options.version must have at least 1 version specified");for(let e=0;e<r.length;++e)n("string"==typeof r[e],"options.version must only contain strings"),r[e]=r[e].toLowerCase(),n(["ipv4","ipv6","ipvfuture"].includes(r[e]),"options.version contains unknown version "+r[e]+" - must be one of ipv4, ipv6, ipvfuture");r=Array.from(new Set(r));const o=`(?:${r.map((e=>{if("forbidden"===t)return s.ip[e];const r=`\\/${"ipv4"===e?s.ip.v4Cidr:s.ip.v6Cidr}`;return"required"===t?`${s.ip[e]}${r}`:`${s.ip[e]}(?:${r})?`})).join("|")})`,i=new RegExp(`^${o}$`);return{cidr:t,versions:r,regex:i,raw:o}}},5752:(e,t,r)=>{"use strict";const n=r(375),s=r(6064),o={generate:function(){const e={},t="\\dA-Fa-f",r="["+t+"]",n="\\w-\\.~",s="!\\$&'\\(\\)\\*\\+,;=",o="%"+t,i=n+o+s+":@",a="["+i+"]",u="(?:0{0,2}\\d|0?[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])";e.ipv4address="(?:"+u+"\\.){3}"+u;const c=r+"{1,4}",l="(?:"+c+":"+c+"|"+e.ipv4address+")",f="(?:"+c+":){6}"+l,p="::(?:"+c+":){5}"+l,m="(?:"+c+")?::(?:"+c+":){4}"+l,d="(?:(?:"+c+":){0,1}"+c+")?::(?:"+c+":){3}"+l,h="(?:(?:"+c+":){0,2}"+c+")?::(?:"+c+":){2}"+l,g="(?:(?:"+c+":){0,3}"+c+")?::"+c+":"+l,y="(?:(?:"+c+":){0,4}"+c+")?::"+l,v="(?:(?:"+c+":){0,5}"+c+")?::"+c,b="(?:(?:"+c+":){0,6}"+c+")?::";e.ipv4Cidr="(?:\\d|[1-2]\\d|3[0-2])",e.ipv6Cidr="(?:0{0,2}\\d|0?[1-9]\\d|1[01]\\d|12[0-8])",e.ipv6address="(?:"+f+"|"+p+"|"+m+"|"+d+"|"+h+"|"+g+"|"+y+"|"+v+"|"+b+")",e.ipvFuture="v"+r+"+\\.["+n+s+":]+",e.scheme="[a-zA-Z][a-zA-Z\\d+-\\.]*",e.schemeRegex=new RegExp(e.scheme);const _="["+n+o+s+":]*",w="["+n+o+s+"]{1,255}",x="(?:\\[(?:"+e.ipv6address+"|"+e.ipvFuture+")\\]|"+e.ipv4address+"|"+w+")",A="(?:"+_+"@)?"+x+"(?::\\d*)?",R="(?:"+_+"@)?("+x+")(?::\\d*)?",$=a+"*",S=a+"+",j="(?:\\/"+$+")*",O="\\/(?:"+S+j+")?",k=S+j,E="["+n+o+s+"@]+"+j,I="(?:\\/\\/\\/"+$+j+")";return e.hierPart="(?:(?:\\/\\/"+A+j+")|"+O+"|"+k+"|"+I+")",e.hierPartCapture="(?:(?:\\/\\/"+R+j+")|"+O+"|"+k+")",e.relativeRef="(?:(?:\\/\\/"+A+j+")|"+O+"|"+E+"|)",e.relativeRefCapture="(?:(?:\\/\\/"+R+j+")|"+O+"|"+E+"|)",e.query="["+i+"\\/\\?]*(?=#|$)",e.queryWithSquareBrackets="["+i+"\\[\\]\\/\\?]*(?=#|$)",e.fragment="["+i+"\\/\\?]*",e}};o.rfc3986=o.generate(),t.ip={v4Cidr:o.rfc3986.ipv4Cidr,v6Cidr:o.rfc3986.ipv6Cidr,ipv4:o.rfc3986.ipv4address,ipv6:o.rfc3986.ipv6address,ipvfuture:o.rfc3986.ipvFuture},o.createRegex=function(e){const t=o.rfc3986,r="(?:\\?"+(e.allowQuerySquareBrackets?t.queryWithSquareBrackets:t.query)+")?(?:#"+t.fragment+")?",i=e.domain?t.relativeRefCapture:t.relativeRef;if(e.relativeOnly)return o.wrap(i+r);let a="";if(e.scheme){n(e.scheme instanceof RegExp||"string"==typeof e.scheme||Array.isArray(e.scheme),"scheme must be a RegExp, String, or Array");const r=[].concat(e.scheme);n(r.length>=1,"scheme must have at least 1 scheme specified");const o=[];for(let e=0;e<r.length;++e){const i=r[e];n(i instanceof RegExp||"string"==typeof i,"scheme at position "+e+" must be a RegExp or String"),i instanceof RegExp?o.push(i.source.toString()):(n(t.schemeRegex.test(i),"scheme at position "+e+" must be a valid scheme"),o.push(s(i)))}a=o.join("|")}const u="(?:"+(a?"(?:"+a+")":t.scheme)+":"+(e.domain?t.hierPartCapture:t.hierPart)+")",c=e.allowRelative?"(?:"+u+"|"+i+")":u;return o.wrap(c+r,a)},o.wrap=function(e,t){return{raw:e=`(?=.)(?!https?:/(?:$|[^/]))(?!https?:///)(?!https?:[^/])${e}`,regex:new RegExp(`^${e}$`),scheme:t}},o.uriRegex=o.createRegex({}),t.regex=function(e={}){return e.scheme||e.allowRelative||e.relativeOnly||e.allowQuerySquareBrackets||e.domain?o.createRegex(e):o.uriRegex}},1447:(e,t)=>{"use strict";const r={operators:["!","^","*","/","%","+","-","<","<=",">",">=","==","!=","&&","||","??"],operatorCharacters:["!","^","*","/","%","+","-","<","=",">","&","|","?"],operatorsOrder:[["^"],["*","/","%"],["+","-"],["<","<=",">",">="],["==","!="],["&&"],["||","??"]],operatorsPrefix:["!","n"],literals:{'"':'"',"`":"`","'":"'","[":"]"},numberRx:/^(?:[0-9]*(\.[0-9]*)?){1}$/,tokenRx:/^[\w\$\#\.\@\:\{\}]+$/,symbol:Symbol("formula"),settings:Symbol("settings")};t.Parser=class{constructor(e,t={}){if(!t[r.settings]&&t.constants)for(const e in t.constants){const r=t.constants[e];if(null!==r&&!["boolean","number","string"].includes(typeof r))throw new Error(`Formula constant ${e} contains invalid ${typeof r} value type`)}this.settings=t[r.settings]?t:Object.assign({[r.settings]:!0,constants:{},functions:{}},t),this.single=null,this._parts=null,this._parse(e)}_parse(e){let n=[],s="",o=0,i=!1;const a=e=>{if(o)throw new Error("Formula missing closing parenthesis");const a=n.length?n[n.length-1]:null;if(i||s||e){if(a&&"reference"===a.type&&")"===e)return a.type="function",a.value=this._subFormula(s,a.value),void(s="");if(")"===e){const e=new t.Parser(s,this.settings);n.push({type:"segment",value:e})}else if(i){if("]"===i)return n.push({type:"reference",value:s}),void(s="");n.push({type:"literal",value:s})}else if(r.operatorCharacters.includes(s))a&&"operator"===a.type&&r.operators.includes(a.value+s)?a.value+=s:n.push({type:"operator",value:s});else if(s.match(r.numberRx))n.push({type:"constant",value:parseFloat(s)});else if(void 0!==this.settings.constants[s])n.push({type:"constant",value:this.settings.constants[s]});else{if(!s.match(r.tokenRx))throw new Error(`Formula contains invalid token: ${s}`);n.push({type:"reference",value:s})}s=""}};for(const t of e)i?t===i?(a(),i=!1):s+=t:o?"("===t?(s+=t,++o):")"===t?(--o,o?s+=t:a(t)):s+=t:t in r.literals?i=r.literals[t]:"("===t?(a(),++o):r.operatorCharacters.includes(t)?(a(),s=t,a()):" "!==t?s+=t:a();a(),n=n.map(((e,t)=>"operator"!==e.type||"-"!==e.value||t&&"operator"!==n[t-1].type?e:{type:"operator",value:"n"}));let u=!1;for(const e of n){if("operator"===e.type){if(r.operatorsPrefix.includes(e.value))continue;if(!u)throw new Error("Formula contains an operator in invalid position");if(!r.operators.includes(e.value))throw new Error(`Formula contains an unknown operator ${e.value}`)}else if(u)throw new Error("Formula missing expected operator");u=!u}if(!u)throw new Error("Formula contains invalid trailing operator");1===n.length&&["reference","literal","constant"].includes(n[0].type)&&(this.single={type:"reference"===n[0].type?"reference":"value",value:n[0].value}),this._parts=n.map((e=>{if("operator"===e.type)return r.operatorsPrefix.includes(e.value)?e:e.value;if("reference"!==e.type)return e.value;if(this.settings.tokenRx&&!this.settings.tokenRx.test(e.value))throw new Error(`Formula contains invalid reference ${e.value}`);return this.settings.reference?this.settings.reference(e.value):r.reference(e.value)}))}_subFormula(e,n){const s=this.settings.functions[n];if("function"!=typeof s)throw new Error(`Formula contains unknown function ${n}`);let o=[];if(e){let t="",s=0,i=!1;const a=()=>{if(!t)throw new Error(`Formula contains function ${n} with invalid arguments ${e}`);o.push(t),t=""};for(let n=0;n<e.length;++n){const o=e[n];i?(t+=o,o===i&&(i=!1)):o in r.literals&&!s?(t+=o,i=r.literals[o]):","!==o||s?(t+=o,"("===o?++s:")"===o&&--s):a()}a()}return o=o.map((e=>new t.Parser(e,this.settings))),function(e){const t=[];for(const r of o)t.push(r.evaluate(e));return s.call(e,...t)}}evaluate(e){const t=this._parts.slice();for(let n=t.length-2;n>=0;--n){const s=t[n];if(s&&"operator"===s.type){const o=t[n+1];t.splice(n+1,1);const i=r.evaluate(o,e);t[n]=r.single(s.value,i)}}return r.operatorsOrder.forEach((n=>{for(let s=1;s<t.length-1;)if(n.includes(t[s])){const n=t[s],o=r.evaluate(t[s-1],e),i=r.evaluate(t[s+1],e);t.splice(s,2);const a=r.calculate(n,o,i);t[s-1]=0===a?0:a}else s+=2})),r.evaluate(t[0],e)}},t.Parser.prototype[r.symbol]=!0,r.reference=function(e){return function(t){return t&&void 0!==t[e]?t[e]:null}},r.evaluate=function(e,t){return null===e?null:"function"==typeof e?e(t):e[r.symbol]?e.evaluate(t):e},r.single=function(e,t){if("!"===e)return!t;const r=-t;return 0===r?0:r},r.calculate=function(e,t,n){if("??"===e)return r.exists(t)?t:n;if("string"==typeof t||"string"==typeof n){if("+"===e)return(t=r.exists(t)?t:"")+(r.exists(n)?n:"")}else switch(e){case"^":return Math.pow(t,n);case"*":return t*n;case"/":return t/n;case"%":return t%n;case"+":return t+n;case"-":return t-n}switch(e){case"<":return t<n;case"<=":return t<=n;case">":return t>n;case">=":return t>=n;case"==":return t===n;case"!=":return t!==n;case"&&":return t&&n;case"||":return t||n}return null},r.exists=function(e){return null!=e}},9926:()=>{},5688:()=>{},9708:()=>{},1152:()=>{},443:()=>{},9848:()=>{},5934:e=>{"use strict";e.exports=JSON.parse('{"version":"17.13.3"}')}},t={},function r(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}(5107);var e,t},e.exports=t()},7205:e=>{e.exports='<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 24 24" style="enable-background:new 0 0 24 24;" xml:space="preserve"><g><path d="M12,16l-5.7-5.7l1.4-1.4l3.3,3.3V2.6h2v9.6l3.3-3.3l1.4,1.4L12,16z M21,15l0,3.5c0,1.4-1.1,2.5-2.5,2.5h-13 C4.1,21,3,19.9,3,18.5V15h2v3.5C5,18.8,5.2,19,5.5,19h13c0.3,0,0.5-0.2,0.5-0.5l0-3.5H21z"></path><path></path><path></path></g></svg>'},6815:function(e,t){var r,n;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,r=function(e){"use strict";if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw new Error("This script should only be loaded in a browser extension.");if(globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id)e.exports=globalThis.browser;else{const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class n extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const s=(t,r)=>(...n)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||n.length<=1&&!1!==r.singleCallbackArg?t.resolve(n[0]):t.resolve(n)},o=e=>1==e?"argument":"arguments",i=(e,t,r)=>new Proxy(t,{apply:(t,n,s)=>r.call(n,e,...s)});let a=Function.call.bind(Object.prototype.hasOwnProperty);const u=(e,t={},r={})=>{let n=Object.create(null),c={has:(t,r)=>r in e||r in n,get(c,l,f){if(l in n)return n[l];if(!(l in e))return;let p=e[l];if("function"==typeof p)if("function"==typeof t[l])p=i(e,e[l],t[l]);else if(a(r,l)){let t=((e,t)=>function(r,...n){if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${o(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${o(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((o,i)=>{if(t.fallbackToNoCallback)try{r[e](...n,s({resolve:o,reject:i},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...n),t.fallbackToNoCallback=!1,t.noCallback=!0,o()}else t.noCallback?(r[e](...n),o()):r[e](...n,s({resolve:o,reject:i},t))}))})(l,r[l]);p=i(e,e[l],t)}else p=p.bind(e);else if("object"==typeof p&&null!==p&&(a(t,l)||a(r,l)))p=u(p,t[l],r[l]);else{if(!a(r,"*"))return Object.defineProperty(n,l,{configurable:!0,enumerable:!0,get:()=>e[l],set(t){e[l]=t}}),p;p=u(p,t[l],r["*"])}return n[l]=p,p},set:(t,r,s,o)=>(r in n?n[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(n,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(n,t)},l=Object.create(e);return new Proxy(l,c)},c=e=>({addListener(t,r,...n){t.addListener(e.get(r),...n)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),l=new n((e=>"function"!=typeof e?e:function(t){const r=u(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),f=new n((e=>"function"!=typeof e?e:function(t,r,n){let s,o,i=!1,a=new Promise((e=>{s=function(t){i=!0,e(t)}}));try{o=e(t,r,s)}catch(e){o=Promise.reject(e)}const u=!0!==o&&((c=o)&&"object"==typeof c&&"function"==typeof c.then);var c;if(!0!==o&&!u&&!i)return!1;return(u?o:a).then((e=>{n(e)}),(e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",n({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error("Failed to send onMessage rejected reply",e)})),!0})),p=({reject:r,resolve:n},s)=>{e.runtime.lastError?e.runtime.lastError.message===t?n():r(new Error(e.runtime.lastError.message)):s&&s.__mozWebExtensionPolyfillReject__?r(new Error(s.message)):n(s)},m=(e,t,r,...n)=>{if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${o(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${o(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((e,t)=>{const s=p.bind(null,{resolve:e,reject:t});n.push(s),r.sendMessage(...n)}))},d={devtools:{network:{onRequestFinished:c(l)}},runtime:{onMessage:c(f),onMessageExternal:c(f),sendMessage:m.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:m.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},h={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":h},services:{"*":h},websites:{"*":h}},u(e,d,r)};e.exports=r(chrome)}},void 0===(n=r.apply(t,[e]))||(e.exports=n)},9306:(e,t,r)=>{"use strict";var n=r(4901),s=r(6823),o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(s(e)+" is not a function")}},679:(e,t,r)=>{"use strict";var n=r(1625),s=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new s("Incorrect invocation")}},8551:(e,t,r)=>{"use strict";var n=r(34),s=String,o=TypeError;e.exports=function(e){if(n(e))return e;throw new o(s(e)+" is not an object")}},9617:(e,t,r)=>{"use strict";var n=r(5397),s=r(5610),o=r(6198),i=function(e){return function(t,r,i){var a=n(t),u=o(a);if(0===u)return!e&&-1;var c,l=s(i,u);if(e&&r!=r){for(;u>l;)if((c=a[l++])!=c)return!0}else for(;u>l;l++)if((e||l in a)&&a[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:i(!0),indexOf:i(!1)}},4527:(e,t,r)=>{"use strict";var n=r(3724),s=r(4376),o=TypeError,i=Object.getOwnPropertyDescriptor,a=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(s(e)&&!i(e,"length").writable)throw new o("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},772:(e,t,r)=>{"use strict";var n=r(9565),s=r(7751),o=r(5966);e.exports=function(e,t,r,i){try{var a=o(e,"return");if(a)return s("Promise").resolve(n(a,e)).then((function(){t(r)}),(function(e){i(e)}))}catch(e){return i(e)}t(r)}},2059:(e,t,r)=>{"use strict";var n=r(9565),s=r(1103),o=r(8551),i=r(2360),a=r(6699),u=r(6279),c=r(8227),l=r(1181),f=r(7751),p=r(5966),m=r(3982),d=r(2529),h=r(9539),g=f("Promise"),y=c("toStringTag"),v="AsyncIteratorHelper",b="WrapForValidAsyncIterator",_=l.set,w=function(e){var t=!e,r=l.getterFor(e?b:v),a=function(e){var n=s((function(){return r(e)})),o=n.error,i=n.value;return o||t&&i.done?{exit:!0,value:o?g.reject(i):g.resolve(d(void 0,!0))}:{exit:!1,value:i}};return u(i(m),{next:function(){var e=a(this),t=e.value;if(e.exit)return t;var r=s((function(){return o(t.nextHandler(g))})),n=r.error,i=r.value;return n&&(t.done=!0),n?g.reject(i):g.resolve(i)},return:function(){var t=a(this),r=t.value;if(t.exit)return r;r.done=!0;var i,u,c=r.iterator,l=s((function(){if(r.inner)try{h(r.inner.iterator,"normal")}catch(e){return h(c,"throw",e)}return p(c,"return")}));return i=u=l.value,l.error?g.reject(u):void 0===i?g.resolve(d(void 0,!0)):(u=(l=s((function(){return n(i,c)}))).value,l.error?g.reject(u):e?g.resolve(u):g.resolve(u).then((function(e){return o(e),d(void 0,!0)})))}})},x=w(!0),A=w(!1);a(A,y,"Async Iterator Helper"),e.exports=function(e,t){var r=function(r,n){n?(n.iterator=r.iterator,n.next=r.next):n=r,n.type=t?b:v,n.nextHandler=e,n.counter=0,n.done=!1,_(this,n)};return r.prototype=t?x:A,r}},6639:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),o=r(8551),i=r(34),a=r(6837),u=r(7751),c=r(1767),l=r(772),f=function(e){var t=0===e,r=1===e,f=2===e,p=3===e;return function(e,m,d){o(e);var h=void 0!==m;!h&&t||s(m);var g=c(e),y=u("Promise"),v=g.iterator,b=g.next,_=0;return new y((function(e,s){var u=function(e){l(v,s,e,s)},c=function(){try{if(h)try{a(_)}catch(e){u(e)}y.resolve(o(n(b,v))).then((function(n){try{if(o(n).done)t?(d.length=_,e(d)):e(!p&&(f||void 0));else{var a=n.value;try{if(h){var g=m(a,_),b=function(n){if(r)c();else if(f)n?c():l(v,e,!1,s);else if(t)try{d[_++]=n,c()}catch(e){u(e)}else n?l(v,e,p||a,s):c()};i(g)?y.resolve(g).then(b,u):b(g)}else d[_++]=a,c()}catch(e){u(e)}}}catch(e){s(e)}}),s)}catch(e){s(e)}};c()}))}};e.exports={toArray:f(0),forEach:f(1),every:f(2),some:f(3),find:f(4)}},1750:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),o=r(8551),i=r(34),a=r(1767),u=r(2059),c=r(2529),l=r(772),f=u((function(e){var t=this,r=t.iterator,s=t.mapper;return new e((function(a,u){var f=function(e){t.done=!0,u(e)},p=function(e){l(r,f,e,f)};e.resolve(o(n(t.next,r))).then((function(r){try{if(o(r).done)t.done=!0,a(c(void 0,!0));else{var n=r.value;try{var u=s(n,t.counter++),l=function(e){a(c(e,!1))};i(u)?e.resolve(u).then(l,p):l(u)}catch(e){p(e)}}}catch(e){f(e)}}),f)}))}));e.exports=function(e){return o(this),s(e),new f(a(this),{mapper:e})}},3982:(e,t,r)=>{"use strict";var n,s,o=r(4576),i=r(7629),a=r(4901),u=r(2360),c=r(2787),l=r(6840),f=r(8227),p=r(6395),m="USE_FUNCTION_CONSTRUCTOR",d=f("asyncIterator"),h=o.AsyncIterator,g=i.AsyncIteratorPrototype;if(g)n=g;else if(a(h))n=h.prototype;else if(i[m]||o[m])try{s=c(c(c(Function("return async function*(){}()")()))),c(s)===Object.prototype&&(n=s)}catch(e){}n?p&&(n=u(n)):n={},a(n[d])||l(n,d,(function(){return this})),e.exports=n},6319:(e,t,r)=>{"use strict";var n=r(8551),s=r(9539);e.exports=function(e,t,r,o){try{return o?t(n(r)[0],r[1]):t(r)}catch(t){s(e,"throw",t)}}},2195:(e,t,r)=>{"use strict";var n=r(9504),s=n({}.toString),o=n("".slice);e.exports=function(e){return o(s(e),8,-1)}},6955:(e,t,r)=>{"use strict";var n=r(2140),s=r(4901),o=r(2195),i=r(8227)("toStringTag"),a=Object,u="Arguments"===o(function(){return arguments}());e.exports=n?o:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=a(e),i))?r:u?o(t):"Object"===(n=o(t))&&s(t.callee)?"Arguments":n}},7740:(e,t,r)=>{"use strict";var n=r(9297),s=r(5031),o=r(7347),i=r(4913);e.exports=function(e,t,r){for(var a=s(t),u=i.f,c=o.f,l=0;l<a.length;l++){var f=a[l];n(e,f)||r&&n(r,f)||u(e,f,c(t,f))}}},2211:(e,t,r)=>{"use strict";var n=r(9039);e.exports=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},2529:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},6699:(e,t,r)=>{"use strict";var n=r(3724),s=r(4913),o=r(6980);e.exports=n?function(e,t,r){return s.f(e,t,o(1,r))}:function(e,t,r){return e[t]=r,e}},6980:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4659:(e,t,r)=>{"use strict";var n=r(3724),s=r(4913),o=r(6980);e.exports=function(e,t,r){n?s.f(e,t,o(0,r)):e[t]=r}},2106:(e,t,r)=>{"use strict";var n=r(283),s=r(4913);e.exports=function(e,t,r){return r.get&&n(r.get,t,{getter:!0}),r.set&&n(r.set,t,{setter:!0}),s.f(e,t,r)}},6840:(e,t,r)=>{"use strict";var n=r(4901),s=r(4913),o=r(283),i=r(9433);e.exports=function(e,t,r,a){a||(a={});var u=a.enumerable,c=void 0!==a.name?a.name:t;if(n(r)&&o(r,c,a),a.global)u?e[t]=r:i(t,r);else{try{a.unsafe?e[t]&&(u=!0):delete e[t]}catch(e){}u?e[t]=r:s.f(e,t,{value:r,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},6279:(e,t,r)=>{"use strict";var n=r(6840);e.exports=function(e,t,r){for(var s in t)n(e,s,t[s],r);return e}},9433:(e,t,r)=>{"use strict";var n=r(4576),s=Object.defineProperty;e.exports=function(e,t){try{s(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},3724:(e,t,r)=>{"use strict";var n=r(9039);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4055:(e,t,r)=>{"use strict";var n=r(4576),s=r(34),o=n.document,i=s(o)&&s(o.createElement);e.exports=function(e){return i?o.createElement(e):{}}},6837:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},8727:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2839:(e,t,r)=>{"use strict";var n=r(4576).navigator,s=n&&n.userAgent;e.exports=s?String(s):""},9519:(e,t,r)=>{"use strict";var n,s,o=r(4576),i=r(2839),a=o.process,u=o.Deno,c=a&&a.versions||u&&u.version,l=c&&c.v8;l&&(s=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!s&&i&&(!(n=i.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=i.match(/Chrome\/(\d+)/))&&(s=+n[1]),e.exports=s},6518:(e,t,r)=>{"use strict";var n=r(4576),s=r(7347).f,o=r(6699),i=r(6840),a=r(9433),u=r(7740),c=r(2796);e.exports=function(e,t){var r,l,f,p,m,d=e.target,h=e.global,g=e.stat;if(r=h?n:g?n[d]||a(d,{}):n[d]&&n[d].prototype)for(l in t){if(p=t[l],f=e.dontCallGetSet?(m=s(r,l))&&m.value:r[l],!c(h?l:d+(g?".":"#")+l,e.forced)&&void 0!==f){if(typeof p==typeof f)continue;u(p,f)}(e.sham||f&&f.sham)&&o(p,"sham",!0),i(r,l,p,e)}}},9039:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},6080:(e,t,r)=>{"use strict";var n=r(7476),s=r(9306),o=r(616),i=n(n.bind);e.exports=function(e,t){return s(e),void 0===t?e:o?i(e,t):function(){return e.apply(t,arguments)}}},616:(e,t,r)=>{"use strict";var n=r(9039);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},9565:(e,t,r)=>{"use strict";var n=r(616),s=Function.prototype.call;e.exports=n?s.bind(s):function(){return s.apply(s,arguments)}},350:(e,t,r)=>{"use strict";var n=r(3724),s=r(9297),o=Function.prototype,i=n&&Object.getOwnPropertyDescriptor,a=s(o,"name"),u=a&&"something"===function(){}.name,c=a&&(!n||n&&i(o,"name").configurable);e.exports={EXISTS:a,PROPER:u,CONFIGURABLE:c}},7476:(e,t,r)=>{"use strict";var n=r(2195),s=r(9504);e.exports=function(e){if("Function"===n(e))return s(e)}},9504:(e,t,r)=>{"use strict";var n=r(616),s=Function.prototype,o=s.call,i=n&&s.bind.bind(o,o);e.exports=n?i:function(e){return function(){return o.apply(e,arguments)}}},7751:(e,t,r)=>{"use strict";var n=r(4576),s=r(4901);e.exports=function(e,t){return arguments.length<2?(r=n[e],s(r)?r:void 0):n[e]&&n[e][t];var r}},1767:e=>{"use strict";e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},851:(e,t,r)=>{"use strict";var n=r(6955),s=r(5966),o=r(4117),i=r(6269),a=r(8227)("iterator");e.exports=function(e){if(!o(e))return s(e,a)||s(e,"@@iterator")||i[n(e)]}},81:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),o=r(8551),i=r(6823),a=r(851),u=TypeError;e.exports=function(e,t){var r=arguments.length<2?a(e):t;if(s(r))return o(n(r,e));throw new u(i(e)+" is not iterable")}},5966:(e,t,r)=>{"use strict";var n=r(9306),s=r(4117);e.exports=function(e,t){var r=e[t];return s(r)?void 0:n(r)}},4576:function(e,t,r){"use strict";var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:(e,t,r)=>{"use strict";var n=r(9504),s=r(8981),o=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return o(s(e),t)}},421:e=>{"use strict";e.exports={}},397:(e,t,r)=>{"use strict";var n=r(7751);e.exports=n("document","documentElement")},5917:(e,t,r)=>{"use strict";var n=r(3724),s=r(9039),o=r(4055);e.exports=!n&&!s((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},7055:(e,t,r)=>{"use strict";var n=r(9504),s=r(9039),o=r(2195),i=Object,a=n("".split);e.exports=s((function(){return!i("z").propertyIsEnumerable(0)}))?function(e){return"String"===o(e)?a(e,""):i(e)}:i},3706:(e,t,r)=>{"use strict";var n=r(9504),s=r(4901),o=r(7629),i=n(Function.toString);s(o.inspectSource)||(o.inspectSource=function(e){return i(e)}),e.exports=o.inspectSource},1181:(e,t,r)=>{"use strict";var n,s,o,i=r(8622),a=r(4576),u=r(34),c=r(6699),l=r(9297),f=r(7629),p=r(6119),m=r(421),d="Object already initialized",h=a.TypeError,g=a.WeakMap;if(i||f.state){var y=f.state||(f.state=new g);y.get=y.get,y.has=y.has,y.set=y.set,n=function(e,t){if(y.has(e))throw new h(d);return t.facade=e,y.set(e,t),t},s=function(e){return y.get(e)||{}},o=function(e){return y.has(e)}}else{var v=p("state");m[v]=!0,n=function(e,t){if(l(e,v))throw new h(d);return t.facade=e,c(e,v,t),t},s=function(e){return l(e,v)?e[v]:{}},o=function(e){return l(e,v)}}e.exports={set:n,get:s,has:o,enforce:function(e){return o(e)?s(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!u(t)||(r=s(t)).type!==e)throw new h("Incompatible receiver, "+e+" required");return r}}}},4209:(e,t,r)=>{"use strict";var n=r(8227),s=r(6269),o=n("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(s.Array===e||i[o]===e)}},4376:(e,t,r)=>{"use strict";var n=r(2195);e.exports=Array.isArray||function(e){return"Array"===n(e)}},4901:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},2796:(e,t,r)=>{"use strict";var n=r(9039),s=r(4901),o=/#|\.prototype\./,i=function(e,t){var r=u[a(e)];return r===l||r!==c&&(s(t)?n(t):!!t)},a=i.normalize=function(e){return String(e).replace(o,".").toLowerCase()},u=i.data={},c=i.NATIVE="N",l=i.POLYFILL="P";e.exports=i},4117:e=>{"use strict";e.exports=function(e){return null==e}},34:(e,t,r)=>{"use strict";var n=r(4901);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},6395:e=>{"use strict";e.exports=!1},757:(e,t,r)=>{"use strict";var n=r(7751),s=r(4901),o=r(1625),i=r(7040),a=Object;e.exports=i?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return s(t)&&o(t.prototype,a(e))}},2652:(e,t,r)=>{"use strict";var n=r(6080),s=r(9565),o=r(8551),i=r(6823),a=r(4209),u=r(6198),c=r(1625),l=r(81),f=r(851),p=r(9539),m=TypeError,d=function(e,t){this.stopped=e,this.result=t},h=d.prototype;e.exports=function(e,t,r){var g,y,v,b,_,w,x,A=r&&r.that,R=!(!r||!r.AS_ENTRIES),$=!(!r||!r.IS_RECORD),S=!(!r||!r.IS_ITERATOR),j=!(!r||!r.INTERRUPTED),O=n(t,A),k=function(e){return g&&p(g,"normal",e),new d(!0,e)},E=function(e){return R?(o(e),j?O(e[0],e[1],k):O(e[0],e[1])):j?O(e,k):O(e)};if($)g=e.iterator;else if(S)g=e;else{if(!(y=f(e)))throw new m(i(e)+" is not iterable");if(a(y)){for(v=0,b=u(e);b>v;v++)if((_=E(e[v]))&&c(h,_))return _;return new d(!1)}g=l(e,y)}for(w=$?e.next:g.next;!(x=s(w,g)).done;){try{_=E(x.value)}catch(e){p(g,"throw",e)}if("object"==typeof _&&_&&c(h,_))return _}return new d(!1)}},9539:(e,t,r)=>{"use strict";var n=r(9565),s=r(8551),o=r(5966);e.exports=function(e,t,r){var i,a;s(e);try{if(!(i=o(e,"return"))){if("throw"===t)throw r;return r}i=n(i,e)}catch(e){a=!0,i=e}if("throw"===t)throw r;if(a)throw i;return s(i),r}},9462:(e,t,r)=>{"use strict";var n=r(9565),s=r(2360),o=r(6699),i=r(6279),a=r(8227),u=r(1181),c=r(5966),l=r(7657).IteratorPrototype,f=r(2529),p=r(9539),m=a("toStringTag"),d="IteratorHelper",h="WrapForValidIterator",g=u.set,y=function(e){var t=u.getterFor(e?h:d);return i(s(l),{next:function(){var r=t(this);if(e)return r.nextHandler();if(r.done)return f(void 0,!0);try{var n=r.nextHandler();return r.returnHandlerResult?n:f(n,r.done)}catch(e){throw r.done=!0,e}},return:function(){var r=t(this),s=r.iterator;if(r.done=!0,e){var o=c(s,"return");return o?n(o,s):f(void 0,!0)}if(r.inner)try{p(r.inner.iterator,"normal")}catch(e){return p(s,"throw",e)}return s&&p(s,"normal"),f(void 0,!0)}})},v=y(!0),b=y(!1);o(b,m,"Iterator Helper"),e.exports=function(e,t,r){var n=function(n,s){s?(s.iterator=n.iterator,s.next=n.next):s=n,s.type=t?h:d,s.returnHandlerResult=!!r,s.nextHandler=e,s.counter=0,s.done=!1,g(this,s)};return n.prototype=t?v:b,n}},713:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),o=r(8551),i=r(1767),a=r(9462),u=r(6319),c=a((function(){var e=this.iterator,t=o(n(this.next,e));if(!(this.done=!!t.done))return u(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return o(this),s(e),new c(i(this),{mapper:e})}},7657:(e,t,r)=>{"use strict";var n,s,o,i=r(9039),a=r(4901),u=r(34),c=r(2360),l=r(2787),f=r(6840),p=r(8227),m=r(6395),d=p("iterator"),h=!1;[].keys&&("next"in(o=[].keys())?(s=l(l(o)))!==Object.prototype&&(n=s):h=!0),!u(n)||i((function(){var e={};return n[d].call(e)!==e}))?n={}:m&&(n=c(n)),a(n[d])||f(n,d,(function(){return this})),e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:h}},6269:e=>{"use strict";e.exports={}},6198:(e,t,r)=>{"use strict";var n=r(8014);e.exports=function(e){return n(e.length)}},283:(e,t,r)=>{"use strict";var n=r(9504),s=r(9039),o=r(4901),i=r(9297),a=r(3724),u=r(350).CONFIGURABLE,c=r(3706),l=r(1181),f=l.enforce,p=l.get,m=String,d=Object.defineProperty,h=n("".slice),g=n("".replace),y=n([].join),v=a&&!s((function(){return 8!==d((function(){}),"length",{value:8}).length})),b=String(String).split("String"),_=e.exports=function(e,t,r){"Symbol("===h(m(t),0,7)&&(t="["+g(m(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!i(e,"name")||u&&e.name!==t)&&(a?d(e,"name",{value:t,configurable:!0}):e.name=t),v&&r&&i(r,"arity")&&e.length!==r.arity&&d(e,"length",{value:r.arity});try{r&&i(r,"constructor")&&r.constructor?a&&d(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=f(e);return i(n,"source")||(n.source=y(b,"string"==typeof t?t:"")),e};Function.prototype.toString=_((function(){return o(this)&&p(this).source||c(this)}),"toString")},741:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},2360:(e,t,r)=>{"use strict";var n,s=r(8551),o=r(6801),i=r(8727),a=r(421),u=r(397),c=r(4055),l=r(6119),f="prototype",p="script",m=l("IE_PROTO"),d=function(){},h=function(e){return"<"+p+">"+e+"</"+p+">"},g=function(e){e.write(h("")),e.close();var t=e.parentWindow.Object;return e=null,t},y=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;y="undefined"!=typeof document?document.domain&&n?g(n):(t=c("iframe"),r="java"+p+":",t.style.display="none",u.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(h("document.F=Object")),e.close(),e.F):g(n);for(var s=i.length;s--;)delete y[f][i[s]];return y()};a[m]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(d[f]=s(e),r=new d,d[f]=null,r[m]=e):r=y(),void 0===t?r:o.f(r,t)}},6801:(e,t,r)=>{"use strict";var n=r(3724),s=r(8686),o=r(4913),i=r(8551),a=r(5397),u=r(1072);t.f=n&&!s?Object.defineProperties:function(e,t){i(e);for(var r,n=a(t),s=u(t),c=s.length,l=0;c>l;)o.f(e,r=s[l++],n[r]);return e}},4913:(e,t,r)=>{"use strict";var n=r(3724),s=r(5917),o=r(8686),i=r(8551),a=r(6969),u=TypeError,c=Object.defineProperty,l=Object.getOwnPropertyDescriptor,f="enumerable",p="configurable",m="writable";t.f=n?o?function(e,t,r){if(i(e),t=a(t),i(r),"function"==typeof e&&"prototype"===t&&"value"in r&&m in r&&!r[m]){var n=l(e,t);n&&n[m]&&(e[t]=r.value,r={configurable:p in r?r[p]:n[p],enumerable:f in r?r[f]:n[f],writable:!1})}return c(e,t,r)}:c:function(e,t,r){if(i(e),t=a(t),i(r),s)try{return c(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},7347:(e,t,r)=>{"use strict";var n=r(3724),s=r(9565),o=r(8773),i=r(6980),a=r(5397),u=r(6969),c=r(9297),l=r(5917),f=Object.getOwnPropertyDescriptor;t.f=n?f:function(e,t){if(e=a(e),t=u(t),l)try{return f(e,t)}catch(e){}if(c(e,t))return i(!s(o.f,e,t),e[t])}},8480:(e,t,r)=>{"use strict";var n=r(1828),s=r(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,s)}},3717:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},2787:(e,t,r)=>{"use strict";var n=r(9297),s=r(4901),o=r(8981),i=r(6119),a=r(2211),u=i("IE_PROTO"),c=Object,l=c.prototype;e.exports=a?c.getPrototypeOf:function(e){var t=o(e);if(n(t,u))return t[u];var r=t.constructor;return s(r)&&t instanceof r?r.prototype:t instanceof c?l:null}},1625:(e,t,r)=>{"use strict";var n=r(9504);e.exports=n({}.isPrototypeOf)},1828:(e,t,r)=>{"use strict";var n=r(9504),s=r(9297),o=r(5397),i=r(9617).indexOf,a=r(421),u=n([].push);e.exports=function(e,t){var r,n=o(e),c=0,l=[];for(r in n)!s(a,r)&&s(n,r)&&u(l,r);for(;t.length>c;)s(n,r=t[c++])&&(~i(l,r)||u(l,r));return l}},1072:(e,t,r)=>{"use strict";var n=r(1828),s=r(8727);e.exports=Object.keys||function(e){return n(e,s)}},8773:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!r.call({1:2},1);t.f=s?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},4270:(e,t,r)=>{"use strict";var n=r(9565),s=r(4901),o=r(34),i=TypeError;e.exports=function(e,t){var r,a;if("string"===t&&s(r=e.toString)&&!o(a=n(r,e)))return a;if(s(r=e.valueOf)&&!o(a=n(r,e)))return a;if("string"!==t&&s(r=e.toString)&&!o(a=n(r,e)))return a;throw new i("Can't convert object to primitive value")}},5031:(e,t,r)=>{"use strict";var n=r(7751),s=r(9504),o=r(8480),i=r(3717),a=r(8551),u=s([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=o.f(a(e)),r=i.f;return r?u(t,r(e)):t}},1103:e=>{"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},7750:(e,t,r)=>{"use strict";var n=r(4117),s=TypeError;e.exports=function(e){if(n(e))throw new s("Can't call method on "+e);return e}},6119:(e,t,r)=>{"use strict";var n=r(5745),s=r(3392),o=n("keys");e.exports=function(e){return o[e]||(o[e]=s(e))}},7629:(e,t,r)=>{"use strict";var n=r(6395),s=r(4576),o=r(9433),i="__core-js_shared__",a=e.exports=s[i]||o(i,{});(a.versions||(a.versions=[])).push({version:"3.40.0",mode:n?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.40.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:(e,t,r)=>{"use strict";var n=r(7629);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},8183:(e,t,r)=>{"use strict";var n=r(9504),s=r(1291),o=r(655),i=r(7750),a=n("".charAt),u=n("".charCodeAt),c=n("".slice),l=function(e){return function(t,r){var n,l,f=o(i(t)),p=s(r),m=f.length;return p<0||p>=m?e?"":void 0:(n=u(f,p))<55296||n>56319||p+1===m||(l=u(f,p+1))<56320||l>57343?e?a(f,p):n:e?c(f,p,p+2):l-56320+(n-55296<<10)+65536}};e.exports={codeAt:l(!1),charAt:l(!0)}},4495:(e,t,r)=>{"use strict";var n=r(9519),s=r(9039),o=r(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!s((function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},5610:(e,t,r)=>{"use strict";var n=r(1291),s=Math.max,o=Math.min;e.exports=function(e,t){var r=n(e);return r<0?s(r+t,0):o(r,t)}},5397:(e,t,r)=>{"use strict";var n=r(7055),s=r(7750);e.exports=function(e){return n(s(e))}},1291:(e,t,r)=>{"use strict";var n=r(741);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},8014:(e,t,r)=>{"use strict";var n=r(1291),s=Math.min;e.exports=function(e){var t=n(e);return t>0?s(t,9007199254740991):0}},8981:(e,t,r)=>{"use strict";var n=r(7750),s=Object;e.exports=function(e){return s(n(e))}},2777:(e,t,r)=>{"use strict";var n=r(9565),s=r(34),o=r(757),i=r(5966),a=r(4270),u=r(8227),c=TypeError,l=u("toPrimitive");e.exports=function(e,t){if(!s(e)||o(e))return e;var r,u=i(e,l);if(u){if(void 0===t&&(t="default"),r=n(u,e,t),!s(r)||o(r))return r;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},6969:(e,t,r)=>{"use strict";var n=r(2777),s=r(757);e.exports=function(e){var t=n(e,"string");return s(t)?t:t+""}},2140:(e,t,r)=>{"use strict";var n={};n[r(8227)("toStringTag")]="z",e.exports="[object z]"===String(n)},655:(e,t,r)=>{"use strict";var n=r(6955),s=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return s(e)}},6823:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},3392:(e,t,r)=>{"use strict";var n=r(9504),s=0,o=Math.random(),i=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+i(++s+o,36)}},7416:(e,t,r)=>{"use strict";var n=r(9039),s=r(8227),o=r(3724),i=r(6395),a=s("iterator");e.exports=!n((function(){var e=new URL("b?a=1&b=2&c=3","https://a"),t=e.searchParams,r=new URLSearchParams("a=1&a=2&b=3"),n="";return e.pathname="c%20d",t.forEach((function(e,r){t.delete("b"),n+=r+e})),r.delete("a",2),r.delete("b",void 0),i&&(!e.toJSON||!r.has("a",1)||r.has("a",2)||!r.has("a",void 0)||r.has("b"))||!t.size&&(i||!o)||!t.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[a]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==n||"x"!==new URL("https://x",void 0).host}))},7040:(e,t,r)=>{"use strict";var n=r(4495);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:(e,t,r)=>{"use strict";var n=r(3724),s=r(9039);e.exports=n&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:e=>{"use strict";var t=TypeError;e.exports=function(e,r){if(e<r)throw new t("Not enough arguments");return e}},8622:(e,t,r)=>{"use strict";var n=r(4576),s=r(4901),o=n.WeakMap;e.exports=s(o)&&/native code/.test(String(o))},8227:(e,t,r)=>{"use strict";var n=r(4576),s=r(5745),o=r(9297),i=r(3392),a=r(4495),u=r(7040),c=n.Symbol,l=s("wks"),f=u?c.for||c:c&&c.withoutSetter||i;e.exports=function(e){return o(l,e)||(l[e]=a&&o(c,e)?c[e]:f("Symbol."+e)),l[e]}},4114:(e,t,r)=>{"use strict";var n=r(6518),s=r(8981),o=r(6198),i=r(4527),a=r(6837);n({target:"Array",proto:!0,arity:1,forced:r(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var t=s(this),r=o(t),n=arguments.length;a(r+n);for(var u=0;u<n;u++)t[r]=arguments[u],r++;return i(t,r),r}})},8111:(e,t,r)=>{"use strict";var n=r(6518),s=r(4576),o=r(679),i=r(8551),a=r(4901),u=r(2787),c=r(2106),l=r(4659),f=r(9039),p=r(9297),m=r(8227),d=r(7657).IteratorPrototype,h=r(3724),g=r(6395),y="constructor",v="Iterator",b=m("toStringTag"),_=TypeError,w=s[v],x=g||!a(w)||w.prototype!==d||!f((function(){w({})})),A=function(){if(o(this,d),u(this)===d)throw new _("Abstract class Iterator not directly constructable")},R=function(e,t){h?c(d,e,{configurable:!0,get:function(){return t},set:function(t){if(i(this),this===d)throw new _("You can't redefine this property");p(this,e)?this[e]=t:l(this,e,t)}}):d[e]=t};p(d,b)||R(b,v),!x&&p(d,y)&&d[y]!==Object||R(y,A),A.prototype=d,n({global:!0,constructor:!0,forced:x},{Iterator:A})},1148:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),o=r(9306),i=r(8551),a=r(1767);n({target:"Iterator",proto:!0,real:!0},{every:function(e){i(this),o(e);var t=a(this),r=0;return!s(t,(function(t,n){if(!e(t,r++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},2489:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),o=r(9306),i=r(8551),a=r(1767),u=r(9462),c=r(6319),l=r(6395),f=u((function(){for(var e,t,r=this.iterator,n=this.predicate,o=this.next;;){if(e=i(s(o,r)),this.done=!!e.done)return;if(t=e.value,c(r,n,[t,this.counter++],!0))return t}}));n({target:"Iterator",proto:!0,real:!0,forced:l},{filter:function(e){return i(this),o(e),new f(a(this),{predicate:e})}})},7588:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),o=r(9306),i=r(8551),a=r(1767);n({target:"Iterator",proto:!0,real:!0},{forEach:function(e){i(this),o(e);var t=a(this),r=0;s(t,(function(t){e(t,r++)}),{IS_RECORD:!0})}})},1701:(e,t,r)=>{"use strict";var n=r(6518),s=r(713);n({target:"Iterator",proto:!0,real:!0,forced:r(6395)},{map:s})},8237:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),o=r(9306),i=r(8551),a=r(1767),u=TypeError;n({target:"Iterator",proto:!0,real:!0},{reduce:function(e){i(this),o(e);var t=a(this),r=arguments.length<2,n=r?void 0:arguments[1],c=0;if(s(t,(function(t){r?(r=!1,n=t):n=e(n,t,c),c++}),{IS_RECORD:!0}),r)throw new u("Reduce of empty iterator with no initial value");return n}})},4648:(e,t,r)=>{"use strict";var n=r(6518),s=r(6639).every;n({target:"AsyncIterator",proto:!0,real:!0},{every:function(e){return s(this,e)}})},7333:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),o=r(9306),i=r(8551),a=r(34),u=r(1767),c=r(2059),l=r(2529),f=r(772),p=r(6395),m=c((function(e){var t=this,r=t.iterator,n=t.predicate;return new e((function(o,u){var c=function(e){t.done=!0,u(e)},p=function(e){f(r,c,e,c)},m=function(){try{e.resolve(i(s(t.next,r))).then((function(r){try{if(i(r).done)t.done=!0,o(l(void 0,!0));else{var s=r.value;try{var u=n(s,t.counter++),f=function(e){e?o(l(s,!1)):m()};a(u)?e.resolve(u).then(f,p):f(u)}catch(e){p(e)}}}catch(e){c(e)}}),c)}catch(e){c(e)}};m()}))}));n({target:"AsyncIterator",proto:!0,real:!0,forced:p},{filter:function(e){return i(this),o(e),new m(u(this),{predicate:e})}})},9920:(e,t,r)=>{"use strict";var n=r(6518),s=r(6639).forEach;n({target:"AsyncIterator",proto:!0,real:!0},{forEach:function(e){return s(this,e)}})},1393:(e,t,r)=>{"use strict";var n=r(6518),s=r(1750);n({target:"AsyncIterator",proto:!0,real:!0,forced:r(6395)},{map:s})},4905:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),o=r(9306),i=r(8551),a=r(34),u=r(7751),c=r(1767),l=r(772),f=u("Promise"),p=TypeError;n({target:"AsyncIterator",proto:!0,real:!0},{reduce:function(e){i(this),o(e);var t=c(this),r=t.iterator,n=t.next,u=arguments.length<2,m=u?void 0:arguments[1],d=0;return new f((function(t,o){var c=function(e){l(r,o,e,o)},h=function(){try{f.resolve(i(s(n,r))).then((function(r){try{if(i(r).done)u?o(new p("Reduce of empty iterator with no initial value")):t(m);else{var n=r.value;if(u)u=!1,m=n,h();else try{var s=e(m,n,d),l=function(e){m=e,h()};a(s)?f.resolve(s).then(l,c):l(s)}catch(e){c(e)}}d++}catch(e){o(e)}}),o)}catch(e){o(e)}};h()}))}})},8992:(e,t,r)=>{"use strict";r(8111)},3215:(e,t,r)=>{"use strict";r(1148)},4520:(e,t,r)=>{"use strict";r(2489)},3949:(e,t,r)=>{"use strict";r(7588)},1454:(e,t,r)=>{"use strict";r(1701)},8872:(e,t,r)=>{"use strict";r(8237)},8898:(e,t,r)=>{"use strict";var n=r(6518),s=r(8183).charAt,o=r(7750),i=r(1291),a=r(655);n({target:"String",proto:!0,forced:!0},{at:function(e){var t=a(o(this)),r=t.length,n=i(e),u=n>=0?n:r+n;return u<0||u>=r?void 0:s(t,u)}})},4603:(e,t,r)=>{"use strict";var n=r(6840),s=r(9504),o=r(655),i=r(2812),a=URLSearchParams,u=a.prototype,c=s(u.append),l=s(u.delete),f=s(u.forEach),p=s([].push),m=new a("a=1&a=2&b=3");m.delete("a",1),m.delete("b",void 0),m+""!="a=2"&&n(u,"delete",(function(e){var t=arguments.length,r=t<2?void 0:arguments[1];if(t&&void 0===r)return l(this,e);var n=[];f(this,(function(e,t){p(n,{key:t,value:e})})),i(t,1);for(var s,a=o(e),u=o(r),m=0,d=0,h=!1,g=n.length;m<g;)s=n[m++],h||s.key===a?(h=!0,l(this,s.key)):d++;for(;d<g;)(s=n[d++]).key===a&&s.value===u||c(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},7566:(e,t,r)=>{"use strict";var n=r(6840),s=r(9504),o=r(655),i=r(2812),a=URLSearchParams,u=a.prototype,c=s(u.getAll),l=s(u.has),f=new a("a=1");!f.has("a",2)&&f.has("a",void 0)||n(u,"has",(function(e){var t=arguments.length,r=t<2?void 0:arguments[1];if(t&&void 0===r)return l(this,e);var n=c(this,e);i(t,1);for(var s=o(r),a=0;a<n.length;)if(n[a++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},8721:(e,t,r)=>{"use strict";var n=r(3724),s=r(9504),o=r(2106),i=URLSearchParams.prototype,a=s(i.forEach);n&&!("size"in i)&&o(i,"size",{get:function(){var e=0;return a(this,(function(){e++})),e},configurable:!0,enumerable:!0})},2222:(e,t,r)=>{"use strict";var n=r(6518),s=r(7751),o=r(9039),i=r(2812),a=r(655),u=r(7416),c=s("URL"),l=u&&o((function(){c.canParse()})),f=o((function(){return 1!==c.canParse.length}));n({target:"URL",stat:!0,forced:!l||f},{canParse:function(e){var t=i(arguments.length,1),r=a(e),n=t<2||void 0===arguments[1]?void 0:a(arguments[1]);try{return!!new c(r,n)}catch(e){return!1}}})}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e={autoRevealNsfw:!1,includeVideoThumbnail:!1,keyboardShortcut:!0};r(1393),r(1454);class t{storage;constructor(e){this.storage=e}async getItemByKey(e){const t=String(e),r=await this.storage.get(t);return Object.keys(r).includes(t)?r:void 0}async getItemByDefaults(e){const t=await this.storage.get(e);return{...e,...t}}async setItem(e){await this.storage.set(e)}async removeItem(e){await this.storage.remove(Array.isArray(e)?[...e].map((e=>String(e))):String(e))}}var n=r(6815),s=r.n(n);r(4114),r(4905),r(8992),r(8872);const o=e=>({error:e,value:void 0}),i=e=>({error:void 0,value:e});var a=r(6075),u=r.n(a);const c=u().object({action:u().valid("download-media"),payload:u().object({tweetId:u().string().required(),screenName:u().string().required()}).required()});class l{payload;constructor(e){this.payload=e}makeResponse(...e){const[t,r]=e;return t?{status:"ok"}:{status:"error",reason:r}}static validate(e){const{value:t,error:r}=c.validate(e);return r?o(r):i(new l(t.payload))}toObject(){return{action:"download-media",payload:this.payload}}}const f=u().object({action:u().valid("check-download-history").required(),payload:u().object({tweetId:u().string().required()}).required()});class p{payload;constructor(e){this.payload=e}static validate(e){const{value:t,error:r}=f.validate(e);return r?o(r):i(new p(t.payload))}makeResponse(...e){const[t,r]=e;return t?{status:"ok",payload:{isExist:r.isExist}}:{status:"error",reason:r}}toObject(){return{action:"check-download-history",payload:this.payload}}}const m=u().object({action:u().valid("capture-response").required(),payload:u().object({type:u().string().valid("TweetDetail","TweetResultByRestId","UserTweets","UserMedia","HomeTimeline","UserTweetsAndReplies","UserHighlightsTweets","UserArticlesTweets","Bookmarks","Likes","CommunitiesExploreTimeline","ListLatestTweetsTimeline","HomeLatestTimeline").required(),body:u().string().required()}).required()});class d{payload;constructor(e){this.payload=e}makeResponse(...e){const[t,r]=e;return t?{status:"ok"}:{status:"error",reason:r}}toObject(){return{action:"capture-response",payload:this.payload}}static validate(e){const{value:t,error:r}=m.validate(e);return r?o(r):i(new d(t.payload))}}const h=async e=>n.runtime.sendMessage(e.toObject());class g{payload;constructor(e){this.payload=e}makeResponse(e,t){if(!0===e&&this.isResponsePayload(t))return{status:"ok",payload:t};if(!1===e&&"string"==typeof t)return{status:"error",reason:t};throw new Error("Invalid arguments to makeResponse")}isResponsePayload(e){return"object"==typeof e&&null!==e&&"transactionId"in e}toObject(){return{action:"request-tx-id",payload:this.payload}}static validate(e){const{value:t,error:r}=v.validate(e);return r?o(r):i(new g(t.payload))}}const y=u().object({method:u().string().required(),path:u().string().required()}).unknown(!1),v=u().object({action:u().valid("request-tx-id").required(),payload:y.required()}).unknown(!1);r(7333),r(4520),r(8898),r(2222),r(4603),r(7566),r(8721),r(4648),r(3215);class b{props;constructor(e){this.props=Object.freeze(e)}mapBy(e){return e(this.props)}is(e){if(null==e)return!1;if(!(e instanceof b&&e instanceof this.constructor))return!1;for(const t in this.props){if(!Object.hasOwn(e.props,t))return!1;const r=this.props[t],n=e.props[t];if(!(r instanceof b?r.is(n):Array.isArray(r)&&Array.isArray(n)?_(r,n):r===n))return!1}return!0}toJSON(){return this.props}duplicate(){return Object.assign(Object.create(Object.getPrototypeOf(this)),this)}}const _=(e,t)=>Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(((e,r)=>e instanceof b?e.is(t[r]):e===t[r]));class w extends b{get screenName(){return this.props.screenName}get tweetId(){return this.props.tweetId}}function x(e,t){if(2!==arguments.length||t)return(t??document).querySelector(String(e))??void 0}function A(e,t){return!(2===arguments.length&&!t)&&Boolean((t??document).querySelector(String(e)))}function R(e,t){if(2===arguments.length&&!t)return[];if(!t||"function"==typeof t.querySelectorAll){const r=(t??document).querySelectorAll(String(e));return Array.prototype.slice.call(r)}const r=new Set;for(const n of t)for(const t of n.querySelectorAll(String(e)))r.add(t);return[...r]}const $=()=>window.location.host,S=()=>{const e=$();return"x.com"===e||"mobile.x.com"===e},j=/\/compose\/tweet\/?.*/,O=/\/intent\/tweet\/?.*/,k=/\/i\/lists\/add_member/,E=/\/\d+\/retweets$/,I=/\/\d+\/likes$/,M=/\/.*\/status\/\d+/,T=()=>Boolean(window.location.pathname.match(M)),C=()=>"tweetdeck.twitter.com"===$()&&A("#react-root");function N(e,t,r,n,s,o,i,a,u){switch(arguments.length){case 1:return e;case 2:return function(){return t(e.apply(this,arguments))};case 3:return function(){return r(t(e.apply(this,arguments)))};case 4:return function(){return n(r(t(e.apply(this,arguments))))};case 5:return function(){return s(n(r(t(e.apply(this,arguments)))))};case 6:return function(){return o(s(n(r(t(e.apply(this,arguments))))))};case 7:return function(){return i(o(s(n(r(t(e.apply(this,arguments)))))))};case 8:return function(){return a(i(o(s(n(r(t(e.apply(this,arguments))))))))};case 9:return function(){return u(a(i(o(s(n(r(t(e.apply(this,arguments)))))))))}}}function P(e,t,r,n,s,o,i,a,u){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return r(t(e));case 4:return n(r(t(e)));case 5:return s(n(r(t(e))));case 6:return o(s(n(r(t(e)))));case 7:return i(o(s(n(r(t(e))))));case 8:return a(i(o(s(n(r(t(e)))))));case 9:return u(a(i(o(s(n(r(t(e))))))));default:for(var c=arguments[0],l=1;l<arguments.length;l++)c=arguments[l](c);return c}}var q=function(e,t){var r="number"==typeof e?function(t){return t.length>=e}:e;return function(){var e=Array.from(arguments);return r(arguments)?t.apply(this,e):function(r){return t.apply(void 0,function(e,t,r){if(r||2===arguments.length)for(var n,s=0,o=t.length;s<o;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))}([r],e,!1))}}},U=function(e){return{_tag:"Left",left:e}},D=function(e){return{_tag:"Right",right:e}},L={};Object.prototype.hasOwnProperty;var F=function(e,t){return function(r){for(var n=r.length,s=e,o=0;o<n;o++)s=t(o,s,r[o]);return s}},z=function(e){return e.length>0},W=function(e,t){return F(e,(function(e,r,n){return t(r,n)}))};function B(e){return function(t,r){return e.chain(t,(function(t){return e.map(r(t),(function(){return t}))}))}}function H(e){return function(t,r){return function(n){return e.chain(n,(function(n){return e.map(r(n),(function(e){var r;return Object.assign({},n,((r={})[t]=e,r))}))}))}}}function K(e){return function(t){return function(r){return e.fromEither("None"===r._tag?U(t()):D(r.value))}}}function V(e){return function(t,r){return e.map(t,(function(){return r}))}}function G(e){var t=V(e);return function(e){return t(e,void 0)}}var Z=U,Y=D,Q=q(2,(function(e,t){return ce(e)?e:t(e.right)})),J=function(e,t){return P(e,te(t))},X=function(e,t){return P(e,se(t))},ee="Either",te=function(e){return function(t){return ce(t)?t:Y(e(t.right))}},re={URI:ee,map:J},ne=(q(2,V(re)),G(re),Y),se=function(e){return function(t){return ce(t)?t:ce(e)?e:Y(t.right(e.right))}},oe={URI:ee,map:J,ap:X},ie={URI:ee,map:J,ap:X,chain:Q},ae={URI:ee,fromEither:function(e){return e}},ue=K(ae),ce=function(e){return"Left"===e._tag},le=function(e,t){return function(r){return ce(r)?e(r.left):t(r.right)}},fe=q(2,B(ie)),pe=function(e){return function(t){return null==t?Z(e):Y(t)}};function me(e){try{return e instanceof Error?e:new Error(String(e))}catch(e){return new Error}}var de=ne(L),he=H(ie),ge=r(3702),ye=r(3643),ve=r(5254);const be=e=>e instanceof HTMLDivElement,_e=e=>be(e)?"photo":(e=>{if(e instanceof HTMLDivElement)return!1;const t=e.classList.length,r=3===t||7===t||6===t;return T()&&r})(e)?"status":"stream",we=/\/history$/,xe=e=>Boolean(e?.closest('[data-testid="card.wrapper"]')),Ae=e=>Boolean(e?.closest('[role="link"]')?.querySelector("time")),Re=e=>Boolean(e?.closest('[id^="id"]:not([aria-labelledby])')?.querySelector("time")),$e=e=>e&&((e=>{const t=(e=>x('[data-testid="videoPlayer"]',e)||x('[data-testid="playButton"]',e)||x('[data-testid="videoComponent"]',e))(e);return Boolean(t&&!Ae(t)&&!xe(t))})(e)||(e=>{if(be(e))return!0;const t=(e=>{const t=x('a[href*="/status/"] > time',e);return t?t.closest("a"):x('a[href*="/status/"]',e)})(e);if(!t)return!1;const r=(n=(e=>{const t=URL.canParse(e)?new URL(e).pathname:e;return t.includes("/photo/")?t:`${t}/photo`})((t.getAttribute("href")??"").replace(we,"")),e=>x(`[href*="${n}"]`,e))(e);var n;return!!r&&!Re(r)})(e)),Se=e=>!(A(".deck-harvester",e)||A(".harvester",e)),je=e=>(0,ye.pipe)(de,he("links",(()=>{return(0,ye.pipe)(e,ke,(t=()=>Z("Failed to get links from article."),r=e=>Y(e),function(e){return z(e)?r(e):t()}));var t,r})),he("screenName",(({links:e})=>(0,ye.pipe)(e,Me(Ie),ue((()=>"Failed to get screen name."))))),he("tweetId",(({links:e})=>(0,ye.pipe)(e,Me(Ee),ue((()=>"Failed to get tweet id."))))),te((e=>new w({screenName:e.screenName,tweetId:e.tweetId})))),Oe=Object.freeze({id:/(?:status\/)(\d+)/,screenName:/(\w+)\/(?:status\/)/,photoModeUrl:/\w+\/status\/\d+\/(photo|video)\/\d+/,editedHistoryUrl:/(?<=.+)\/history$/}),ke=e=>{const t=be(e)?R('[href*="analytics"]',e):R('[data-testid="User-Name"] [href]',e),r=x("a > time",e);return"A"===r?.parentElement?.tagName&&t.push(r.parentElement),t.filter((e=>e.hasAttribute("href"))).map((e=>e.getAttribute("href"))).filter(ve.Kg).map((e=>e.replace(Oe.editedHistoryUrl,"")))},Ee=e=>e.match(Oe.id)?.at(1),Ie=e=>e.match(Oe.screenName)?.at(1),Me=e=>t=>(0,ye.pipe)(t,W("",((t,r)=>(0,ve.Im)(t)?e(r)??"":t)),(0,ge.fromPredicate)((e=>(0,ve.Kg)(e)&&!(0,ve.Im)(e)))),Te=e=>(e&&(e.dataset.harvestArticle="true"),e),Ce=e=>e.closest("[data-harvest-article]"),Ne=e=>(0,ye.pipe)(e,Ce,pe("Failed to get target article when parsing tweet info."),Q(je),le((0,ye.flow)(me,o),i));class Pe{downloadKey;buttonQuery;focusing;constructor(e,t){this.buttonQuery=e,this.downloadKey=t,this.focusing=document.activeElement}#e(e){return x(this.buttonQuery,e)}#t(e){return e instanceof HTMLElement&&"INPUT"!==e.tagName&&"TEXTAREA"!==e.tagName&&(!S()||!("classList"in e)||!e.classList.value.includes("Editor"))}handleKeyDown(e){this.#t(e.target)&&e.code===this.downloadKey&&e.target&&this.updateFocusing(e.target)}handleKeyUp(e){if(!this.focusing||!this.#t(e.target)||e.code!==this.downloadKey)return;const t=Ce(this.focusing);if(t){const e=this.#e(t);e&&e.click()}}}class qe extends Pe{constructor(){super(".harvester","KeyP")}updateFocusing(e){this.focusing=e}}class Ue extends Pe{constructor(){super(".harvester","KeyD")}updateFocusing(e){this.focusing=e}}r(9920),r(3949);const De=e=>(e.classList.remove("downloading","success","error","downloaded"),e),Le=e=>t=>(De(t),t.classList.add(e),t),Fe=e=>e.classList.contains("downloading"),ze=e=>{e.stopImmediatePropagation();const t=e.target;if(!(t instanceof Element))return;const r=t.closest(".harvester");if(!r)return;if(Fe(r))return;Le("downloading")(r);const{value:n,error:s}=Ne(r);if(s)return console.error(s),Le("error")(r);const o=new l(n.mapBy((e=>e)));h(o).then((e=>Le("ok"===e.status?"success":"error")(r)))},We=e=>(e.addEventListener("click",ze),e),Be=e=>{const{value:t,error:r}=Ne(e);if(r)return e;const n=new p({tweetId:t.tweetId});return h(n).then((t=>"error"===t.status||Fe(e)?e:t.payload.isExist?Le("downloaded")(e):t.payload.isExist?e:De(e))),e};var He=r(7205),Ke=r.n(He);const Ve=e=>{const t=document.createElement("div");return t.innerHTML=e.trim(),t.firstElementChild},Ge=e=>{if(!e||e.dataset.autoReveal||A('[data-testid="placementTracking"]',t=e)||Boolean(t.closest('[data-testid="placementTracking"]')))return;var t;if("LI"===e.tagName)return void x('[role="button"]',e)?.click();const r=x('[role="button"][style*="blur"]',e);r&&(e.dataset.autoReveal="true",r.click())};function Ze(e){return N(Y,e.of)}function Ye(e){return function(t){return e.map(t,Y)}}function Qe(e){return function(e,t){return function(r){return function(n){return e.map(n,(function(e){return t.map(e,r)}))}}}(e,re)}function Je(e){return function(e,t){return function(r){return function(n){return e.ap(e.map(n,(function(e){return function(r){return t.ap(e,r)}})),r)}}}(e,oe)}var Xe=function(e,t){return function(){return t(e())}},et=function(e,t){return function(){return e()(t())}},tt=function(e){return function(){return e}},rt=q(2,(function(e,t){return function(){return t(e())()}})),nt="IO",st={URI:nt,map:Xe},ot=(q(2,V(st)),G(st),{URI:nt,of:tt}),it={URI:nt,map:Xe,ap:et},at={URI:nt,map:Xe,ap:et,of:tt,chain:rt},ut=(B({URI:nt,map:Xe,ap:et,chain:rt}),Ze(ot)),ct=tt,lt=Ye(st),ft=(function(e){return function(t){return function(r){return e.chain(r,(function(r){return ce(r)?t(r.left):e.of(r)}))}}}(at),function(e,t){return P(e,pt(t))}),pt=Qe(st),mt=(function(){}(st),Je(it)),dt=ut,ht=q(2,function(e){return function(t,r){return e.chain(t,(function(t){return ce(t)?e.of(t):r(t.right)}))}}(at)),gt="IOEither",yt={URI:gt,map:ft},vt=(q(2,V(yt)),G(yt),{URI:gt,map:ft,ap:function(e,t){return P(e,mt(t))},chain:ht}),bt={URI:gt,fromEither:ct},_t={URI:gt,fromIO:lt},wt=q(2,B(vt)),xt=(function(e,t){var r=function(e){return function(t){return N(t,e.fromEither)}}(e),n=B(t)}(bt,vt),function(e,t){var r=B(t)}(_t,vt),dt(L)),At=H(vt);const Rt=e=>{const t=e.cloneNode(!0);var r;return r=t,x('[data-testid="app-text-transition-container"] > span > span',r)?.remove(),t},$t=e=>P(x("svg",e),pe("Failed to get icon svg.")),St=e=>e?.classList?.value??"r-4qtqp9 r-yyyyoo r-1xvli5t r-dnmrzs r-bnwqim r-1plcrui r-lrvibr r-1hdv0qi",jt=e=>P(ne(e),Q(N(St,Mt,ne)),fe((t=>P(e.replaceWith(t),ne)))),Ot=e=>P(x('[role="group"][aria-label]',e)||x('.r-18u37iz[role="group"][id^="id__"]',e),pe("Failed to get action bar.")),kt=e=>P(x('[data-testid="reply"] > div',e),pe("Failed to get sample button")),Et=e=>P(xt,At("mode",(()=>P(e,_e,dt))),At("actionBar",(()=>P(e,Ot,ct))),At("button",(t=>{return P((r=t.mode,e=>P(de,he("wipButton",(()=>P(e,kt,Q(N(Rt,ne))))),fe((({wipButton:e})=>P(e,$t,Q(N(jt))))),he("fullButton",(({wipButton:e})=>P(e,It(r)))),fe((({fullButton:e})=>P(e,$t,Q(N((e=>t=>t.previousElementSibling?.classList.add(`${e}BG`))(r),ne))))),fe((({fullButton:e})=>P(e,We,ne))),te((e=>e.fullButton))))(e),ct);var r})),wt((e=>P(e.actionBar.appendChild(e.button),dt))),wt((e=>P(Be(e.button),dt))),pt((()=>"ok"))),It=e=>t=>P(ne({wrapper:Ve(`\n      <div class="harvester ${e}" data-testid="harvester-button" data-harvest-ref="U2FsdGVkX18434vXoO+1oS21I0YQm8zFX6xy775AvdCpmSEOQHO9ns7wa518zD8t">\n        <div aria-haspopup="true" aria-label="Media Harvest" role="button" data-focusable="true" tabindex="0"         style="display: flex;justify-content: center;"></div>\n      </div>\n      `)}),fe((({wrapper:e})=>P(e.querySelector(".harvester > div"),pe("Failed to get inner wrapper."),fe((e=>P(e.appendChild(t),ne)))))),te((e=>e.wrapper))),Mt=e=>{const t=Ve(Ke());return t.setAttribute("class",e),t.setAttribute("style","opacity: unset !important;"),t},Tt=e=>{if(be(e)&&!Se(e)){const t=(e=>x(".deck-harvester",e)||x(".harvester",e))(e);t&&Be(t)}if(!(Boolean(window.location.pathname.match(k))||Boolean(window.location.pathname.match(E))||Boolean(window.location.pathname.match(I))||Boolean(window.location.pathname.match(j))||Boolean(window.location.pathname.match(O)))&&Se(e)){const t=Et;(0,ye.pipe)(e,Te,t)()}},Ct="harvestObserveId",Nt=(e,t,r,n={childList:!0})=>{const s=t instanceof HTMLElement?t:x(t);if(s&&(i=s,!Object.hasOwn(i.dataset,Ct))){const t=new MutationObserver(r);return t.observe(s,n),(o=e,e=>(e.dataset[Ct]=o,e))(s),t}var o,i};class Pt{autoRevealNsfw;constructor(e){this.autoRevealNsfw=e}initialize(){const e=x('[aria-labelledby="modal-header"]');e&&T()&&!A('[aria-label="Loading"]')&&Tt(e),R("article").forEach((e=>{this.autoRevealNsfw&&Ge(e),$e(e)&&Tt(e)}))}observeModal(){Nt("modal","#layers",(()=>{this.initialize()}),{childList:!0,subtree:!0})}observeRoot(){Nt("root","#react-root",(()=>{this.initialize(),this.observeStream(),this.observeModal(),this.observeColumns()}),{childList:!0,subtree:!0})}observeStream(){const e=e=>{e.forEach((e=>{e.addedNodes.forEach((e=>{if(!(e instanceof HTMLElement))return;const t=x("article",e);t&&(this.autoRevealNsfw&&Ge(t),$e(t)&&Tt(t))}))}))};R('[data-testid="multi-column-layout-column-content"] > section[role="region"] > div[aria-label] > div').forEach((t=>{Nt("Stream",t,e)}))}observeColumns(){Nt("Columns",'main[role="main"] > div > div > div',(e=>{e.forEach((e=>{e.addedNodes.length&&this.observeStream()}))}),{childList:!0})}}class qt{autoRevealNsfw;constructor(e=!1){this.autoRevealNsfw=e}observeRoot(){Nt("Root","#react-root",((e,t)=>{this.initialize(),A('[role="region"]')&&A("article")&&(this.observeHead(),this.observeModal(),this.observeStream(),this.observeHead(),t.disconnect())}),{childList:!0,subtree:!0})}initialize(){const e=x('[aria-labelledby="modal-header"] > div:first-child');e&&T()&&Tt(e);const t=R("article");for(const e of t)this.autoRevealNsfw&&Ge(e),$e(e)&&Tt(e);R('section[role="region"] > div[aria-label] > div li').forEach((e=>this.autoRevealNsfw&&Ge(e)))}observeStream(){Nt("Stream",'section[role="region"] > div[aria-label] > div',(e=>{for(const t of e)for(const e of t.addedNodes){if(!(e instanceof HTMLElement))return;R("li",e).forEach((e=>this.autoRevealNsfw&&Ge(e)));const t=x("article",e);if(!t)return;this.autoRevealNsfw&&Ge(t),$e(t)&&Tt(t)}}))}observeTimeline(){Nt("Timeline",'[data-testid="primaryColumn"] [aria-label]',(()=>{this.initialize()}),{childList:!0,subtree:!0})}observeHead(){Nt("Head","head",(()=>{this.initialize(),this.observeRoot(),this.observeTimeline()}),{childList:!0,subtree:!1})}observeModal(){const e=(e,t)=>{this.initialize(),t.disconnect()};Nt("Modal","#layers",(()=>{this.initialize();const t=x('[aria-labelledby="modal-header"] [aria-expanded="true"]');t&&Nt("Modal Thread",t,e)}),{childList:!0,subtree:!0})}}const Ut=new class{storageArea;constructor(e){this.storageArea=e}async get(){return await this.storageArea.getItemByDefaults(e)}async save(e){await this.storageArea.setItem(e)}async reset(){await this.save(e)}getDefault(){return e}}(new class extends t{constructor(e){super(e||s().storage.local)}}),Dt=(()=>{let e=!1;return t=>{if(!t||e)return;const r=S()?new Ue:C()?new qe:new Ue;r&&(e=!0,window.addEventListener("keyup",(e=>r.handleKeyUp(e))),window.addEventListener("keydown",(e=>r.handleKeyDown(e))))}})();Ut.get().then((e=>(Dt(e.keyboardShortcut),e))).then((e=>{const t=(e=>S()?new qt(e):C()?new Pt(e):new qt(e))(e.autoRevealNsfw);if(t)return window.addEventListener("focus",(()=>{let r=!1;return()=>{Dt(e.keyboardShortcut),t.initialize(),r||(t.observeRoot(),r=!0)}})()),t.observeRoot(),e}));const Lt=[{type:"TweetDetail",endpoint:"TweetDetail"},{type:"TweetResultByRestId",endpoint:"TweetResultByRestId"},{type:"UserTweets",endpoint:"UserTweets"},{type:"UserMedia",endpoint:"UserMedia"},{type:"HomeTimeline",endpoint:"HomeTimeline"},{type:"HomeLatestTimeline",endpoint:"HomeLatestTimeline"},{type:"UserArticlesTweets",endpoint:"UserArticlesTweets"},{type:"UserTweetsAndReplies",endpoint:"UserTweetsAndReplies"},{type:"UserHighlightsTweets",endpoint:"UserHighlightsTweets"},{type:"Bookmarks",endpoint:"Bookmarks"},{type:"Likes",endpoint:"Likes"},{type:"CommunitiesExploreTimeline",endpoint:"CommunitiesExploreTimeline"},{type:"ListLatestTweetsTimeline",endpoint:"ListLatestTweetsTimeline"}],Ft=e=>{for(const{type:t,endpoint:r}of Lt)if(e.endsWith(r))return t;return 0};document.addEventListener("mh:media-response",(async e=>{await h(new d({type:Ft(e.detail.path),body:e.detail.body}))})),n.runtime.onMessage.addListener(((e,t,r)=>{const n=g.validate(e);if(void 0!==n.error)return;const s=n.value.payload,o=self.crypto.randomUUID();return document.addEventListener("mh:tx-id:response",(function e(t){const{uuid:s,value:i}=t.detail;s===o&&(r(n.value.makeResponse(!0,{transactionId:i})),document.removeEventListener("mh:tx-id:response",e))})),document.dispatchEvent(new CustomEvent("mh:tx-id:request",{detail:(i={uuid:o,method:s.method,path:s.path},i)})),!0;var i}))})()})();
//# sourceMappingURL=main.js.map