{"manifest_version": 3, "name": "__MSG_app_appName__", "short_name": "MediaHarvest", "description": "__MSG_app_appDesc__", "version": "__MANIFEST_RELEASE_VERSION__", "version_name": "__MANIFEST_VERSION_NAME__", "default_locale": "en", "icons": {"16": "assets/icons/<EMAIL>", "32": "assets/icons/<EMAIL>", "48": "assets/icons/<EMAIL>", "128": "assets/icons/<EMAIL>"}, "action": {"default_icon": {"16": "assets/icons/<EMAIL>", "32": "assets/icons/<EMAIL>", "48": "assets/icons/<EMAIL>", "128": "assets/icons/<EMAIL>"}, "default_popup": "index.html?tab=popup"}, "options_ui": {"page": "index.html", "open_in_tab": true}, "background": {"service_worker": "sw.js"}, "content_scripts": [{"matches": ["*://twitter.com/*", "*://mobile.twitter.com/*", "*://tweetdeck.twitter.com/*", "*://x.com/*"], "js": ["main.js"], "css": ["main.css"], "run_at": "document_end"}, {"world": "MAIN", "matches": ["*://twitter.com/*", "*://mobile.twitter.com/*", "*://tweetdeck.twitter.com/*", "*://x.com/*"], "js": ["inject.js"], "run_at": "document_start"}], "host_permissions": ["*://twitter.com/*", "*://mobile.twitter.com/*", "*://api.twitter.com/*", "*://tweetdeck.twitter.com/*", "*://x.com/*", "*://*.x.com/*"], "permissions": ["downloads", "cookies", "storage", "notifications", "unlimitedStorage"], "content_security_policy": {"extension_page": "default-src 'self'; connect-src 'self' https://o1169684.ingest.sentry.io https://*.mediaharvest.app https://cognito-identity.ap-northeast-1.amazonaws.com https://twitter.com https://*.twitter.com https://x.com https://*.x.com; style-src https://fonts.googleapis.com 'unsafe-inline'; font-src https://fonts.gstatic.com; script-src 'self'; img-src https://placehold.co https://pbs.twimg.com;"}}