// ==UserScript==
// @name         X Pro Media Only View
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Display only media posts (images, videos, GIFs) in X Pro with old TweetDeck style
// <AUTHOR>
// @match        https://pro.x.com/*
// @match        https://pro.twitter.com/*
// @grant        GM_addStyle
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // Global state
    let mediaOnlyMode = false;
    let observer = null;

    // Add custom styles for old TweetDeck-like media display
    GM_addStyle(`
        /* Media only mode styles */
        .media-only-active article:not(.has-media),
        article.hide-non-media {
            display: none !important;
        }
        
        /* Prevent layout shift by maintaining column structure */
        .media-only-active [data-testid="primaryColumn"] {
            min-height: 100vh;
        }

        /* Old TweetDeck style media grid */
        .media-only-active .media-grid-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 4px;
            padding: 4px;
        }

        .media-only-active .media-grid-item {
            position: relative;
            overflow: hidden;
            aspect-ratio: 1;
            cursor: pointer;
            background: #000;
        }

        .media-only-active .media-grid-item img,
        .media-only-active .media-grid-item video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Toggle button styles */
        .media-only-toggle {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background: #1da1f2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
            transition: background 0.2s;
        }

        .media-only-toggle:hover {
            background: #1a91da;
        }

        .media-only-toggle.active {
            background: #e1444d;
        }

        /* Hide unnecessary elements in media-only mode */
        .media-only-active [data-testid="cellInnerDiv"] > div > div:not(:has(img, video)) {
            display: none;
        }

        /* Compact tweet display in media mode */
        .media-only-active article {
            margin: 0 !important;
            padding: 4px !important;
            border: none !important;
        }

        .media-only-active [data-testid="tweetText"] {
            display: none;
        }

        .media-only-active [data-testid="reply"],
        .media-only-active [data-testid="retweet"],
        .media-only-active [data-testid="like"],
        .media-only-active [data-testid="share"] {
            display: none;
        }

        /* Enhanced media display */
        .media-only-active [data-testid="tweetPhoto"] {
            border-radius: 0 !important;
            margin: 0 !important;
        }

        .media-only-active [data-testid="videoPlayer"] {
            border-radius: 0 !important;
            margin: 0 !important;
        }

        /* GIF handling */
        .media-only-active [data-testid="gifPlayer"] {
            border-radius: 0 !important;
            margin: 0 !important;
        }

        /* Overlay for media info */
        .media-info-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 100%);
            color: white;
            padding: 8px;
            opacity: 0;
            transition: opacity 0.2s;
            pointer-events: none;
        }

        .media-grid-item:hover .media-info-overlay {
            opacity: 1;
        }

        .media-info-overlay .username {
            font-size: 12px;
            font-weight: bold;
        }

        .media-info-overlay .timestamp {
            font-size: 10px;
            opacity: 0.8;
        }
    `);

    // Function to check if a tweet has media
    function hasMedia(article) {
        if (!article) return false;
        
        // Check for images
        const hasImages = article.querySelector('[data-testid="tweetPhoto"]') !== null;
        
        // Check for videos
        const hasVideos = article.querySelector('[data-testid="videoPlayer"]') !== null ||
                         article.querySelector('video') !== null;
        
        // Check for GIFs
        const hasGifs = article.querySelector('[data-testid="gifPlayer"]') !== null ||
                       article.querySelector('[data-testid="card.layoutLarge.media"]') !== null;
        
        // Check for any media in general
        const hasAnyMedia = article.querySelector('img[src*="media"], img[src*="pbs.twimg.com"], video') !== null;
        
        return hasImages || hasVideos || hasGifs || hasAnyMedia;
    }

    // Function to mark tweets with media
    function markMediaTweets(newTweetsOnly = false) {
        // If we're only processing new tweets, just mark them
        if (newTweetsOnly && mediaOnlyMode) {
            const tweets = document.querySelectorAll('article[data-testid="tweet"]:not(.processed)');
            tweets.forEach(tweet => {
                tweet.classList.add('processed');
                if (hasMedia(tweet)) {
                    tweet.classList.add('has-media');
                } else {
                    tweet.classList.add('hide-non-media');
                }
            });
            return;
        }
        
        // Full scan for initial load or toggle
        const tweets = document.querySelectorAll('article[data-testid="tweet"]');
        tweets.forEach(tweet => {
            tweet.classList.add('processed');
            if (hasMedia(tweet)) {
                tweet.classList.add('has-media');
            } else {
                tweet.classList.add('hide-non-media');
            }
        });
    }

    // Function to toggle media only mode
    function toggleMediaOnly() {
        mediaOnlyMode = !mediaOnlyMode;
        
        if (mediaOnlyMode) {
            // Mark all tweets and hide non-media ones
            document.body.classList.add('media-only-active');
            markMediaTweets(false);
            startObserver();
        } else {
            // Just remove the active class but keep the media markers
            // This way hidden tweets stay hidden until page refresh
            document.body.classList.remove('media-only-active');
            stopObserver();
        }
        
        // Update button state
        const button = document.querySelector('.media-only-toggle');
        if (button) {
            button.classList.toggle('active', mediaOnlyMode);
            button.textContent = mediaOnlyMode ? 'Exit Media Only' : 'Media Only';
        }
    }

    // Create toggle button
    function createToggleButton() {
        const button = document.createElement('button');
        button.className = 'media-only-toggle';
        button.textContent = 'Media Only';
        button.addEventListener('click', toggleMediaOnly);
        document.body.appendChild(button);
    }


    // Mutation observer to handle dynamically loaded tweets
    function startObserver() {
        if (observer) return;
        
        observer = new MutationObserver((mutations) => {
            let needsUpdate = false;
            
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        if (node.matches && node.matches('article[data-testid="tweet"]')) {
                            needsUpdate = true;
                        } else if (node.querySelector && node.querySelector('article[data-testid="tweet"]')) {
                            needsUpdate = true;
                        }
                    }
                });
            });
            
            if (needsUpdate && mediaOnlyMode) {
                markMediaTweets(true); // Only process new tweets
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Stop observer
    function stopObserver() {
        if (observer) {
            observer.disconnect();
            observer = null;
        }
    }

    // Enhanced media grid view (optional - for true old TweetDeck style)
    function createMediaGrid() {
        // This would create a grid layout similar to old TweetDeck
        // Currently keeping the timeline view but hiding non-media posts
        // Can be expanded to create a true grid if needed
    }

    // Initialize when page is ready
    function init() {
        // Wait a bit for the page to fully load
        setTimeout(() => {
            createToggleButton();
            
            // Auto-enable if URL contains media parameter
            if (window.location.search.includes('media=true')) {
                toggleMediaOnly();
            }
        }, 2000);
    }

    // Handle navigation changes in single-page app
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            // Re-mark tweets on navigation
            if (mediaOnlyMode) {
                setTimeout(markMediaTweets, 500);
            }
        }
    }).observe(document, {subtree: true, childList: true});

    // Start the script
    init();

    // Keyboard shortcut (Alt+M)
    document.addEventListener('keydown', (e) => {
        if (e.altKey && e.key === 'm') {
            toggleMediaOnly();
        }
    });

})();