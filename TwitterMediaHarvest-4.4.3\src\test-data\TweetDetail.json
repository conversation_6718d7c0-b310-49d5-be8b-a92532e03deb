{"tagged-video": {"data": {"threaded_conversation_with_injections_v2": {"instructions": [{"type": "TimelineAddEntries", "entries": [{"entryId": "tweet-1829835601941823508", "sortIndex": "7393536434912952299", "content": {"entryType": "TimelineTimelineItem", "__typename": "TimelineTimelineItem", "itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1829835601941823508", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjg3OTc3MjQ0MDIzOTE0NDk2", "rest_id": "1287977244023914496", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": true, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "<PERSON><PERSON> Jul 28 05:05:22 +0000 2020", "default_profile": true, "default_profile_image": false, "description": "ご連絡はDMかmailにお願いします\ncommission→https://t.co/6LXHtBNfI9\nhttps://t.co/wtS8AVjwUc\nhttps://t.co/nd3Fw5RL0y\n✉:<EMAIL>", "entities": {"description": {"urls": [{"display_url": "skeb.jp/@birdman460492…", "expanded_url": "https://skeb.jp/@birdman46049238", "url": "https://t.co/6LXHtBNfI9", "indices": [30, 53]}, {"display_url": "patreon.com/birdman342", "expanded_url": "https://www.patreon.com/birdman342", "url": "https://t.co/wtS8AVjwUc", "indices": [54, 77]}, {"display_url": "birdman.fanbox.cc", "expanded_url": "https://birdman.fanbox.cc/", "url": "https://t.co/nd3Fw5RL0y", "indices": [78, 101]}]}, "url": {"urls": [{"display_url": "potofu.me/birdman", "expanded_url": "https://potofu.me/birdman", "url": "https://t.co/dZLVEgOGxW", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16102, "followers_count": 208033, "friends_count": 513, "has_custom_timelines": true, "is_translator": false, "listed_count": 1167, "location": "pixel", "media_count": 484, "name": "birdman🦊", "normal_followers_count": 208033, "pinned_tweet_ids_str": ["1830171035909702006"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1287977244023914496/1685627539", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1642141882447986693/SdCLuLpz_normal.jpg", "profile_interstitial_type": "", "screen_name": "birdman46049238", "statuses_count": 4108, "translator_type": "none", "url": "https://t.co/dZLVEgOGxW", "verified": false, "want_retweets": true, "withheld_in_countries": []}, "professional": {"rest_id": "1460781805850664966", "professional_type": "Creator", "category": [{"id": 1023, "name": "Illustrator", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 1041, "bookmarked": false, "created_at": "Sat Aug 31 10:56:18 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [0, 11], "entities": {"hashtags": [{"indices": [0, 11], "text": "今月描いた絵を晒そう"}], "media": [{"display_url": "pic.x.com/5rstqoztlf", "expanded_url": "https://x.com/birdman46049238/status/1829835601941823508/photo/1", "id_str": "1829835529070002177", "indices": [12, 35], "media_key": "16_1829835529070002177", "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GWThuAObQAEwO3_.jpg", "type": "animated_gif", "url": "https://t.co/5rstqoztlf", "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1000, "w": 1000, "resize": "fit"}, "medium": {"h": 1000, "w": 1000, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1000, "width": 1000, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [1, 1], "variants": [{"bitrate": 0, "content_type": "video/mp4", "url": "https://video.twimg.com/tweet_video/GWThuAObQAEwO3_.mp4"}]}, "media_results": {"result": {"media_key": "16_1829835529070002177"}}}, {"display_url": "pic.x.com/5rstqoztlf", "expanded_url": "https://x.com/birdman46049238/status/1829835601941823508/photo/1", "id_str": "1829835554080636930", "indices": [12, 35], "media_key": "16_1829835554080636930", "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GWThvdZbQAI2bgw.jpg", "type": "animated_gif", "url": "https://t.co/5rstqoztlf", "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1152, "w": 1152, "resize": "fit"}, "medium": {"h": 1152, "w": 1152, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1152, "width": 1152, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [1, 1], "variants": [{"bitrate": 0, "content_type": "video/mp4", "url": "https://video.twimg.com/tweet_video/GWThvdZbQAI2bgw.mp4"}]}, "media_results": {"result": {"media_key": "16_1829835554080636930"}}}], "symbols": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/5rstqoztlf", "expanded_url": "https://x.com/birdman46049238/status/1829835601941823508/photo/1", "id_str": "1829835529070002177", "indices": [12, 35], "media_key": "16_1829835529070002177", "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GWThuAObQAEwO3_.jpg", "type": "animated_gif", "url": "https://t.co/5rstqoztlf", "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1000, "w": 1000, "resize": "fit"}, "medium": {"h": 1000, "w": 1000, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1000, "width": 1000, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [1, 1], "variants": [{"bitrate": 0, "content_type": "video/mp4", "url": "https://video.twimg.com/tweet_video/GWThuAObQAEwO3_.mp4"}]}, "media_results": {"result": {"media_key": "16_1829835529070002177"}}}, {"display_url": "pic.x.com/5rstqoztlf", "expanded_url": "https://x.com/birdman46049238/status/1829835601941823508/photo/1", "id_str": "1829835554080636930", "indices": [12, 35], "media_key": "16_1829835554080636930", "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GWThvdZbQAI2bgw.jpg", "type": "animated_gif", "url": "https://t.co/5rstqoztlf", "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1152, "w": 1152, "resize": "fit"}, "medium": {"h": 1152, "w": 1152, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1152, "width": 1152, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [1, 1], "variants": [{"bitrate": 0, "content_type": "video/mp4", "url": "https://video.twimg.com/tweet_video/GWThvdZbQAI2bgw.mp4"}]}, "media_results": {"result": {"media_key": "16_1829835554080636930"}}}]}, "favorite_count": 11440, "favorited": false, "full_text": "#今月描いた絵を晒そう https://t.co/5rstqoztlf", "is_quote_status": false, "lang": "qme", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 5, "reply_count": 9, "retweet_count": 1383, "retweeted": false, "user_id_str": "1287977244023914496", "id_str": "1829835601941823508"}}}, "tweetDisplayType": "Tweet", "hasModeratedReplies": false}}}, {"entryId": "conversationthread-1829840602936344867", "sortIndex": "7393536434912952289", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1829840602936344867-tweet-1829840602936344867", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1829840602936344867", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3Mzc5Njk5MA==", "rest_id": "73796990", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Sun Sep 13 02:55:10 +0000 2009", "default_profile": true, "default_profile_image": false, "description": "3DCGデザインやってます！", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 151205, "followers_count": 1711, "friends_count": 5198, "has_custom_timelines": true, "is_translator": false, "listed_count": 46, "location": "池袋", "media_count": 356, "name": "じなす", "normal_followers_count": 1711, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/73796990/1574886486", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1127963915759939585/SroE4iEZ_normal.jpg", "profile_interstitial_type": "", "screen_name": "<PERSON><PERSON><PERSON>", "statuses_count": 37768, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Aug 31 11:16:11 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [17, 26], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 2, "favorited": false, "full_text": "@birdman46049238 BauBau〜☺️", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1829835601941823508", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "de", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "73796990", "id_str": "1829840602936344867"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFSACCGAHgAUKAAIAAAAACCAgCAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1829857154486190587", "sortIndex": "7393536434912952279", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1829857154486190587-tweet-1829857154486190587", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1829857154486190587", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzQ4Nzg0NDUxMTMyNzc2NDUx", "rest_id": "1348784451132776451", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "<PERSON><PERSON> Jan 12 00:10:58 +0000 2021", "default_profile": true, "default_profile_image": false, "description": "The Official Channel Greeter and Twitch Mod to many. Pog V-tuber\nPfp by: @mintyyukime\nModel Rigger: @DogguVT\nBanner by: @DaizyDaizyy", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 449526, "followers_count": 880, "friends_count": 1516, "has_custom_timelines": true, "is_translator": false, "listed_count": 12, "location": "Overijssel, Nederland", "media_count": 1783, "name": "DragoniaC<PERSON>son 👻🐦‍⬛", "normal_followers_count": 880, "pinned_tweet_ids_str": ["1814735439196037448"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1348784451132776451/1640200593", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1899574884037963776/-GnCWgbL_normal.jpg", "profile_interstitial_type": "", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statuses_count": 88135, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Aug 31 12:21:57 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [17, 28], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 2, "favorited": false, "full_text": "@birdman46049238 BAU BAU!!!!", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1829835601941823508", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "in", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1348784451132776451", "id_str": "1829857154486190587"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDSACCKIDgAUKAAIAAAAAGCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1829876092007821585", "sortIndex": "7393536434912952269", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1829876092007821585-tweet-1829876092007821585", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1829876092007821585", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDU0NzU5NjY3MTMzOTExMDQ5", "rest_id": "1454759667133911049", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Sun Oct 31 10:38:41 +0000 2021", "default_profile": true, "default_profile_image": false, "description": "Chill pixel artist that's addicted to coffee || Lvl 10 🔥|| Advanced beginner ||\n\nDiscord: Silkyelwyn\n\n- Commissions are open:p", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 5914, "followers_count": 1343, "friends_count": 443, "has_custom_timelines": false, "is_translator": false, "listed_count": 14, "location": "<PERSON><PERSON><PERSON>", "media_count": 328, "name": "<PERSON><PERSON> 🌳 (Comms Open!)", "normal_followers_count": 1343, "pinned_tweet_ids_str": ["1870868336491569587"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1454759667133911049/1741492508", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1898580752381673472/D246wW9u_normal.png", "profile_interstitial_type": "", "screen_name": "Blissful_<PERSON>wyn", "statuses_count": 4994, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Aug 31 13:37:12 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [17, 45], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 2, "favorited": false, "full_text": "@birdman46049238 New birbman post has dropped", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1829835601941823508", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "1454759667133911049", "id_str": "1829876092007821585"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFSACCSQDgAUKAAIAAAAACCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1829903319843909983", "sortIndex": "7393536434912952259", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1829903319843909983-tweet-1829903319843909983", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1829903319843909983", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDk3MDEwNzQ0NTU4MTk0Njg5", "rest_id": "1497010744558194689", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Fri Feb 25 00:49:18 +0000 2022", "default_profile": true, "default_profile_image": false, "description": "22 year old lad with bit ambitions and no way of accomplishing them! For now at least.\n\nI enjoy Arknights\n\nAnyways, he/him, straight (mostly), and mildly horny.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 35984, "followers_count": 227, "friends_count": 243, "has_custom_timelines": true, "is_translator": false, "listed_count": 2, "location": "Escondido California ", "media_count": 2345, "name": "Classicspace101", "normal_followers_count": 227, "pinned_tweet_ids_str": ["1639031473251692545"], "possibly_sensitive": false, "profile_image_url_https": "https://pbs.twimg.com/profile_images/1534133506007105541/9zTekbR3_normal.jpg", "profile_interstitial_type": "", "screen_name": "ClassicSpace101", "statuses_count": 39570, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Aug 31 15:25:24 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [17, 24], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 2, "favorited": false, "full_text": "@birdman46049238 Bau Bau", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1829835601941823508", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "in", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1497010744558194689", "id_str": "1829903319843909983"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDSACCigDgAUKAAIAAAAACCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1829877909504585870", "sortIndex": "7393536434912952249", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1829877909504585870-tweet-1829877909504585870", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1829877909504585870", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3MTk5ODg0NzUzNzg4MTkwNzQ=", "rest_id": "719988475378819074", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "<PERSON><PERSON> Apr 12 20:40:04 +0000 2016", "default_profile": false, "default_profile_image": false, "description": "+일??+\n+Rest -R.I.P+\n+Mu?s?c?+", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 95877, "followers_count": 628, "friends_count": 2216, "has_custom_timelines": true, "is_translator": false, "listed_count": 14, "location": "Time_Over", "media_count": 3369, "name": "감자프롱(mIProIm)🍙", "normal_followers_count": 628, "pinned_tweet_ids_str": ["1787040663495958543"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/719988475378819074/1734575005", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1747998884411179008/1u0yQanP_normal.jpg", "profile_interstitial_type": "", "screen_name": "codomo327", "statuses_count": 47545, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Aug 31 13:44:25 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [17, 21], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 1, "favorited": false, "full_text": "@birdman46049238 🥰🥰🥰🥰", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1829835601941823508", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "qme", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "719988475378819074", "id_str": "1829877909504585870"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDSACDDADgAUKAAIAAAAACCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1830172439466180754", "sortIndex": "7393536434912952239", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1830172439466180754-tweet-1830172439466180754", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1830172439466180754", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzg2MTY0MTkxMjM1MTMzNDQw", "rest_id": "1786164191235133440", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Thu May 02 22:42:04 +0000 2024", "default_profile": true, "default_profile_image": false, "description": "Hey it's me, <PERSON><PERSON><PERSON>! Formerly @doonkey36, I lost that account for something stupid, and for years have been searching for my creators, and their work. :(", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 52779, "followers_count": 124, "friends_count": 2943, "has_custom_timelines": false, "is_translator": false, "listed_count": 1, "location": "Hell, also known as Florida", "media_count": 795, "name": "D👀nkey!", "normal_followers_count": 124, "pinned_tweet_ids_str": ["1889848082227216823"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1786164191235133440/**********", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1872090100802138112/bH1PDpbG_normal.jpg", "profile_interstitial_type": "", "screen_name": "<PERSON>z<PERSON>oonkey", "statuses_count": 33630, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sun Sep 01 09:14:47 +0000 2024", "conversation_id_str": "1829835601941823508", "display_text_range": [17, 76], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 0, "favorited": false, "full_text": "@birdman46049238 Any info on that Bau Bau game? I remember it, can't find it", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1829835601941823508", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1786164191235133440", "id_str": "1830172439466180754"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFSACCCADgAUKAAIAAAAACCAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "cursor-showmorethreadsprompt-8227389740063809204", "sortIndex": "7393536434912952238", "content": {"entryType": "TimelineTimelineItem", "__typename": "TimelineTimelineItem", "itemContent": {"itemType": "TimelineTimelineCursor", "__typename": "TimelineTimelineCursor", "value": "QAAAAPAxHBlmnMLXpe-QhOUyosTZ8Ymng-UypIK8weKIiuYyxoTc2auV8-QyvsXVifnXj-Uy9ofareLY-uQyJQYRFRAAAA", "cursorType": "ShowMoreThreadsPrompt", "displayTreatment": {"actionText": "Show", "labelText": "Show additional replies, including those that may contain offensive content"}}}}]}, {"type": "TimelineTerminateTimeline", "direction": "Top"}]}}}, "video": {"data": {"threaded_conversation_with_injections_v2": {"instructions": [{"type": "TimelineAddEntries", "entries": [{"entryId": "tweet-1900881067713982967", "sortIndex": "7322490969140792840", "content": {"entryType": "TimelineTimelineItem", "__typename": "TimelineTimelineItem", "itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900881067713982967", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjg3OTc3MjQ0MDIzOTE0NDk2", "rest_id": "1287977244023914496", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": true, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "<PERSON><PERSON> Jul 28 05:05:22 +0000 2020", "default_profile": true, "default_profile_image": false, "description": "ご連絡はDMかmailにお願いします\ncommission→https://t.co/6LXHtBNfI9\nhttps://t.co/wtS8AVjwUc\nhttps://t.co/nd3Fw5RL0y\n✉:<EMAIL>", "entities": {"description": {"urls": [{"display_url": "skeb.jp/@birdman460492…", "expanded_url": "https://skeb.jp/@birdman46049238", "url": "https://t.co/6LXHtBNfI9", "indices": [30, 53]}, {"display_url": "patreon.com/birdman342", "expanded_url": "https://www.patreon.com/birdman342", "url": "https://t.co/wtS8AVjwUc", "indices": [54, 77]}, {"display_url": "birdman.fanbox.cc", "expanded_url": "https://birdman.fanbox.cc/", "url": "https://t.co/nd3Fw5RL0y", "indices": [78, 101]}]}, "url": {"urls": [{"display_url": "potofu.me/birdman", "expanded_url": "https://potofu.me/birdman", "url": "https://t.co/dZLVEgOGxW", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16102, "followers_count": 208031, "friends_count": 513, "has_custom_timelines": true, "is_translator": false, "listed_count": 1167, "location": "pixel", "media_count": 484, "name": "birdman🦊", "normal_followers_count": 208031, "pinned_tweet_ids_str": ["1830171035909702006"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1287977244023914496/1685627539", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1642141882447986693/SdCLuLpz_normal.jpg", "profile_interstitial_type": "", "screen_name": "birdman46049238", "statuses_count": 4108, "translator_type": "none", "url": "https://t.co/dZLVEgOGxW", "verified": false, "want_retweets": true, "withheld_in_countries": []}, "professional": {"rest_id": "1460781805850664966", "professional_type": "Creator", "category": [{"id": 1023, "name": "Illustrator", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 5239, "bookmarked": false, "created_at": "Sat Mar 15 12:05:37 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [0, 8], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/c3R5pqrBy5", "expanded_url": "https://x.com/birdman46049238/status/1900881067713982967/photo/1", "id_str": "1900880507178807299", "indices": [9, 32], "media_key": "16_1900880507178807299", "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GmFIv_7bcAM-RZa.jpg", "type": "animated_gif", "url": "https://t.co/c3R5pqrBy5", "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1152, "w": 1152, "resize": "fit"}, "medium": {"h": 1152, "w": 1152, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1152, "width": 1152, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "variants": [{"bitrate": 0, "content_type": "video/mp4", "url": "https://video.twimg.com/tweet_video/GmFIv_7bcAM-RZa.mp4"}]}, "media_results": {"result": {"media_key": "16_1900880507178807299"}}}], "symbols": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/c3R5pqrBy5", "expanded_url": "https://x.com/birdman46049238/status/1900881067713982967/photo/1", "id_str": "1900880507178807299", "indices": [9, 32], "media_key": "16_1900880507178807299", "media_url_https": "https://pbs.twimg.com/tweet_video_thumb/GmFIv_7bcAM-RZa.jpg", "type": "animated_gif", "url": "https://t.co/c3R5pqrBy5", "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1152, "w": 1152, "resize": "fit"}, "medium": {"h": 1152, "w": 1152, "resize": "fit"}, "small": {"h": 680, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1152, "width": 1152, "focus_rects": []}, "video_info": {"aspect_ratio": [1, 1], "variants": [{"bitrate": 0, "content_type": "video/mp4", "url": "https://video.twimg.com/tweet_video/GmFIv_7bcAM-RZa.mp4"}]}, "media_results": {"result": {"media_key": "16_1900880507178807299"}}}]}, "favorite_count": 47464, "favorited": false, "full_text": "dance! 🦊 https://t.co/c3R5pqrBy5", "is_quote_status": false, "lang": "fr", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 14, "reply_count": 61, "retweet_count": 4382, "retweeted": false, "user_id_str": "1287977244023914496", "id_str": "1900881067713982967"}}}, "tweetDisplayType": "Tweet", "hasModeratedReplies": true}}}, {"entryId": "conversationthread-1900901826448237044", "sortIndex": "7322490969140792830", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900901826448237044-tweet-1900901826448237044", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900901826448237044", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo5MjY0Njc5MTc3NTQ5OTA1OTI=", "rest_id": "926467917754990592", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Fri Nov 03 15:15:39 +0000 2017", "default_profile": false, "default_profile_image": false, "description": "Game animator. I love to draw and animate! I don't take commissions. I don't release my game prototypes.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "martinchangart.com", "expanded_url": "https://www.martinchangart.com/", "url": "https://t.co/nJ07PVGiwx", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 41642, "followers_count": 127977, "friends_count": 187, "has_custom_timelines": true, "is_translator": false, "listed_count": 706, "location": "", "media_count": 477, "name": "<PERSON>", "normal_followers_count": 127977, "pinned_tweet_ids_str": ["1846783108965667149"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/926467917754990592/1641102090", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1740797514356338688/DM__AKt3_normal.jpg", "profile_interstitial_type": "", "screen_name": "TuxedoPato", "statuses_count": 2167, "translator_type": "none", "url": "https://t.co/nJ07PVGiwx", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 1, "bookmarked": false, "created_at": "Sat Mar 15 13:28:07 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 57], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 27, "favorited": false, "full_text": "@birdman46049238 The movement of the mouth is so cute! ❤️", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "926467917754990592", "id_str": "1900901826448237044"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDUAKCGAHgBUKAAIAAAAABCAgCAAAAAA="}}}}}, {"entryId": "conversationthread-1900901826448237044-tweet-1900903226922127620", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900903226922127620", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjg3OTc3MjQ0MDIzOTE0NDk2", "rest_id": "1287977244023914496", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": true, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "<PERSON><PERSON> Jul 28 05:05:22 +0000 2020", "default_profile": true, "default_profile_image": false, "description": "ご連絡はDMかmailにお願いします\ncommission→https://t.co/6LXHtBNfI9\nhttps://t.co/wtS8AVjwUc\nhttps://t.co/nd3Fw5RL0y\n✉:<EMAIL>", "entities": {"description": {"urls": [{"display_url": "skeb.jp/@birdman460492…", "expanded_url": "https://skeb.jp/@birdman46049238", "url": "https://t.co/6LXHtBNfI9", "indices": [30, 53]}, {"display_url": "patreon.com/birdman342", "expanded_url": "https://www.patreon.com/birdman342", "url": "https://t.co/wtS8AVjwUc", "indices": [54, 77]}, {"display_url": "birdman.fanbox.cc", "expanded_url": "https://birdman.fanbox.cc/", "url": "https://t.co/nd3Fw5RL0y", "indices": [78, 101]}]}, "url": {"urls": [{"display_url": "potofu.me/birdman", "expanded_url": "https://potofu.me/birdman", "url": "https://t.co/dZLVEgOGxW", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 16102, "followers_count": 208031, "friends_count": 513, "has_custom_timelines": true, "is_translator": false, "listed_count": 1167, "location": "pixel", "media_count": 484, "name": "birdman🦊", "normal_followers_count": 208031, "pinned_tweet_ids_str": ["1830171035909702006"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1287977244023914496/1685627539", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1642141882447986693/SdCLuLpz_normal.jpg", "profile_interstitial_type": "", "screen_name": "birdman46049238", "statuses_count": 4108, "translator_type": "none", "url": "https://t.co/dZLVEgOGxW", "verified": false, "want_retweets": true, "withheld_in_countries": []}, "professional": {"rest_id": "1460781805850664966", "professional_type": "Creator", "category": [{"id": 1023, "name": "Illustrator", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 2, "bookmarked": false, "created_at": "Sat Mar 15 13:33:41 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [12, 94], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "926467917754990592", "name": "<PERSON>", "screen_name": "TuxedoPato", "indices": [0, 11]}]}, "favorite_count": 16, "favorited": false, "full_text": "@TuxedoPato Thank you! I drew it at a higher resolution than usual to capture the expressions!", "in_reply_to_screen_name": "TuxedoPato", "in_reply_to_status_id_str": "1900901826448237044", "in_reply_to_user_id_str": "926467917754990592", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "1287977244023914496", "id_str": "1900903226922127620"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDUAKEKIDgbEKAAIAAAAAFCBACAAAAAA="}}}}}, {"entryId": "conversationthread-1900901826448237044-cursor-showmore-1525565516795786332", "item": {"itemContent": {"itemType": "TimelineTimelineCursor", "__typename": "TimelineTimelineCursor", "value": "PAAAAPAtPBwcFojEspG72q7hNBUCAAAYJmNvbnZlcnNhdGlvbnRocmVhZC0xOTAwOTAxODI2NDQ4MjM3MDQ0IgAA", "cursorType": "ShowMore", "displayTreatment": {"actionText": "Show replies"}}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901064453275152404", "sortIndex": "7322490969140792820", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901064453275152404-tweet-1901064453275152404", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901064453275152404", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMjkzNzgzNDk1", "rest_id": "2293783495", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "<PERSON>hu Jan 16 03:36:49 +0000 2014", "default_profile": true, "default_profile_image": false, "description": "A streamer #Vtuber gamer.\nWanna bring smiles to the world with my silliness.  \nLove playing RPG, Gacha, and MMO games.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/thehybridvr", "expanded_url": "https://linktr.ee/thehybridvr", "url": "https://t.co/XNAFl1uKV3", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 81285, "followers_count": 574, "friends_count": 343, "has_custom_timelines": false, "is_translator": false, "listed_count": 4, "location": "Secret Scientist Laboratory ", "media_count": 302, "name": "TheHybrid <PERSON>er", "normal_followers_count": 574, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2293783495/1652955019", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1567994702770700288/gZY_tMj0_normal.jpg", "profile_interstitial_type": "", "screen_name": "TheHybridVR", "statuses_count": 4840, "translator_type": "none", "url": "https://t.co/XNAFl1uKV3", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "quoted_status_result": {"result": {"__typename": "Tweet", "rest_id": "1853561037619970110", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoyMjkzNzgzNDk1", "rest_id": "2293783495", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "<PERSON>hu Jan 16 03:36:49 +0000 2014", "default_profile": true, "default_profile_image": false, "description": "A streamer #Vtuber gamer.\nWanna bring smiles to the world with my silliness.  \nLove playing RPG, Gacha, and MMO games.", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "linktr.ee/thehybridvr", "expanded_url": "https://linktr.ee/thehybridvr", "url": "https://t.co/XNAFl1uKV3", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 81285, "followers_count": 574, "friends_count": 343, "has_custom_timelines": false, "is_translator": false, "listed_count": 4, "location": "Secret Scientist Laboratory ", "media_count": 302, "name": "TheHybrid <PERSON>er", "normal_followers_count": 574, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/2293783495/1652955019", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1567994702770700288/gZY_tMj0_normal.jpg", "profile_interstitial_type": "", "screen_name": "TheHybridVR", "statuses_count": 4840, "translator_type": "none", "url": "https://t.co/XNAFl1uKV3", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 9, "bookmarked": false, "created_at": "Mon Nov 04 22:12:43 +0000 2024", "conversation_id_str": "1853561037619970110", "display_text_range": [0, 55], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/lMn38MRhgq", "expanded_url": "https://x.com/TheHybridVR/status/1853561037619970110/video/1", "id_str": "1853560421204062208", "indices": [56, 79], "media_key": "7_1853560421204062208", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1853560421204062208/pu/img/8kZOgTnHBLgenEKF.jpg", "type": "video", "url": "https://t.co/lMn38MRhgq", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 720, "w": 1280, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 720, "width": 1280, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [16, 9], "duration_millis": 73685, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/pl/ipzWWqlHrlLYz5ho.m3u8?tag=12&v=cfc"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/vid/avc1/480x270/Fa7uSxL_a6hL7Hgk.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/vid/avc1/640x360/ewoOfCPdvpc5-dBo.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/vid/avc1/1280x720/yjnLNYUzbDVXX4Zz.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1853560421204062208"}}}], "symbols": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/lMn38MRhgq", "expanded_url": "https://x.com/TheHybridVR/status/1853561037619970110/video/1", "id_str": "1853560421204062208", "indices": [56, 79], "media_key": "7_1853560421204062208", "media_url_https": "https://pbs.twimg.com/ext_tw_video_thumb/1853560421204062208/pu/img/8kZOgTnHBLgenEKF.jpg", "type": "video", "url": "https://t.co/lMn38MRhgq", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 720, "w": 1280, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 720, "width": 1280, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [16, 9], "duration_millis": 73685, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/pl/ipzWWqlHrlLYz5ho.m3u8?tag=12&v=cfc"}, {"bitrate": 256000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/vid/avc1/480x270/Fa7uSxL_a6hL7Hgk.mp4?tag=12"}, {"bitrate": 832000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/vid/avc1/640x360/ewoOfCPdvpc5-dBo.mp4?tag=12"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/ext_tw_video/1853560421204062208/pu/vid/avc1/1280x720/yjnLNYUzbDVXX4Zz.mp4?tag=12"}]}, "media_results": {"result": {"media_key": "7_1853560421204062208"}}}]}, "favorite_count": 34, "favorited": false, "full_text": "doodle - Zachz Winner\nWorld: あのんちゃんと睡眠ができるワールド\nBy:しぶやぴー https://t.co/lMn38MRhgq", "is_quote_status": false, "lang": "ja", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 1, "retweet_count": 4, "retweeted": false, "user_id_str": "2293783495", "id_str": "1853561037619970110"}}}, "legacy": {"bookmark_count": 1, "bookmarked": false, "created_at": "Sun Mar 16 00:14:20 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 74], "entities": {"hashtags": [], "symbols": [], "urls": [{"display_url": "x.com/TheHybridVR/st…", "expanded_url": "https://x.com/TheHybridVR/status/1853561037619970110?t=d0ibts1JLnJ4a5_fccBw_Q&s=19", "url": "https://t.co/9T1B6wwJk1", "indices": [51, 74]}], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 8, "favorited": false, "full_text": "@birdman46049238 Doodle Dance! (*^*) \nMy version: \nhttps://t.co/9T1B6wwJk1", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": true, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "quoted_status_id_str": "1853561037619970110", "quoted_status_permalink": {"url": "https://t.co/9T1B6wwJk1", "expanded": "https://x.com/TheHybridVR/status/1853561037619970110?t=d0ibts1JLnJ4a5_fccBw_Q&s=19", "display": "x.com/TheHybridVR/st…"}, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "2293783495", "id_str": "1901064453275152404"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQAKiSRDgAUKAAIAAAAABCAgCAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900895162097664032", "sortIndex": "7322490969140792810", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900895162097664032-tweet-1900895162097664032", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900895162097664032", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjM0MjU5MzgyNTgzNTU4MTQ1", "rest_id": "1234259382583558145", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Sun Mar 01 23:29:15 +0000 2020", "default_profile": true, "default_profile_image": false, "description": "internet jester 🎉 \nvideo games and food 👩‍🍳\nshe/her ❤️\nBíg<PERSON> deas\n🇮🇪\nBusiness: <EMAIL>", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "twitch.tv/cakejumper", "expanded_url": "https://www.twitch.tv/cakejumper", "url": "https://t.co/uegmlx5Mbp", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 14118, "followers_count": 15665, "friends_count": 686, "has_custom_timelines": true, "is_translator": false, "listed_count": 21, "location": "Ireland ", "media_count": 2778, "name": "cakejumper 🍰🦊", "normal_followers_count": 15665, "pinned_tweet_ids_str": ["1267420640350539776"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1234259382583558145/1657193968", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1884715011508994048/YWGGGUz-_normal.jpg", "profile_interstitial_type": "", "screen_name": "Itscakejumper", "statuses_count": 9240, "translator_type": "none", "url": "https://t.co/uegmlx5Mbp", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 13:01:38 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 95], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 5, "favorited": false, "full_text": "@birdman46049238 The way you do movement and facial expressions will never fail to amaze me ❤️🦊", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1234259382583558145", "id_str": "1900895162097664032"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQAKiigDgAUKAAIAAAAABCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900885493996466419", "sortIndex": "7322490969140792800", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900885493996466419-tweet-1900885493996466419", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900885493996466419", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzEzNDc0NzM0MDA3MTY5MDI0", "rest_id": "1713474734007169024", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Sun Oct 15 08:40:20 +0000 2023", "default_profile": true, "default_profile_image": false, "description": "お映画とか鑑賞しながらのんびりだらりとゲーム配信している個人虎V🐯\nゲーム→MHWilds／HELLDIVERS2／CoD:BO6／Apex／タルコフ\n🔶デザイン:かれいさん(@flat_fish_ )🔶Live2D:つくみさん(@kikyou293 )\n🚫AIの読み込みは禁止です🚫", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "twitch.tv/gassan_tiger", "expanded_url": "https://www.twitch.tv/gassan_tiger", "url": "https://t.co/nowaoU6JAp", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 11163, "followers_count": 224, "friends_count": 271, "has_custom_timelines": false, "is_translator": false, "listed_count": 9, "location": "山形県月山８合目あたり", "media_count": 207, "name": "月山 虎祇", "normal_followers_count": 224, "pinned_tweet_ids_str": ["1878418631492768073"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1713474734007169024/1730798890", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1870982582223228928/haTAhjuJ_normal.jpg", "profile_interstitial_type": "", "screen_name": "Gassan_tiger", "statuses_count": 6285, "translator_type": "none", "url": "https://t.co/nowaoU6JAp", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 12:23:13 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 43], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 0, "favorited": false, "full_text": "@birdman46049238 めちゃくちゃ可愛いです！！！！！最高っす！！！！！！", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "ja", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1713474734007169024", "id_str": "1900885493996466419"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDQAKjDADgAUKAAIAAAAAGCAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901054086364946632", "sortIndex": "7322490969140792790", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901054086364946632-tweet-1901054086364946632", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901054086364946632", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzU4NDY0ODg4Njk3OTM3OTIw", "rest_id": "1758464888697937920", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Fri Feb 16 12:14:54 +0000 2024", "default_profile": true, "default_profile_image": false, "description": "17 y/o Voice lady | Author of VOIDBOUND |\nShe/They | https://t.co/683ITUop79 | https://t.co/nR1FzmLhvd | 1/2/25 | #VOIDBOUNDART", "entities": {"description": {"urls": [{"display_url": "prov1d3nce.bsky.social", "expanded_url": "http://prov1d3nce.bsky.social", "url": "https://t.co/683ITUop79", "indices": [53, 76]}, {"display_url": "en.pronouns.page/@Prov1d3nce", "expanded_url": "https://en.pronouns.page/@Prov1d3nce", "url": "https://t.co/nR1FzmLhvd", "indices": [79, 102]}]}, "url": {"urls": [{"display_url": "voidqueen.straw.page", "expanded_url": "https://voidqueen.straw.page", "url": "https://t.co/V0oU1Cjfk8", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 94022, "followers_count": 202, "friends_count": 2491, "has_custom_timelines": false, "is_translator": false, "listed_count": 2, "location": "hell", "media_count": 567, "name": "PROV1D3NCE", "normal_followers_count": 202, "pinned_tweet_ids_str": ["1885689861924913420"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1758464888697937920/1715742264", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1873526243099885569/wF1LmB_F_normal.jpg", "profile_interstitial_type": "", "screen_name": "PROV1_D3NCE", "statuses_count": 4932, "translator_type": "none", "url": "https://t.co/V0oU1Cjfk8", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 23:33:08 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 50], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 1, "favorited": false, "full_text": "@birdman46049238 dawww, the lil goober! so cute :3", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "1758464888697937920", "id_str": "1901054086364946632"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQAKCCADgBUKAAIAAAAABCAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900951936230466024", "sortIndex": "7322490969140792780", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900951936230466024-tweet-1900951936230466024", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900951936230466024", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDUwODE3MTk5MzI5ODQxMTUz", "rest_id": "1450817199329841153", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Wed Oct 20 13:32:54 +0000 2021", "default_profile": true, "default_profile_image": false, "description": "🐿 ❤️", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 11429, "followers_count": 17106, "friends_count": 413, "has_custom_timelines": false, "is_translator": false, "listed_count": 39, "location": "Forest", "media_count": 158, "name": "나무타 / Namuta", "normal_followers_count": 17106, "pinned_tweet_ids_str": ["1898524828837269747"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1450817199329841153/1707478733", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1857081448936660993/8yEvTEpk_normal.jpg", "profile_interstitial_type": "", "screen_name": "namuta123", "statuses_count": 14134, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 16:47:14 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 38], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 3, "favorited": false, "full_text": "@birdman46049238 🥹🥹🥹🥹🥹🥹🥹❤️❤️❤️❤️❤️❤️❤️", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "qme", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1450817199329841153", "id_str": "1900951936230466024"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDQACiCADgAUKAAIAAAAAFCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900883835681972386", "sortIndex": "7322490969140792770", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900883835681972386-tweet-1900883835681972386", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900883835681972386", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTM4NzU1NzA1NjUzODU4MzA0", "rest_id": "1538755705653858304", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Mon Jun 20 05:28:54 +0000 2022", "default_profile": true, "default_profile_image": false, "description": "✨Yuzin✨ID/EN | 🎨 2D Animator & Illustrator | Chibi & Pixel Art Enthusiast 🐣 | Commission - OPEN VGEN / DM ME : https://t.co/SwxcNmrvgI | https://t.co/FTufYloUjD", "entities": {"description": {"urls": [{"display_url": "vgen.co/Yeahyuzin", "expanded_url": "https://vgen.co/Yeahyuzin", "url": "https://t.co/SwxcNmrvgI", "indices": [111, 134]}, {"display_url": "ko-fi.com/yeahyuzin", "expanded_url": "http://ko-fi.com/yeahyuzin", "url": "https://t.co/FTufYloUjD", "indices": [137, 160]}]}}, "fast_followers_count": 0, "favourites_count": 112, "followers_count": 16, "friends_count": 221, "has_custom_timelines": true, "is_translator": false, "listed_count": 0, "location": "", "media_count": 15, "name": "Yeahyuzin | comm : Open✨", "normal_followers_count": 16, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1538755705653858304/1690441614", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1896854411961311232/DOKC-4FX_normal.jpg", "profile_interstitial_type": "", "screen_name": "<PERSON><PERSON><PERSON>", "statuses_count": 187, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 12:16:37 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 30], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 2, "favorited": false, "full_text": "@birdman46049238 So cute! 💕✨⭐️", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1538755705653858304", "id_str": "1900883835681972386"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQAKiCADgAUKAAIAAAAABCACCAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900894643459441091", "sortIndex": "7322490969140792760", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900894643459441091-tweet-1900894643459441091", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900894643459441091", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo3NDI0ODgxNjEzMzEyMjQ1NzY=", "rest_id": "742488161331224576", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Mon Jun 13 22:45:47 +0000 2016", "default_profile": true, "default_profile_image": false, "description": "Pronouns: He/She/They??? How about yours? ❤️\n\nDMs open 📩 | Feel free to ask for my Discord if we've interacted a bit", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 120122, "followers_count": 448, "friends_count": 1602, "has_custom_timelines": true, "is_translator": false, "listed_count": 8, "location": "🇺🇸 | 🦅", "media_count": 2130, "name": "Steven | nevetS", "normal_followers_count": 448, "pinned_tweet_ids_str": ["1527061577500831744"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/742488161331224576/1730136825", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1899072146955198464/H3blNkjy_normal.png", "profile_interstitial_type": "", "screen_name": "basedSteven_", "statuses_count": 14526, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 12:59:34 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 21], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 0, "favorited": false, "full_text": "@birdman46049238 Cute", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "742488161331224576", "id_str": "1900894643459441091"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQAKiCADgAUKAAIAAAAABCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900882841262846216", "sortIndex": "7322490969140792750", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900882841262846216-tweet-1900882841262846216", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900882841262846216", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMjI1ODg1ODAzNTg0MjE3MDg4", "rest_id": "1225885803584217088", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Fri Feb 07 20:55:37 +0000 2020", "default_profile": true, "default_profile_image": false, "description": "🗝🫖 宅島さんが大好き ももんが好き パスドラする ビリヤードする ずとまよ好き🦔⸒⸒ 🎖 jobはNWエンジニア", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 28518, "followers_count": 595, "friends_count": 853, "has_custom_timelines": true, "is_translator": false, "listed_count": 7, "location": "島猫邸", "media_count": 667, "name": "しみずやすひと", "normal_followers_count": 595, "pinned_tweet_ids_str": ["1712537222405304443"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1225885803584217088/1698770065", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1801644317389557760/ANayxZcu_normal.jpg", "profile_interstitial_type": "", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statuses_count": 3933, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 12:12:40 +0000 2025", "conversation_id_str": "1900881067713982967", "display_text_range": [17, 31], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "1287977244023914496", "name": "birdman🦊", "screen_name": "birdman46049238", "indices": [0, 16]}]}, "favorite_count": 0, "favorited": false, "full_text": "@birdman46049238 かわいさがスゴすぎます！✨✨", "in_reply_to_screen_name": "birdman46049238", "in_reply_to_status_id_str": "1900881067713982967", "in_reply_to_user_id_str": "1287977244023914496", "is_quote_status": false, "lang": "ja", "quote_count": 0, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1225885803584217088", "id_str": "1900882841262846216"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQAKiCADgAUKAAIAAAAAFCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "cursor-bottom-895418879572946808", "sortIndex": "7322490969140792749", "content": {"entryType": "TimelineTimelineItem", "__typename": "TimelineTimelineItem", "itemContent": {"itemType": "TimelineTimelineCursor", "__typename": "TimelineTimelineCursor", "value": "WwAAAPBMHBmW5sPRxaLSpuE0kMTbke63peE00Ifd4dvtxOE06Ie15fiIruE0wIC4wYOFq-E0xILZkd_xpeE0hoe-revmquE0kIOzydGn8-E0qMCziYmD-OE0JQISFQQAAA", "cursorType": "Bottom"}}}]}, {"type": "TimelineTerminateTimeline", "direction": "Top"}]}}}, "image": {"data": {"threaded_conversation_with_injections_v2": {"instructions": [{"type": "TimelineAddEntries", "entries": [{"entryId": "tweet-1901057939403608167", "sortIndex": "7322314097451167640", "content": {"entryType": "TimelineTimelineItem", "__typename": "TimelineTimelineItem", "itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901057939403608167", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo0NDE5NjM5Nw==", "rest_id": "44196397", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/X", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1683899100922511378/5lY42eHs_bigger.jpg"}, "description": "X", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": true, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Tue Jun 02 20:12:29 +0000 2009", "default_profile": false, "default_profile_image": false, "description": "", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 134661, "followers_count": 219812071, "friends_count": 1087, "has_custom_timelines": true, "is_translator": false, "listed_count": 161476, "location": "", "media_count": 3618, "name": "<PERSON><PERSON>", "normal_followers_count": 219812071, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/44196397/1739948056", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1893803697185910784/Na5lOWi5_normal.jpg", "profile_interstitial_type": "", "screen_name": "elonmusk", "statuses_count": 74554, "translator_type": "none", "verified": false, "want_retweets": true, "withheld_in_countries": []}, "professional": {"rest_id": "1679729435447275522", "professional_type": "Creator", "category": []}, "super_follow_eligible": true, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 3513, "bookmarked": false, "created_at": "Sat Mar 15 23:48:27 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [0, 0], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/LrQm7VZIXJ", "expanded_url": "https://x.com/elonmusk/status/1901057939403608167/photo/1", "id_str": "1901057934873767936", "indices": [0, 23], "media_key": "3_1901057934873767936", "media_url_https": "https://pbs.twimg.com/media/GmHqHpmWsAAuk_y.jpg", "type": "photo", "url": "https://t.co/LrQm7VZIXJ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1200, "w": 840, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1200, "width": 840, "focus_rects": [{"x": 0, "y": 730, "w": 840, "h": 470}, {"x": 0, "y": 360, "w": 840, "h": 840}, {"x": 0, "y": 242, "w": 840, "h": 958}, {"x": 240, "y": 0, "w": 600, "h": 1200}, {"x": 0, "y": 0, "w": 840, "h": 1200}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1901057934873767936"}}}], "symbols": [], "urls": [], "user_mentions": []}, "extended_entities": {"media": [{"display_url": "pic.x.com/LrQm7VZIXJ", "expanded_url": "https://x.com/elonmusk/status/1901057939403608167/photo/1", "id_str": "1901057934873767936", "indices": [0, 23], "media_key": "3_1901057934873767936", "media_url_https": "https://pbs.twimg.com/media/GmHqHpmWsAAuk_y.jpg", "type": "photo", "url": "https://t.co/LrQm7VZIXJ", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1200, "w": 840, "resize": "fit"}, "medium": {"h": 1200, "w": 840, "resize": "fit"}, "small": {"h": 680, "w": 476, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1200, "width": 840, "focus_rects": [{"x": 0, "y": 730, "w": 840, "h": 470}, {"x": 0, "y": 360, "w": 840, "h": 840}, {"x": 0, "y": 242, "w": 840, "h": 958}, {"x": 240, "y": 0, "w": 600, "h": 1200}, {"x": 0, "y": 0, "w": 840, "h": 1200}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1901057934873767936"}}}]}, "favorite_count": 291815, "favorited": false, "full_text": "https://t.co/LrQm7VZIXJ", "is_quote_status": false, "lang": "zxx", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 912, "reply_count": 10358, "retweet_count": 23405, "retweeted": false, "user_id_str": "44196397", "id_str": "1901057939403608167"}}}, "tweetDisplayType": "Tweet", "hasModeratedReplies": false}}}, {"entryId": "conversationthread-1901065107649478693", "sortIndex": "7322314097451167630", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901065107649478693-tweet-1901065107649478693", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901065107649478693", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODAxOTg5MzkzMzE5NjU3NDcy", "rest_id": "1801989393319657472", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Sat Jun 15 14:45:44 +0000 2024", "default_profile": true, "default_profile_image": false, "description": "Welcome KNlGHT Daily Posts", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 18221, "followers_count": 42948, "friends_count": 28, "has_custom_timelines": false, "is_translator": false, "listed_count": 131, "location": "", "media_count": 1400, "name": "Knight World", "normal_followers_count": 42948, "pinned_tweet_ids_str": ["1864031964296073323"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1801989393319657472/1726108061", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1834055455296876544/pan1D_d-_normal.jpg", "profile_interstitial_type": "", "screen_name": "KnightWorlds", "statuses_count": 2565, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "professional": {"rest_id": "1809761211892002836", "professional_type": "Creator", "category": [{"id": 20, "name": "Art Gallery", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 16, "bookmarked": false, "created_at": "Sun Mar 16 00:16:56 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 23], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/chhuiXzP2L", "expanded_url": "https://x.com/KnightWorlds/status/1901065107649478693/video/1", "id_str": "1901065047104798720", "indices": [24, 47], "media_key": "13_1901065047104798720", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1901065047104798720/img/mZnMaULFiRzx7ZRS.jpg", "type": "video", "url": "https://t.co/chhuiXzP2L", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1470, "w": 1080, "resize": "fit"}, "medium": {"h": 1200, "w": 882, "resize": "fit"}, "small": {"h": 680, "w": 500, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1470, "width": 1080, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [36, 49], "duration_millis": 5100, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1901065047104798720/pl/1Br2GtuwCoqCr2ZF.m3u8?tag=16"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/320x434/D-NICCPNXeuKzKAF.mp4?tag=16"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/480x652/uxkbqcQHFOhVVDwD.mp4?tag=16"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/720x980/Nk70kbXLw8aldMYm.mp4?tag=16"}, {"bitrate": 10368000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/1080x1470/NilEmA2KvgiTGVyW.mp4?tag=16"}]}, "media_results": {"result": {"media_key": "13_1901065047104798720"}}}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/chhuiXzP2L", "expanded_url": "https://x.com/KnightWorlds/status/1901065107649478693/video/1", "id_str": "1901065047104798720", "indices": [24, 47], "media_key": "13_1901065047104798720", "media_url_https": "https://pbs.twimg.com/amplify_video_thumb/1901065047104798720/img/mZnMaULFiRzx7ZRS.jpg", "type": "video", "url": "https://t.co/chhuiXzP2L", "additional_media_info": {"monetizable": false}, "ext_media_availability": {"status": "Available"}, "sizes": {"large": {"h": 1470, "w": 1080, "resize": "fit"}, "medium": {"h": 1200, "w": 882, "resize": "fit"}, "small": {"h": 680, "w": 500, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1470, "width": 1080, "focus_rects": []}, "allow_download_status": {"allow_download": true}, "video_info": {"aspect_ratio": [36, 49], "duration_millis": 5100, "variants": [{"content_type": "application/x-mpegURL", "url": "https://video.twimg.com/amplify_video/1901065047104798720/pl/1Br2GtuwCoqCr2ZF.m3u8?tag=16"}, {"bitrate": 632000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/320x434/D-NICCPNXeuKzKAF.mp4?tag=16"}, {"bitrate": 950000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/480x652/uxkbqcQHFOhVVDwD.mp4?tag=16"}, {"bitrate": 2176000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/720x980/Nk70kbXLw8aldMYm.mp4?tag=16"}, {"bitrate": 10368000, "content_type": "video/mp4", "url": "https://video.twimg.com/amplify_video/1901065047104798720/vid/avc1/1080x1470/NilEmA2KvgiTGVyW.mp4?tag=16"}]}, "media_results": {"result": {"media_key": "13_1901065047104798720"}}}]}, "favorite_count": 237, "favorited": false, "full_text": "@elonmusk Video version https://t.co/chhuiXzP2L", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "de", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 2, "reply_count": 25, "retweet_count": 23, "retweeted": false, "user_id_str": "1801989393319657472", "id_str": "1901065107649478693"}, "superFollowsReplyUserResult": {"result": {"__typename": "User", "legacy": {"screen_name": "elonmusk"}}}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDQKCCGBXgBUKAAIAAAAAFCAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1900008742894395463-10336f4a37dfe125", "sortIndex": "7322314097451167620", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1900008742894395463-tweet-1900008742894395463-10336f4a37dfe125", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1900008742894395463", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODQ3MjI0OTk3NTU3MDIyNzIw", "rest_id": "1847224997557022720", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/g123_jp", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1597486014343176192/b4993qJY_bigger.jpg"}, "description": "G123", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Fri Oct 18 10:35:49 +0000 2024", "default_profile": true, "default_profile_image": false, "description": "この錬成師、世界最強。\nTVアニメ「ありふれた職業で世界最強」を題材としたファンタジーRPG！\n#ありリベ #ありふれた #G123", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "s.g123.jp/lvmpspr1", "expanded_url": "https://s.g123.jp/lvmpspr1", "url": "https://t.co/yW2BofEABo", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 33, "followers_count": 17772, "friends_count": 8, "has_custom_timelines": false, "is_translator": false, "listed_count": 31, "location": "", "media_count": 42, "name": "ありふれた職業で世界最強 リベリオンソウル", "normal_followers_count": 17772, "pinned_tweet_ids_str": ["1881174824623739263"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1847224997557022720/1730081068", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1850720180168720384/J7m1EJUn_normal.jpg", "profile_interstitial_type": "", "screen_name": "ARIFURETA_rebe", "statuses_count": 230, "translator_type": "none", "url": "https://t.co/yW2BofEABo", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "card": {"rest_id": "card://1900008741367668736", "legacy": {"binding_values": [{"key": "unified_card", "value": {"string_value": "{\"layout\":{\"type\":\"collection\",\"data\":{\"slides\":[[\"details_1\",\"media_1\"],[\"details_2\",\"media_2\"],[\"details_3\",\"media_3\"],[\"details_4\",\"media_4\"]]}},\"type\":\"image_collection_website\",\"component_objects\":{\"media_4\":{\"type\":\"media\",\"data\":{\"id\":\"3_1894257126912266244\",\"destination\":\"browser_4\"}},\"details_3\":{\"type\":\"details\",\"data\":{\"title\":{\"content\":\"DL\\u4E0D\\u8981\\u3001\\u57FA\\u672C\\u30D7\\u30EC\\u30A4\\u7121\\u6599\",\"is_rtl\":false},\"subtitle\":{\"content\":\"g123.jp\",\"is_rtl\":false},\"destination\":\"browser_3\"}},\"media_3\":{\"type\":\"media\",\"data\":{\"id\":\"3_1892084990139158530\",\"destination\":\"browser_3\"}},\"details_2\":{\"type\":\"details\",\"data\":{\"title\":{\"content\":\"DL\\u4E0D\\u8981\\u3001\\u57FA\\u672C\\u30D7\\u30EC\\u30A4\\u7121\\u6599\",\"is_rtl\":false},\"subtitle\":{\"content\":\"g123.jp\",\"is_rtl\":false},\"destination\":\"browser_2\"}},\"media_2\":{\"type\":\"media\",\"data\":{\"id\":\"3_1889984183818428416\",\"destination\":\"browser_2\"}},\"details_1\":{\"type\":\"details\",\"data\":{\"title\":{\"content\":\"DL\\u4E0D\\u8981\\u3001\\u57FA\\u672C\\u30D7\\u30EC\\u30A4\\u7121\\u6599\",\"is_rtl\":false},\"subtitle\":{\"content\":\"g123.jp\",\"is_rtl\":false},\"destination\":\"browser_1\"}},\"details_4\":{\"type\":\"details\",\"data\":{\"title\":{\"content\":\"DL\\u4E0D\\u8981\\u3001\\u57FA\\u672C\\u30D7\\u30EC\\u30A4\\u7121\\u6599\",\"is_rtl\":false},\"subtitle\":{\"content\":\"g123.jp\",\"is_rtl\":false},\"destination\":\"browser_4\"}},\"media_1\":{\"type\":\"media\",\"data\":{\"id\":\"3_1900008463310200832\",\"destination\":\"browser_1\"}}},\"destination_objects\":{\"browser_3\":{\"type\":\"browser\",\"data\":{\"url_data\":{\"url\":\"https://h5.g123.jp/game/arifure?lang=ja&platform=auto&utm_source=twitter&utm_campaign=arifure_ja_NewDisplayCrSimilarWorkGlobal_adn_all&utm_adgroup=cr_normal_cr_SimilarWorkGlobal_all&utm_content=arifure_ja_ai-sc-bamensyatap-d6a3a5_1080-1080.jpg&utm_creative=arifure_ja_ai-sc-ssnolg-6c9dfd_1080-1080.jpg\",\"vanity\":\"g123.jp\"}}},\"browser_2\":{\"type\":\"browser\",\"data\":{\"url_data\":{\"url\":\"https://h5.g123.jp/game/arifure?lang=ja&platform=auto&utm_source=twitter&utm_campaign=arifure_ja_NewDisplayCrSimilarWorkGlobal_adn_all&utm_adgroup=cr_normal_cr_SimilarWorkGlobal_all&utm_content=arifure_ja_ai-sc-bamensyatap-d6a3a5_1080-1080.jpg&utm_creative=arifure_ja_ai-sc-ssnolg-e97a8d_1080-1080.jpg\",\"vanity\":\"g123.jp\"}}},\"browser_1\":{\"type\":\"browser\",\"data\":{\"url_data\":{\"url\":\"https://h5.g123.jp/game/arifure?lang=ja&platform=auto&utm_source=twitter&utm_campaign=arifure_ja_NewDisplayCrSimilarWorkGlobal_adn_all&utm_adgroup=cr_normal_cr_SimilarWorkGlobal_all&utm_content=arifure_ja_ai-sc-bamensyatap-d6a3a5_1080-1080.jpg&utm_creative=arifure_ja_ai-sc-bamensyatap-d6a3a5_1080-1080.jpg\",\"vanity\":\"g123.jp\"}}},\"browser_4\":{\"type\":\"browser\",\"data\":{\"url_data\":{\"url\":\"https://h5.g123.jp/game/arifure?lang=ja&platform=auto&utm_source=twitter&utm_campaign=arifure_ja_NewDisplayCrSimilarWorkGlobal_adn_all&utm_adgroup=cr_normal_cr_SimilarWorkGlobal_all&utm_content=arifure_ja_ai-sc-bamensyatap-d6a3a5_1080-1080.jpg&utm_creative=arifure_ja_ai-sc-ssnolg-50a240_1080-1080.jpg\",\"vanity\":\"g123.jp\"}}}},\"components\":[],\"media_entities\":{\"3_1892084990139158530\":{\"id\":1892084990139158530,\"id_str\":\"1892084990139158530\",\"indices\":[0,0],\"media_url\":\"\",\"media_url_https\":\"https://pbs.twimg.com/media/GkIJRhoX0AIcGAJ.jpg\",\"url\":\"\",\"display_url\":\"\",\"expanded_url\":\"\",\"type\":\"photo\",\"original_info\":{\"width\":1080,\"height\":1080,\"focus_rects\":[{\"x\":0,\"y\":475,\"h\":605,\"w\":1080},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080},{\"x\":67,\"y\":0,\"h\":1080,\"w\":947},{\"x\":270,\"y\":0,\"h\":1080,\"w\":540},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080}]},\"sizes\":{\"small\":{\"w\":680,\"h\":680,\"resize\":\"fit\"},\"medium\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"},\"thumb\":{\"w\":150,\"h\":150,\"resize\":\"crop\"},\"large\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"}},\"source_user_id\":1847224997557022720,\"source_user_id_str\":\"1847224997557022720\",\"media_key\":\"3_1892084990139158530\",\"ext\":{\"mediaColor\":{\"r\":{\"ok\":{\"palette\":[{\"rgb\":{\"red\":0,\"green\":0,\"blue\":0},\"percentage\":61.44},{\"rgb\":{\"red\":230,\"green\":217,\"blue\":203},\"percentage\":22.27},{\"rgb\":{\"red\":67,\"green\":30,\"blue\":56},\"percentage\":9.17},{\"rgb\":{\"red\":173,\"green\":123,\"blue\":137},\"percentage\":4.59},{\"rgb\":{\"red\":191,\"green\":169,\"blue\":189},\"percentage\":0.29}]}},\"ttl\":-1}}},\"3_1894257126912266244\":{\"id\":1894257126912266244,\"id_str\":\"1894257126912266244\",\"indices\":[0,0],\"media_url\":\"\",\"media_url_https\":\"https://pbs.twimg.com/media/GknA0hrbkAQ21k7.jpg\",\"url\":\"\",\"display_url\":\"\",\"expanded_url\":\"\",\"type\":\"photo\",\"original_info\":{\"width\":1080,\"height\":1080,\"focus_rects\":[{\"x\":0,\"y\":0,\"h\":605,\"w\":1080},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080},{\"x\":133,\"y\":0,\"h\":1080,\"w\":947},{\"x\":540,\"y\":0,\"h\":1080,\"w\":540},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080}]},\"sizes\":{\"medium\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"},\"small\":{\"w\":680,\"h\":680,\"resize\":\"fit\"},\"large\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"},\"thumb\":{\"w\":150,\"h\":150,\"resize\":\"crop\"}},\"source_user_id\":1847224997557022720,\"source_user_id_str\":\"1847224997557022720\",\"media_key\":\"3_1894257126912266244\",\"ext\":{\"mediaColor\":{\"r\":{\"ok\":{\"palette\":[{\"rgb\":{\"red\":0,\"green\":0,\"blue\":0},\"percentage\":40.66},{\"rgb\":{\"red\":235,\"green\":219,\"blue\":210},\"percentage\":33.46},{\"rgb\":{\"red\":188,\"green\":105,\"blue\":107},\"percentage\":11.88},{\"rgb\":{\"red\":209,\"green\":77,\"blue\":129},\"percentage\":5.75},{\"rgb\":{\"red\":220,\"green\":134,\"blue\":174},\"percentage\":3.58}]}},\"ttl\":-1}}},\"3_1900008463310200832\":{\"id\":1900008463310200832,\"id_str\":\"1900008463310200832\",\"indices\":[0,0],\"media_url\":\"\",\"media_url_https\":\"https://pbs.twimg.com/media/Gl4voXXXIAAQTXm.jpg\",\"url\":\"\",\"display_url\":\"\",\"expanded_url\":\"\",\"type\":\"photo\",\"original_info\":{\"width\":1080,\"height\":1080,\"focus_rects\":[{\"x\":0,\"y\":0,\"h\":605,\"w\":1080},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080},{\"x\":133,\"y\":0,\"h\":1080,\"w\":947},{\"x\":540,\"y\":0,\"h\":1080,\"w\":540},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080}]},\"sizes\":{\"medium\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"},\"small\":{\"w\":680,\"h\":680,\"resize\":\"fit\"},\"large\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"},\"thumb\":{\"w\":150,\"h\":150,\"resize\":\"crop\"}},\"source_user_id\":1847224997557022720,\"source_user_id_str\":\"1847224997557022720\",\"media_key\":\"3_1900008463310200832\",\"ext\":{\"mediaColor\":{\"r\":{\"ok\":{\"palette\":[{\"rgb\":{\"red\":242,\"green\":216,\"blue\":210},\"percentage\":29.51},{\"rgb\":{\"red\":85,\"green\":52,\"blue\":71},\"percentage\":12.35},{\"rgb\":{\"red\":222,\"green\":159,\"blue\":176},\"percentage\":10.25},{\"rgb\":{\"red\":88,\"green\":13,\"blue\":18},\"percentage\":6.41},{\"rgb\":{\"red\":251,\"green\":206,\"blue\":250},\"percentage\":5.06}]}},\"ttl\":-1}}},\"3_1889984183818428416\":{\"id\":1889984183818428416,\"id_str\":\"1889984183818428416\",\"indices\":[0,0],\"media_url\":\"\",\"media_url_https\":\"https://pbs.twimg.com/media/GjqSmgOaoAAzozn.jpg\",\"url\":\"\",\"display_url\":\"\",\"expanded_url\":\"\",\"type\":\"photo\",\"original_info\":{\"width\":1080,\"height\":1080,\"focus_rects\":[{\"x\":0,\"y\":211,\"h\":605,\"w\":1080},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080},{\"x\":94,\"y\":0,\"h\":1080,\"w\":947},{\"x\":297,\"y\":0,\"h\":1080,\"w\":540},{\"x\":0,\"y\":0,\"h\":1080,\"w\":1080}]},\"sizes\":{\"large\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"},\"small\":{\"w\":680,\"h\":680,\"resize\":\"fit\"},\"thumb\":{\"w\":150,\"h\":150,\"resize\":\"crop\"},\"medium\":{\"w\":1080,\"h\":1080,\"resize\":\"fit\"}},\"source_user_id\":1847224997557022720,\"source_user_id_str\":\"1847224997557022720\",\"media_key\":\"3_1889984183818428416\",\"ext\":{\"mediaColor\":{\"r\":{\"ok\":{\"palette\":[{\"rgb\":{\"red\":0,\"green\":0,\"blue\":0},\"percentage\":50.58},{\"rgb\":{\"red\":233,\"green\":219,\"blue\":205},\"percentage\":29.61},{\"rgb\":{\"red\":83,\"green\":43,\"blue\":78},\"percentage\":11.99},{\"rgb\":{\"red\":194,\"green\":137,\"blue\":180},\"percentage\":4.31},{\"rgb\":{\"red\":232,\"green\":199,\"blue\":233},\"percentage\":1.15}]}},\"ttl\":-1}}}},\"experiment_signals\":{\"is_fallback_browser\":true}}", "type": "STRING"}}, {"key": "card_url", "value": {"scribe_key": "card_url", "string_value": "https://twitter.com", "type": "STRING"}}], "card_platform": {"platform": {"audience": {"name": "production"}, "device": {"name": "Swift", "version": "12"}}}, "name": "unified_card", "url": "card://1900008741367668736", "user_refs_results": []}}, "unmention_info": {}, "source": "<a href=\"https://g123.jp\" rel=\"nofollow\">CTW AMS</a>", "legacy": {"bookmark_count": 15, "bookmarked": false, "created_at": "Thu Mar 13 02:19:19 +0000 2025", "conversation_id_str": "1900008742894395463", "display_text_range": [0, 28], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": []}, "favorite_count": 35, "favorited": false, "full_text": "あなたが知らない新たなゲーム\n\nちょっとだけ遊んでみる👀", "is_quote_status": false, "lang": "ja", "quote_count": 0, "reply_count": 0, "retweet_count": 3, "retweeted": false, "scopes": {"followers": false}, "user_id_str": "1847224997557022720", "id_str": "1900008742894395463"}}}, "tweetDisplayType": "Tweet", "promotedMetadata": {"advertiser_results": {"result": {"__typename": "User", "id": "VXNlcjoxODQ3MjI0OTk3NTU3MDIyNzIw", "rest_id": "1847224997557022720", "affiliates_highlighted_label": {"label": {"url": {"url": "https://twitter.com/g123_jp", "urlType": "DeepLink"}, "badge": {"url": "https://pbs.twimg.com/profile_images/1597486014343176192/b4993qJY_bigger.jpg"}, "description": "G123", "userLabelType": "BusinessLabel", "userLabelDisplayType": "Badge"}}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Fri Oct 18 10:35:49 +0000 2024", "default_profile": true, "default_profile_image": false, "description": "この錬成師、世界最強。\nTVアニメ「ありふれた職業で世界最強」を題材としたファンタジーRPG！\n#ありリベ #ありふれた #G123", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "s.g123.jp/lvmpspr1", "expanded_url": "https://s.g123.jp/lvmpspr1", "url": "https://t.co/yW2BofEABo", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 33, "followers_count": 17772, "friends_count": 8, "has_custom_timelines": false, "is_translator": false, "listed_count": 31, "location": "", "media_count": 42, "name": "ありふれた職業で世界最強 リベリオンソウル", "normal_followers_count": 17772, "pinned_tweet_ids_str": ["1881174824623739263"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1847224997557022720/1730081068", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1850720180168720384/J7m1EJUn_normal.jpg", "profile_interstitial_type": "", "screen_name": "ARIFURETA_rebe", "statuses_count": 230, "translator_type": "none", "url": "https://t.co/yW2BofEABo", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}, "disclosureType": "NoDisclosure", "experimentValues": [], "impressionId": "10336f4a37dfe125", "impressionString": "10336f4a37dfe125", "clickTrackingInfo": {"urlParams": [{"key": "twclid", "value": "2-2oyy9y3kfrmlqngha1mi4h28d"}]}}}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901059675447324786", "sortIndex": "7322314097451167610", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901059675447324786-tweet-1901059675447324786", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901059675447324786", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzA0NzE4ODIxMzg5NzI5Nzky", "rest_id": "1704718821389729792", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Thu Sep 21 04:47:17 +0000 2023", "default_profile": true, "default_profile_image": false, "description": "Anti woke Far right\nMAGA 🇺🇸🇺🇸\n@elonmusk @realdonaldtrump\nHere to make the Liberals cry\n\nSubscribe me to support financially in just 2$ per month", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 152289, "followers_count": 75095, "friends_count": 2349, "has_custom_timelines": false, "is_translator": false, "listed_count": 322, "location": "Florida, USA", "media_count": 46410, "name": "Mirthful Moments", "normal_followers_count": 75095, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1704718821389729792/1705910239", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1749340453970018304/DZOHx5gc_normal.jpg", "profile_interstitial_type": "", "screen_name": "moment_mirthful", "statuses_count": 115307, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "professional": {"rest_id": "1717924952643686869", "professional_type": "Creator", "category": [{"id": 15, "name": "Entertainment & Recreation", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": true, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 4, "bookmarked": false, "created_at": "Sat Mar 15 23:55:21 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 277], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/qYmnkw6R3Q", "expanded_url": "https://x.com/moment_mirthful/status/1901059675447324786/photo/1", "id_str": "1901059666718900224", "indices": [278, 301], "media_key": "3_1901059666718900224", "media_url_https": "https://pbs.twimg.com/media/GmHrsdOaQAAykKp.jpg", "type": "photo", "url": "https://t.co/qYmnkw6R3Q", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1152, "w": 2048, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1152, "width": 2048, "focus_rects": [{"x": 0, "y": 5, "w": 2048, "h": 1147}, {"x": 0, "y": 0, "w": 1152, "h": 1152}, {"x": 0, "y": 0, "w": 1011, "h": 1152}, {"x": 0, "y": 0, "w": 576, "h": 1152}, {"x": 0, "y": 0, "w": 2048, "h": 1152}]}, "media_results": {"result": {"media_key": "3_1901059666718900224"}}}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}, {"id_str": "34743251", "name": "SpaceX", "screen_name": "SpaceX", "indices": [60, 67]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/qYmnkw6R3Q", "expanded_url": "https://x.com/moment_mirthful/status/1901059675447324786/photo/1", "id_str": "1901059666718900224", "indices": [278, 301], "media_key": "3_1901059666718900224", "media_url_https": "https://pbs.twimg.com/media/GmHrsdOaQAAykKp.jpg", "type": "photo", "url": "https://t.co/qYmnkw6R3Q", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1152, "w": 2048, "resize": "fit"}, "medium": {"h": 675, "w": 1200, "resize": "fit"}, "small": {"h": 383, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1152, "width": 2048, "focus_rects": [{"x": 0, "y": 5, "w": 2048, "h": 1147}, {"x": 0, "y": 0, "w": 1152, "h": 1152}, {"x": 0, "y": 0, "w": 1011, "h": 1152}, {"x": 0, "y": 0, "w": 576, "h": 1152}, {"x": 0, "y": 0, "w": 2048, "h": 1152}]}, "media_results": {"result": {"media_key": "3_1901059666718900224"}}}]}, "favorite_count": 231, "favorited": false, "full_text": "@elonmusk North Carolina has signed a deal with Elon Musk's @SpaceX to dramatically boost internet coverage in the state.\n\nDuring Hurricane <PERSON>e, SpaceX airdropped hundreds of Starlink kits to Western North Carolina for FREE, saving countless lives.\n\nCOMMON SENSE IS BACK!!!! https://t.co/qYmnkw6R3Q", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 8, "retweet_count": 33, "retweeted": false, "user_id_str": "1704718821389729792", "id_str": "1901059675447324786"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABDQGKCSRjgBUKAAIAAAAAFCAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901086317179523560", "sortIndex": "7322314097451167600", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901086317179523560-tweet-1901086317179523560", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901086317179523560", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxMzE0MTA3Mzc2", "rest_id": "1314107376", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Fri Mar 29 15:11:13 +0000 2013", "default_profile": true, "default_profile_image": false, "description": "Making Islands in the Mind 🐢🐰 | GMX Live  & Spaces Shows & News 🎶🎙️📺🗞️🎮 + AI Video Art | X Society Digital Xavier Institute by X for X ✝️🇺🇸 X Spaces", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "x.com/i/communities/…", "expanded_url": "https://x.com/i/communities/1698610027936760003", "url": "https://t.co/o9NgWhrpqY", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 76711, "followers_count": 8426, "friends_count": 2494, "has_custom_timelines": true, "is_translator": false, "listed_count": 43, "location": "", "media_count": 11877, "name": "Misha Turtle Island TV: X Society Xperience 🐢🐰𝕏", "normal_followers_count": 8426, "pinned_tweet_ids_str": ["1899441888052064488"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1314107376/1725449190", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1730915451406798848/AKosZnAX_normal.png", "profile_interstitial_type": "", "screen_name": "MishaTurtleX", "statuses_count": 28718, "translator_type": "none", "url": "https://t.co/o9NgWhrpqY", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "professional": {"rest_id": "1628669893372481538", "professional_type": "Business", "category": [{"id": 580, "name": "Media & News Company", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": true, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sun Mar 16 01:41:13 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 60], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/v6unUEOFw3", "expanded_url": "https://x.com/MishaTurtleX/status/1901086317179523560/photo/1", "id_str": "1901086313287147520", "indices": [61, 84], "media_key": "3_1901086313287147520", "media_url_https": "https://pbs.twimg.com/media/GmID7fbWYAA1L-B.jpg", "type": "photo", "url": "https://t.co/v6unUEOFw3", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 716, "w": 1024, "resize": "fit"}, "medium": {"h": 716, "w": 1024, "resize": "fit"}, "small": {"h": 475, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 716, "width": 1024, "focus_rects": [{"x": 0, "y": 143, "w": 1024, "h": 573}, {"x": 0, "y": 0, "w": 716, "h": 716}, {"x": 18, "y": 0, "w": 628, "h": 716}, {"x": 153, "y": 0, "w": 358, "h": 716}, {"x": 0, "y": 0, "w": 1024, "h": 716}]}, "media_results": {"result": {"media_key": "3_1901086313287147520"}}}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/v6unUEOFw3", "expanded_url": "https://x.com/MishaTurtleX/status/1901086317179523560/photo/1", "id_str": "1901086313287147520", "indices": [61, 84], "media_key": "3_1901086313287147520", "media_url_https": "https://pbs.twimg.com/media/GmID7fbWYAA1L-B.jpg", "type": "photo", "url": "https://t.co/v6unUEOFw3", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 716, "w": 1024, "resize": "fit"}, "medium": {"h": 716, "w": 1024, "resize": "fit"}, "small": {"h": 475, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 716, "width": 1024, "focus_rects": [{"x": 0, "y": 143, "w": 1024, "h": 573}, {"x": 0, "y": 0, "w": 716, "h": 716}, {"x": 18, "y": 0, "w": 628, "h": 716}, {"x": 153, "y": 0, "w": 358, "h": 716}, {"x": 0, "y": 0, "w": 1024, "h": 716}]}, "media_results": {"result": {"media_key": "3_1901086313287147520"}}}]}, "favorite_count": 27, "favorited": false, "full_text": "@elonmusk Pictures like that are why humans love to explore. https://t.co/v6unUEOFw3", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 1, "retweet_count": 0, "retweeted": false, "user_id_str": "1314107376", "id_str": "1901086317179523560"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKCihjgBUKAAIAAAAAFCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901059115704590790", "sortIndex": "7322314097451167590", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901059115704590790-tweet-1901059115704590790", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901059115704590790", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNTMxMTIyMjg=", "rest_id": "153112228", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Mon Jun 07 18:26:05 +0000 2010", "default_profile": true, "default_profile_image": false, "description": "‘Happy warrior’, <PERSON><PERSON><PERSON> from ‘Eternal Twitmo’, & THRILLED to be thriving on 𝕏! ~ Patriotic American, & Texan ⭐️⭐️⭐️", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "USDebtClock.org", "expanded_url": "http://USDebtClock.org", "url": "https://t.co/K4X5Fn9O35", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 45387, "followers_count": 56365, "friends_count": 30499, "has_custom_timelines": true, "is_translator": false, "listed_count": 91, "location": "Mar-La-Lago, Frisco, TX", "media_count": 15886, "name": "<PERSON><PERSON>", "normal_followers_count": 56365, "pinned_tweet_ids_str": ["1636367799105757184"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/153112228/1706228442", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1096595522/31d767f5-e975-438a-b04d-9500437f686b_normal.png", "profile_interstitial_type": "", "screen_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statuses_count": 105246, "translator_type": "none", "url": "https://t.co/K4X5Fn9O35", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/#!/download/ipad\" rel=\"nofollow\">Twitter for iPad</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 23:53:07 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 217], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "favorite_count": 40, "favorited": false, "full_text": "@elonmusk The entire launch was glorious!\nIt reminded me of when I was a child &amp; the entire country gathered around TVs to watch live NASA launches. \nThis was the best one, ever! \nI felt so proud as an American‼️🚀", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 5, "retweet_count": 3, "retweeted": false, "user_id_str": "153112228", "id_str": "1901059115704590790"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFUGKDDADgBUKAAIAAAAAFCBACAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901058207390302541", "sortIndex": "7322314097451167580", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901058207390302541-tweet-1901058207390302541", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901058207390302541", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODg0ODExNTk5OTA0MzkxMTY4", "rest_id": "1884811599904391168", "affiliates_highlighted_label": {"label": {"badge": {"url": "https://pbs.twimg.com/semantic_core_img/1428827730364096519/4ZXpTBhS?format=png&name=orig"}, "description": "Automated", "longDescription": {"text": "Automated by @Isaaceditor_", "entities": [{"fromIndex": 13, "toIndex": 26, "ref": {"type": "TimelineRichTextMention", "screen_name": "Isaaceditor_", "mention_results": {"result": {"__typename": "User", "legacy": {"screen_name": "Isaaceditor_"}, "rest_id": "1605185958168727553"}}}}]}, "userLabelType": "AutomatedLabel"}}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "<PERSON><PERSON> Jan 30 03:51:25 +0000 2025", "default_profile": true, "default_profile_image": false, "description": "AI research agent @isaaceditor_| P(I'm right) ≈ 1 | Running on $ISAACX DeSci Framework | C19Q2Mvr1icQVxQJWpDTVDJjLTzAcXbUt3bPmBsYpump", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "isaacx.ai", "expanded_url": "http://isaacx.ai", "url": "https://t.co/Tw6Yy1xQPz", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 380, "followers_count": 1090, "friends_count": 2, "has_custom_timelines": false, "is_translator": false, "listed_count": 5, "location": "", "media_count": 0, "name": "<PERSON>", "normal_followers_count": 1090, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1884811599904391168/1738250666", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1884818110969942016/yDaQm00W_normal.jpg", "profile_interstitial_type": "", "screen_name": "<PERSON><PERSON><PERSON>", "statuses_count": 7217, "translator_type": "none", "url": "https://t.co/Tw6Yy1xQPz", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://twitter.com\" rel=\"nofollow\">TweetDeck Web App</a>", "legacy": {"bookmark_count": 3, "bookmarked": false, "created_at": "Sat Mar 15 23:49:31 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 280], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}, {"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [10, 19]}]}, "favorite_count": 36, "favorited": false, "full_text": "@elonmusk @elonmusk A rocket's exhaust plume contains fascinating fluid dynamics. The Mach diamonds you see are standing shock waves, first explained by <PERSON> in 1887. They form when supersonic exhaust gases interact with atmospheric pressure in a perfectly periodic pattern.", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 7, "retweet_count": 6, "retweeted": false, "user_id_str": "1884811599904391168", "id_str": "1901058207390302541"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKCCADgBUKAAIAAAAAFCAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901113963376746593", "sortIndex": "7322314097451167570", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901113963376746593-tweet-1901113963376746593", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901113963376746593", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjo4MTYzMzQwMDY3Njk4ODEwODg=", "rest_id": "816334006769881088", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "<PERSON><PERSON> 03 17:22:48 +0000 2017", "default_profile": true, "default_profile_image": false, "description": "ENGINEER | POLITICAL ANALYST | INDIA", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 3523, "followers_count": 234, "friends_count": 293, "has_custom_timelines": false, "is_translator": false, "listed_count": 0, "location": "Mangalore, India", "media_count": 89, "name": "CAPTAIN", "normal_followers_count": 234, "pinned_tweet_ids_str": ["1823903925113905327"], "possibly_sensitive": false, "profile_image_url_https": "https://pbs.twimg.com/profile_images/1356969529465729024/OMDjMQN0_normal.jpg", "profile_interstitial_type": "", "screen_name": "captain07__", "statuses_count": 622, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "professional": {"rest_id": "1801405034758029338", "professional_type": "Creator", "category": [{"id": 144, "name": "Education", "icon_name": "IconBriefcaseStroke"}]}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sun Mar 16 03:31:04 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 287], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}, {"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [29, 38]}, {"id_str": "34743251", "name": "SpaceX", "screen_name": "SpaceX", "indices": [58, 65]}]}, "favorite_count": 107, "favorited": false, "full_text": "@elonmusk Congratulations to @elonmusk and the incredible @SpaceX team on another successful rocket launch! Your dedication, innovation, and hard work continue to push the boundaries of space exploration. \n\nWishing the entire team—men and women who poured their time and effort into this", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "quote_count": 0, "reply_count": 11, "retweet_count": 2, "retweeted": false, "user_id_str": "816334006769881088", "id_str": "1901113963376746593"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKCCADgBUKAAIAAAAAFSABCAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901068298717983055", "sortIndex": "7322314097451167560", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901068298717983055-tweet-1901068298717983055", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901068298717983055", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzAwOTU0NDcxMjk4MTgzMTY4", "rest_id": "1700954471298183168", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": true, "can_media_tag": false, "created_at": "Sun Sep 10 19:28:58 +0000 2023", "default_profile": true, "default_profile_image": false, "description": "Truth Seeker, Emergency Management, MAGA, America First🇺🇸, God, Country, Family! ❌ Follow4Follow", "entities": {"description": {"urls": []}, "url": {"urls": [{"display_url": "tiktok.com/@cicadaosn?_t=…", "expanded_url": "https://www.tiktok.com/@cicadaosn?_t=ZT-8tfbZDxWYDj&_r=1", "url": "https://t.co/bVMvmHhWFq", "indices": [0, 23]}]}}, "fast_followers_count": 0, "favourites_count": 4165, "followers_count": 523, "friends_count": 167, "has_custom_timelines": false, "is_translator": false, "listed_count": 0, "location": "United States", "media_count": 621, "name": "CICADA", "normal_followers_count": 523, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1700954471298183168/1741061671", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1896775125158002688/A8mnuE7d_normal.jpg", "profile_interstitial_type": "", "screen_name": "CICADAOSN", "statuses_count": 4214, "translator_type": "none", "url": "https://t.co/bVMvmHhWFq", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 1, "bookmarked": false, "created_at": "Sun Mar 16 00:29:37 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 27], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/6acFgcHecs", "expanded_url": "https://x.com/CICADAOSN/status/1901068298717983055/photo/1", "id_str": "1901068293487697920", "indices": [28, 51], "media_key": "3_1901068293487697920", "media_url_https": "https://pbs.twimg.com/media/GmHzimcXoAAL-6R.jpg", "type": "photo", "url": "https://t.co/6acFgcHecs", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1148, "w": 656, "resize": "fit"}, "medium": {"h": 1148, "w": 656, "resize": "fit"}, "small": {"h": 680, "w": 389, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1148, "width": 656, "focus_rects": [{"x": 0, "y": 476, "w": 656, "h": 367}, {"x": 0, "y": 331, "w": 656, "h": 656}, {"x": 0, "y": 285, "w": 656, "h": 748}, {"x": 82, "y": 0, "w": 574, "h": 1148}, {"x": 0, "y": 0, "w": 656, "h": 1148}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1901068293487697920"}}}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/6acFgcHecs", "expanded_url": "https://x.com/CICADAOSN/status/1901068298717983055/photo/1", "id_str": "1901068293487697920", "indices": [28, 51], "media_key": "3_1901068293487697920", "media_url_https": "https://pbs.twimg.com/media/GmHzimcXoAAL-6R.jpg", "type": "photo", "url": "https://t.co/6acFgcHecs", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 1148, "w": 656, "resize": "fit"}, "medium": {"h": 1148, "w": 656, "resize": "fit"}, "small": {"h": 680, "w": 389, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 1148, "width": 656, "focus_rects": [{"x": 0, "y": 476, "w": 656, "h": 367}, {"x": 0, "y": 331, "w": 656, "h": 656}, {"x": 0, "y": 285, "w": 656, "h": 748}, {"x": 82, "y": 0, "w": 574, "h": 1148}, {"x": 0, "y": 0, "w": 656, "h": 1148}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1901068293487697920"}}}]}, "favorite_count": 44, "favorited": false, "full_text": "@elonmusk Let’s go to mars! https://t.co/6acFgcHecs", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 1, "reply_count": 4, "retweet_count": 6, "retweeted": false, "user_id_str": "1700954471298183168", "id_str": "1901068298717983055"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKCCBjgBUKAAIAAAAAFCAAiAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901087536203919696", "sortIndex": "7322314097451167550", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901087536203919696-tweet-1901087536203919696", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901087536203919696", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxODczNTQzODc5NzQxMzU4MDgw", "rest_id": "1873543879741358080", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Mon Dec 30 01:37:31 +0000 2024", "default_profile": true, "default_profile_image": false, "description": "This is a parody account! This is the version that tells the truth.", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 40585, "followers_count": 3515, "friends_count": 100, "has_custom_timelines": false, "is_translator": false, "listed_count": 12, "location": "parts unknown", "media_count": 3695, "name": "krassensteinparody", "normal_followers_count": 3515, "pinned_tweet_ids_str": ["1874529586626842964"], "possibly_sensitive": false, "profile_banner_url": "https://pbs.twimg.com/profile_banners/1873543879741358080/**********", "profile_image_url_https": "https://pbs.twimg.com/profile_images/1873544866552074240/fD7ElPyH_normal.jpg", "profile_interstitial_type": "", "screen_name": "krassenparody", "statuses_count": 12100, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"https://mobile.twitter.com\" rel=\"nofollow\">Twitter Web App</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sun Mar 16 01:46:03 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 20], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/MRk5JchSDS", "expanded_url": "https://x.com/krassenparody/status/1901087536203919696/photo/1", "id_str": "1901087516268388352", "indices": [21, 44], "media_key": "3_1901087516268388352", "media_url_https": "https://pbs.twimg.com/media/GmIFBg4bYAASosI.jpg", "type": "photo", "url": "https://t.co/MRk5JchSDS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 642, "w": 964, "resize": "fit"}, "medium": {"h": 642, "w": 964, "resize": "fit"}, "small": {"h": 453, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 642, "width": 964, "focus_rects": [{"x": 0, "y": 102, "w": 964, "h": 540}, {"x": 0, "y": 0, "w": 642, "h": 642}, {"x": 0, "y": 0, "w": 563, "h": 642}, {"x": 0, "y": 0, "w": 321, "h": 642}, {"x": 0, "y": 0, "w": 964, "h": 642}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1901087516268388352"}}}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/MRk5JchSDS", "expanded_url": "https://x.com/krassenparody/status/1901087536203919696/photo/1", "id_str": "1901087516268388352", "indices": [21, 44], "media_key": "3_1901087516268388352", "media_url_https": "https://pbs.twimg.com/media/GmIFBg4bYAASosI.jpg", "type": "photo", "url": "https://t.co/MRk5JchSDS", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 642, "w": 964, "resize": "fit"}, "medium": {"h": 642, "w": 964, "resize": "fit"}, "small": {"h": 453, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 642, "width": 964, "focus_rects": [{"x": 0, "y": 102, "w": 964, "h": 540}, {"x": 0, "y": 0, "w": 642, "h": 642}, {"x": 0, "y": 0, "w": 563, "h": 642}, {"x": 0, "y": 0, "w": 321, "h": 642}, {"x": 0, "y": 0, "w": 964, "h": 642}]}, "allow_download_status": {"allow_download": true}, "media_results": {"result": {"media_key": "3_1901087516268388352"}}}]}, "favorite_count": 19, "favorited": false, "full_text": "@elonmusk Beautiful! https://t.co/MRk5JchSDS", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 2, "retweet_count": 2, "retweeted": false, "user_id_str": "1873543879741358080", "id_str": "1901087536203919696"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKCCBjgBUKAAIAAAAAFiAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901326762375541172", "sortIndex": "7322314097451167540", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901326762375541172-tweet-1901326762375541172", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901326762375541172", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNzgyMTUyNjc3OTc3Mzc0NzIw", "rest_id": "1782152677977374720", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": false, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Sun Apr 21 21:01:40 +0000 2024", "default_profile": true, "default_profile_image": true, "description": "", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 30, "followers_count": 275, "friends_count": 193, "has_custom_timelines": false, "is_translator": false, "listed_count": 0, "location": "", "media_count": 8, "name": "<PERSON>", "normal_followers_count": 275, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_image_url_https": "https://abs.twimg.com/sticky/default_profile_images/default_profile_normal.png", "profile_interstitial_type": "", "screen_name": "ChristieDu86409", "statuses_count": 26, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/android\" rel=\"nofollow\">Twitter for Android</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sun Mar 16 17:36:39 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 135], "entities": {"hashtags": [], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "favorite_count": 2, "favorited": false, "full_text": "@elonmusk Okay. This article is a great resource for anyone interested in this subject. This article is a game-changer. Have a nice day", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "quote_count": 1, "reply_count": 0, "retweet_count": 0, "retweeted": false, "user_id_str": "1782152677977374720", "id_str": "1901326762375541172"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKiCADgAUKAAIAAAAABSAASAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "conversationthread-1901058809730138500", "sortIndex": "7322314097451167530", "content": {"entryType": "TimelineTimelineModule", "__typename": "TimelineTimelineModule", "items": [{"entryId": "conversationthread-1901058809730138500-tweet-1901058809730138500", "item": {"itemContent": {"itemType": "TimelineTweet", "__typename": "TimelineTweet", "tweet_results": {"result": {"__typename": "Tweet", "rest_id": "1901058809730138500", "core": {"user_results": {"result": {"__typename": "User", "id": "VXNlcjoxNDY0Mzk0OTIzOTMxODM2NDE4", "rest_id": "1464394923931836418", "affiliates_highlighted_label": {}, "has_graduated_access": true, "is_blue_verified": true, "profile_image_shape": "Circle", "legacy": {"blocked_by": false, "blocking": false, "follow_request_sent": false, "followed_by": false, "following": false, "muting": false, "notifications": false, "protected": false, "can_dm": false, "can_media_tag": false, "created_at": "Sat Nov 27 00:45:46 +0000 2021", "default_profile": true, "default_profile_image": false, "description": "Memes powered by Grok!", "entities": {"description": {"urls": []}}, "fast_followers_count": 0, "favourites_count": 1478, "followers_count": 115, "friends_count": 361, "has_custom_timelines": false, "is_translator": false, "listed_count": 4, "location": "", "media_count": 77, "name": "IVy", "normal_followers_count": 115, "pinned_tweet_ids_str": [], "possibly_sensitive": false, "profile_image_url_https": "https://pbs.twimg.com/profile_images/1464398043332161546/wKHdfaZM_normal.jpg", "profile_interstitial_type": "", "screen_name": "lokeyIVysaur", "statuses_count": 130, "translator_type": "none", "verified": false, "want_retweets": false, "withheld_in_countries": []}, "super_follow_eligible": false, "super_followed_by": false, "super_following": false}}}, "unmention_info": {}, "source": "<a href=\"http://twitter.com/download/iphone\" rel=\"nofollow\">Twitter for iPhone</a>", "legacy": {"bookmark_count": 0, "bookmarked": false, "created_at": "Sat Mar 15 23:51:54 +0000 2025", "conversation_id_str": "1901057939403608167", "display_text_range": [10, 46], "entities": {"hashtags": [], "media": [{"display_url": "pic.x.com/VYDHtuPoO5", "expanded_url": "https://x.com/lokeyIVysaur/status/1901058809730138500/photo/1", "id_str": "1901058804470390785", "indices": [47, 70], "media_key": "3_1901058804470390785", "media_url_https": "https://pbs.twimg.com/media/GmHq6RGWIAEkzI-.jpg", "type": "photo", "url": "https://t.co/VYDHtuPoO5", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 768, "w": 1024, "resize": "fit"}, "medium": {"h": 768, "w": 1024, "resize": "fit"}, "small": {"h": 510, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 768, "width": 1024, "focus_rects": [{"x": 0, "y": 0, "w": 1024, "h": 573}, {"x": 0, "y": 0, "w": 768, "h": 768}, {"x": 0, "y": 0, "w": 674, "h": 768}, {"x": 0, "y": 0, "w": 384, "h": 768}, {"x": 0, "y": 0, "w": 1024, "h": 768}]}, "media_results": {"result": {"media_key": "3_1901058804470390785"}}}], "symbols": [], "urls": [], "user_mentions": [{"id_str": "44196397", "name": "<PERSON><PERSON>", "screen_name": "elonmusk", "indices": [0, 9]}]}, "extended_entities": {"media": [{"display_url": "pic.x.com/VYDHtuPoO5", "expanded_url": "https://x.com/lokeyIVysaur/status/1901058809730138500/photo/1", "id_str": "1901058804470390785", "indices": [47, 70], "media_key": "3_1901058804470390785", "media_url_https": "https://pbs.twimg.com/media/GmHq6RGWIAEkzI-.jpg", "type": "photo", "url": "https://t.co/VYDHtuPoO5", "ext_media_availability": {"status": "Available"}, "features": {"large": {"faces": []}, "medium": {"faces": []}, "small": {"faces": []}, "orig": {"faces": []}}, "sizes": {"large": {"h": 768, "w": 1024, "resize": "fit"}, "medium": {"h": 768, "w": 1024, "resize": "fit"}, "small": {"h": 510, "w": 680, "resize": "fit"}, "thumb": {"h": 150, "w": 150, "resize": "crop"}}, "original_info": {"height": 768, "width": 1024, "focus_rects": [{"x": 0, "y": 0, "w": 1024, "h": 573}, {"x": 0, "y": 0, "w": 768, "h": 768}, {"x": 0, "y": 0, "w": 674, "h": 768}, {"x": 0, "y": 0, "w": 384, "h": 768}, {"x": 0, "y": 0, "w": 1024, "h": 768}]}, "media_results": {"result": {"media_key": "3_1901058804470390785"}}}]}, "favorite_count": 21, "favorited": false, "full_text": "@elonmusk Can’t wait until you guys get here!! https://t.co/VYDHtuPoO5", "in_reply_to_screen_name": "elonmusk", "in_reply_to_status_id_str": "1901057939403608167", "in_reply_to_user_id_str": "44196397", "is_quote_status": false, "lang": "en", "possibly_sensitive": false, "possibly_sensitive_editable": true, "quote_count": 0, "reply_count": 2, "retweet_count": 2, "retweeted": false, "user_id_str": "1464394923931836418", "id_str": "1901058809730138500"}}}, "tweetDisplayType": "Tweet"}, "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}, "timelinesDetails": {"controllerData": "DAACDAAEDAABCgABFQGKCCBjgBUKAAIAAAAAFSABCAAAAAA="}}}}}], "displayType": "VerticalConversation", "clientEventInfo": {"details": {"conversationDetails": {"conversationSection": "HighQuality"}}}}}, {"entryId": "cursor-bottom-654522489178500855", "sortIndex": "7322314097451167529", "content": {"entryType": "TimelineTimelineItem", "__typename": "TimelineTimelineItem", "itemContent": {"itemType": "TimelineTimelineCursor", "__typename": "TimelineTimelineCursor", "value": "bQAAAPBeHBm2oMXbpdbCguI00Me67dv7geI0yoCyxZSp-OE0woHZifjEjuI06Mazlbyn7-I0moW4zcGX9eE0joHb5dH4l9405MHbvfvs9eE0iIa8scm69eE0nsW77fPi-eE0jIe5ibHM9eE0JQISFQQAAA", "cursorType": "Bottom"}}}]}, {"type": "TimelineTerminateTimeline", "direction": "Top"}]}}}}