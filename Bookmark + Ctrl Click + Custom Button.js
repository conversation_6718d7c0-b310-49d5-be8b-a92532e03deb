// ==UserScript==
// @name         Bookmark + Ctrl Click + Custom Button (VioletMonkey Version)
// @namespace    http://violentmonkey.github.io/
// @version      2.0
// @description  VioletMonkey optimized script. Auto-detect TweetDeck/X updates. Bookmark by "...", add custom Like + Bookmark + Download button, Ctrl+Click tweets only.
// <AUTHOR>
// @match        https://x.com/i/tweetdeck*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function () {
    'use strict';

    let isReady = false;
    let observer = null;

    function likeTweet(tweetElement) {
        const likeButton = tweetElement.querySelector('a[rel="favorite"]') ||
                          tweetElement.querySelector('[data-testid="like"]') ||
                          tweetElement.querySelector('[aria-label*="Like"]');
        if (likeButton && !likeButton.classList.contains('favorited')) {
            likeButton.click();
        }
    }

    function bookmarkTweet(tweetElement) {
        let tweetId = null;

        // Multiple ways to find tweet ID for different layouts
        const dataChirp = tweetElement.querySelector('[data-chirp-id]');
        if (dataChirp) {
            tweetId = dataChirp.getAttribute('data-chirp-id');
        }

        if (!tweetId && tweetElement.getAttribute('data-tweet-id')) {
            tweetId = tweetElement.getAttribute('data-tweet-id');
        }

        // Try to find bookmark button directly
        const bookmarkButton = tweetElement.querySelector('[data-testid="bookmark"]') ||
                              tweetElement.querySelector('[aria-label*="Bookmark"]') ||
                              tweetElement.querySelector('.tweet-bookmark-menu-option');

        if (bookmarkButton) {
            bookmarkButton.click();
            return;
        }

        // Fallback method for older layouts
        if (!tweetId) return;

        const menuItem = document.createElement('li');
        menuItem.className = 'feature-bookmark is-selectable';
        const bookmarkLink = document.createElement('a');
        bookmarkLink.className = 'tweet-bookmark-menu-option';
        bookmarkLink.href = '#';
        bookmarkLink.setAttribute('data-action', 'bookmark');
        bookmarkLink.setAttribute('data-tweet-id', tweetId);
        menuItem.appendChild(bookmarkLink);
        document.body.appendChild(menuItem);

        setTimeout(() => {
            bookmarkLink.click();
            if (document.body.contains(menuItem)) {
                document.body.removeChild(menuItem);
            }
        }, 10);
    }

    function downloadMedia(tweetElement) {
        const downloadButton = tweetElement.querySelector('a[data-btd-action="download-media"]') ||
                              tweetElement.querySelector('[data-testid="download"]');
        if (downloadButton) {
            downloadButton.click();
        }
    }

    function performAllActions(tweetElement) {
        likeTweet(tweetElement);
        setTimeout(() => bookmarkTweet(tweetElement), 100);
        setTimeout(() => downloadMedia(tweetElement), 200);
    }

    function addDropdownListener() {
        document.body.addEventListener('click', function (event) {
            const moreOptionsButton =
                event.target.closest('i[class*="icon-more"]') ||
                event.target.closest('.icon-more') ||
                event.target.closest('[data-testid="caret"]') ||
                event.target.closest('[aria-label*="More"]');

            if (moreOptionsButton) {
                event.preventDefault();
                event.stopPropagation();
                const tweetElement =
                    moreOptionsButton.closest('article') ||
                    moreOptionsButton.closest('[data-tweet-id]') ||
                    moreOptionsButton.closest('[data-testid="tweet"]');

                if (!tweetElement) return;
                bookmarkTweet(tweetElement);
            }
        }, true);
    }

    function createCustomActionButton(tweetElement) {
        if (tweetElement.querySelector('.auto-action-button')) return;

        const dropdownButton =
            tweetElement.querySelector('i[class*="icon-more"]') ||
            tweetElement.querySelector('.icon-more') ||
            tweetElement.querySelector('[data-testid="caret"]') ||
            tweetElement.querySelector('[aria-label*="More"]');

        if (!dropdownButton) return;

        const parentLi = dropdownButton.closest('li');
        if (!parentLi) return;

        const newLi = document.createElement('li');
        newLi.className = 'tweet-action-item pull-left auto-action-button';
        newLi.style.cursor = 'pointer';

        const newBtn = document.createElement('a');
        newBtn.className = 'js-show-tip tweet-action position-rel';
        newBtn.title = 'Like + Bookmark + Download';
        newBtn.innerHTML = `<span style="font-weight: bold; font-size: 24px; position: relative; top: -5px;">X</span>`;

        newBtn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            performAllActions(tweetElement);
        });

        newLi.appendChild(newBtn);
        parentLi.parentNode.insertBefore(newLi, parentLi.nextSibling);
    }

    function processTweets() {
        if (!isReady) return;

        const tweets = document.querySelectorAll('article, [data-tweet-id], [data-testid="tweet"]');
        tweets.forEach(tweet => {
            try {
                createCustomActionButton(tweet);
            } catch (e) {
                console.log('Error processing tweet:', e);
            }
        });
    }

    function initializeScript() {
        isReady = true;
        addDropdownListener();
        processTweets();

        // Set up MutationObserver for dynamic content
        if (observer) observer.disconnect();

        observer = new MutationObserver(function(mutations) {
            let shouldProcess = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldProcess = true;
                }
            });
            if (shouldProcess) {
                setTimeout(processTweets, 500);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Backup interval for catching missed updates
        setInterval(processTweets, 3000);
    }

    // Handle Ctrl+Click functionality
    document.addEventListener('click', function(event) {
        const tweet = event.target.closest('.js-stream-item') ||
                     event.target.closest('article') ||
                     event.target.closest('[data-testid="tweet"]');

        if (tweet) {
            const isActionButton = event.target.closest('.js-reply-action') ||
                                   event.target.closest('[rel="retweet"]') ||
                                   event.target.closest('.js-show-tip') ||
                                   event.target.closest('[rel="favorite"]') ||
                                   event.target.closest('[rel="actionsMenu"]') ||
                                   event.target.closest('.feature-customtimelines') ||
                                   event.target.closest('[data-testid="reply"]') ||
                                   event.target.closest('[data-testid="retweet"]') ||
                                   event.target.closest('[data-testid="like"]') ||
                                   event.target.closest('[data-testid="bookmark"]') ||
                                   event.target.closest('.auto-action-button');

            const isMedia = event.target.closest('.js-media-image-link') ||
                           event.target.closest('.media-img') ||
                           event.target.closest('.js-media-preview-container') ||
                           event.target.closest('.js-zoomable') ||
                           event.target.closest('[data-testid="tweetPhoto"]');

            if (isActionButton || isMedia) return;

            if (!event.ctrlKey) {
                event.stopPropagation();
                event.preventDefault();
            }
        }
    }, true);

    // Wait for page to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeScript);
    } else {
        setTimeout(initializeScript, 1000);
    }

    // Handle navigation changes (SPA)
    let currentUrl = location.href;
    setInterval(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            setTimeout(initializeScript, 2000);
        }
    }, 1000);

})();