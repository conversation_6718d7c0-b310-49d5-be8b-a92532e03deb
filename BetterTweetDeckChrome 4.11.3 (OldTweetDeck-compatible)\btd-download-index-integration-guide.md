# BetterTweetDeck Download Index Patch Integration Guide

This guide explains how to integrate the `btd-download-index-patch.js` file into your BetterTweetDeck Chrome extension to add index numbering for multiple media downloads.

## What This Patch Does

When downloading multiple media files from a single tweet, the patch automatically adds index numbers (_1, _2, _3, etc.) to prevent filename conflicts and maintain proper ordering.

## Integration Methods

### Method 1: Add to Background Script (Recommended)

1. Open the `background.js` file in the extension root directory
2. Add the following at the beginning of the file after any existing imports:

```javascript
// Load the download index patch
import './btd-download-index-patch.js';
```

Or if the background.js uses script tags, add:
```javascript
// Load the download index patch
(async () => {
  const script = await fetch(chrome.runtime.getURL('btd-download-index-patch.js'));
  const code = await script.text();
  eval(code);
})();
```

### Method 2: Add as Web Accessible Resource

1. Edit `manifest.json` and add the patch file to web_accessible_resources:

```json
"web_accessible_resources": [
  {
    "resources": [
      "build/inject.js",
      "*.png",
      "build/emoji-sheet-64.png",
      "btd-download-index-patch.js"
    ],
    "matches": [
      "*://tweetdeck.twitter.com/*",
      "*://tweetdeck.dimden.dev/*",
      "*://twitter.com/*",
      "*://x.com/*"
    ]
  }
]
```

2. Then inject it from the content script or background script.

### Method 3: Manual Integration

If you prefer to integrate the code directly:

1. Find where BetterTweetDeck handles media downloads
2. Add the patch code before any download operations
3. The patch will automatically intercept `chrome.downloads.download` calls

## How It Works

The patch:
- Intercepts all `chrome.downloads.download` API calls
- Extracts tweet IDs from URLs or filenames
- Tracks download counts per tweet
- Adds index suffixes (_1, _2, etc.) for multiple files from the same tweet
- Automatically cleans up tracking data after 1 minute

## Testing

1. Navigate to TweetDeck (x.com/i/tweetdeck)
2. Find a tweet with multiple media files (photos/videos)
3. Use the "Download media" button
4. Check that files are saved with proper indexing:
   - First file: `username-1234567890123456789-1.jpg`
   - Second file: `username-1234567890123456789-1_2.jpg`
   - Third file: `username-1234567890123456789-1_3.jpg`

## Troubleshooting

If the patch isn't working:

1. Check the browser console for "BetterTweetDeck Download Index Patch loaded" message
2. Ensure the patch is loaded before any download operations
3. Verify that chrome.downloads API is available in your context
4. Check for any Content Security Policy restrictions

## Notes

- The patch is designed to be non-invasive and won't break existing functionality
- It only modifies filenames when multiple files are from the same tweet
- The first file keeps the original naming pattern for backward compatibility
- Subsequent files get indexed starting from _2