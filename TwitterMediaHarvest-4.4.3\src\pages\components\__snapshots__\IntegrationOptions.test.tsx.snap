// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`unit test for IntegrationOptions component match chrome snapshot: chrome 1`] = `
<div>
  <div
    class="chakra-stack css-tl3ftk"
  >
    <div
      class="chakra-form-control css-0"
      data-testid="filenameDetector-integration-switch"
      label="Translated<options:integrations_Filename Detector>"
      role="group"
    >
      <label
        class="chakra-form__label css-28a6i0"
        data-testid="feature-switch-label"
        for=":r0:"
        id="field-:r1:-label"
      >
        <div
          class="chakra-stack css-mz2o5n"
          style="transition: background 300ms;"
        >
          <div
            class="chakra-stack css-1bef4uc"
          >
            <p
              class="chakra-text css-1tbqbam"
            >
              Translated&lt;options:integrations_Filename Detector&gt;
            </p>
            <p
              class="chakra-text css-qljqz"
            >
              Translated&lt;options:integrations_The detector can notify user when the filename is modified by other extensions.&gt;
            </p>
             
          </div>
          <div
            class="css-fzqoy4"
          >
            <label
              class="chakra-switch css-16pgy8f"
              data-checked=""
              data-testid="feature-switch"
            >
              <input
                aria-disabled="false"
                aria-invalid="false"
                checked=""
                class="chakra-switch__input"
                id=":r0:"
                style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
                type="checkbox"
              />
              <span
                aria-hidden="true"
                class="chakra-switch__track css-14qxnv8"
                data-checked=""
              >
                <span
                  class="chakra-switch__thumb css-0"
                  data-checked=""
                />
              </span>
            </label>
          </div>
        </div>
      </label>
    </div>
    <div
      class="chakra-form-control css-0"
      data-testid="dispatchToAria2-integration-switch"
      label="Translated<options:integrations_Dispatch download to Aria2>"
      role="group"
    >
      <label
        class="chakra-form__label css-28a6i0"
        data-testid="feature-switch-label"
        for=":r2:"
        id="field-:r3:-label"
      >
        <div
          class="chakra-stack css-mz2o5n"
          style="transition: background 300ms;"
        >
          <div
            class="chakra-stack css-1bef4uc"
          >
            <p
              class="chakra-text css-1tbqbam"
            >
              Translated&lt;options:integrations_Dispatch download to Aria2&gt;
            </p>
            <p
              class="chakra-text css-qljqz"
            >
              Translated&lt;options:integrations_Transfer the download to Aria2 via 
              <a
                class="chakra-link css-p03q1r"
                data-testid="aria2-ext-link"
                href="https://chrome.google.com/webstore/detail/mpkodccbngfoacfalldjimigbofkhgjn"
                target="_blank"
              >
                Translated&lt;options:integrations_Aria2-Explorer&gt;
              </a>
              .&gt;
            </p>
             
          </div>
          <div
            class="css-fzqoy4"
          >
            <label
              class="chakra-switch css-16pgy8f"
              data-testid="feature-switch"
            >
              <input
                aria-disabled="false"
                aria-invalid="false"
                class="chakra-switch__input"
                id=":r2:"
                style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
                type="checkbox"
              />
              <span
                aria-hidden="true"
                class="chakra-switch__track css-14qxnv8"
              >
                <span
                  class="chakra-switch__thumb css-0"
                />
              </span>
            </label>
          </div>
        </div>
      </label>
    </div>
  </div>
</div>
`;

exports[`unit test for IntegrationOptions component match edge snapshot: edge 1`] = `
<div>
  <div
    class="chakra-stack css-tl3ftk"
  >
    <div
      class="chakra-form-control css-0"
      data-testid="filenameDetector-integration-switch"
      label="Translated<options:integrations_Filename Detector>"
      role="group"
    >
      <label
        class="chakra-form__label css-28a6i0"
        data-testid="feature-switch-label"
        for=":r4:"
        id="field-:r5:-label"
      >
        <div
          class="chakra-stack css-mz2o5n"
          style="transition: background 300ms;"
        >
          <div
            class="chakra-stack css-1bef4uc"
          >
            <p
              class="chakra-text css-1tbqbam"
            >
              Translated&lt;options:integrations_Filename Detector&gt;
            </p>
            <p
              class="chakra-text css-qljqz"
            >
              Translated&lt;options:integrations_The detector can notify user when the filename is modified by other extensions.&gt;
            </p>
             
          </div>
          <div
            class="css-fzqoy4"
          >
            <label
              class="chakra-switch css-16pgy8f"
              data-checked=""
              data-testid="feature-switch"
            >
              <input
                aria-disabled="false"
                aria-invalid="false"
                checked=""
                class="chakra-switch__input"
                id=":r4:"
                style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
                type="checkbox"
              />
              <span
                aria-hidden="true"
                class="chakra-switch__track css-14qxnv8"
                data-checked=""
              >
                <span
                  class="chakra-switch__thumb css-0"
                  data-checked=""
                />
              </span>
            </label>
          </div>
        </div>
      </label>
    </div>
    <div
      class="chakra-form-control css-0"
      data-testid="dispatchToAria2-integration-switch"
      label="Translated<options:integrations_Dispatch download to Aria2>"
      role="group"
    >
      <label
        class="chakra-form__label css-28a6i0"
        data-testid="feature-switch-label"
        for=":r6:"
        id="field-:r7:-label"
      >
        <div
          class="chakra-stack css-mz2o5n"
          style="transition: background 300ms;"
        >
          <div
            class="chakra-stack css-1bef4uc"
          >
            <p
              class="chakra-text css-1tbqbam"
            >
              Translated&lt;options:integrations_Dispatch download to Aria2&gt;
            </p>
            <p
              class="chakra-text css-qljqz"
            >
              Translated&lt;options:integrations_Transfer the download to Aria2 via 
              <a
                class="chakra-link css-p03q1r"
                data-testid="aria2-ext-link"
                href="https://chrome.google.com/webstore/detail/mpkodccbngfoacfalldjimigbofkhgjn"
                target="_blank"
              >
                Translated&lt;options:integrations_Aria2-Explorer&gt;
              </a>
              .&gt;
            </p>
             
          </div>
          <div
            class="css-fzqoy4"
          >
            <label
              class="chakra-switch css-16pgy8f"
              data-testid="feature-switch"
            >
              <input
                aria-disabled="false"
                aria-invalid="false"
                class="chakra-switch__input"
                id=":r6:"
                style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
                type="checkbox"
              />
              <span
                aria-hidden="true"
                class="chakra-switch__track css-14qxnv8"
              >
                <span
                  class="chakra-switch__thumb css-0"
                />
              </span>
            </label>
          </div>
        </div>
      </label>
    </div>
  </div>
</div>
`;

exports[`unit test for IntegrationOptions component match firefox snapshot: firefox 1`] = `
<div>
  <div
    class="chakra-stack css-tl3ftk"
  >
    <div
      class="chakra-form-control css-0"
      data-disabled=""
      data-testid="filenameDetector-integration-switch"
      label="Translated<options:integrations_Filename Detector>"
      role="group"
    >
      <label
        class="chakra-form__label css-qf9lcl"
        data-disabled=""
        data-testid="feature-switch-label"
        for=":r8:"
        id="field-:r9:-label"
      >
        <div
          class="chakra-stack css-mz2o5n"
          style="transition: background 300ms;"
        >
          <div
            class="chakra-stack css-1bef4uc"
          >
            <p
              class="chakra-text css-1tbqbam"
            >
              Translated&lt;options:integrations_Filename Detector&gt;
            </p>
            <p
              class="chakra-text css-qljqz"
            >
              Translated&lt;options:integrations_The detector can notify user when the filename is modified by other extensions.&gt;
            </p>
            <div
              class="chakra-offset-slide"
              style="opacity: 0; transform: translateY(10px);"
            >
              <div
                class="chakra-stack css-ng2lei"
              >
                <svg
                  class="chakra-icon chakra-icon css-raeoph"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"
                    fill="currentColor"
                  />
                </svg>
                <div
                  class="chakra-form__helper-text css-s0rnn6"
                  data-testid="feature-switch-helper-text"
                  id="field-:r9:-helptext"
                >
                  Translated&lt;options:integrations_This integration is not compatible with Firefox&gt;
                </div>
              </div>
            </div>
          </div>
          <div
            class="css-fzqoy4"
          >
            <label
              class="chakra-switch css-16pgy8f"
              data-checked=""
              data-disabled=""
              data-testid="feature-switch"
            >
              <input
                aria-describedby="field-:r9:-helptext"
                aria-disabled="true"
                aria-invalid="false"
                checked=""
                class="chakra-switch__input"
                disabled=""
                id=":r8:"
                style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
                type="checkbox"
              />
              <span
                aria-hidden="true"
                class="chakra-switch__track css-14qxnv8"
                data-checked=""
                data-disabled=""
              >
                <span
                  class="chakra-switch__thumb css-0"
                  data-checked=""
                  data-disabled=""
                />
              </span>
            </label>
          </div>
        </div>
      </label>
    </div>
    <div
      class="chakra-form-control css-0"
      data-disabled=""
      data-testid="dispatchToAria2-integration-switch"
      label="Translated<options:integrations_Dispatch download to Aria2>"
      role="group"
    >
      <label
        class="chakra-form__label css-qf9lcl"
        data-disabled=""
        data-testid="feature-switch-label"
        for=":rb:"
        id="field-:rc:-label"
      >
        <div
          class="chakra-stack css-mz2o5n"
          style="transition: background 300ms;"
        >
          <div
            class="chakra-stack css-1bef4uc"
          >
            <p
              class="chakra-text css-1tbqbam"
            >
              Translated&lt;options:integrations_Dispatch download to Aria2&gt;
            </p>
            <p
              class="chakra-text css-qljqz"
            >
              Translated&lt;options:integrations_Transfer the download to Aria2 via Translated&lt;options:integrations_Aria2-Explorer&gt;.&gt;
            </p>
            <div
              class="chakra-offset-slide"
              style="opacity: 0; transform: translateY(10px);"
            >
              <div
                class="chakra-stack css-ng2lei"
              >
                <svg
                  class="chakra-icon chakra-icon css-raeoph"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"
                    fill="currentColor"
                  />
                </svg>
                <div
                  class="chakra-form__helper-text css-s0rnn6"
                  data-testid="feature-switch-helper-text"
                  id="field-:rc:-helptext"
                >
                  Translated&lt;options:integrations_This integration is not compatible with Firefox&gt;
                </div>
              </div>
            </div>
          </div>
          <div
            class="css-fzqoy4"
          >
            <label
              class="chakra-switch css-16pgy8f"
              data-disabled=""
              data-testid="feature-switch"
            >
              <input
                aria-describedby="field-:rc:-helptext"
                aria-disabled="true"
                aria-invalid="false"
                class="chakra-switch__input"
                disabled=""
                id=":rb:"
                style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
                type="checkbox"
              />
              <span
                aria-hidden="true"
                class="chakra-switch__track css-14qxnv8"
                data-disabled=""
              >
                <span
                  class="chakra-switch__thumb css-0"
                  data-disabled=""
                />
              </span>
            </label>
          </div>
        </div>
      </label>
    </div>
  </div>
</div>
`;
