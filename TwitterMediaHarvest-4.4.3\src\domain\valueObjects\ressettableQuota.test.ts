/**
 * @file This file is auto-generated.
 * @generated by <PERSON><PERSON><PERSON> \w Claude 3.5 Sonnet
 */
import { ResettableQuota } from './resettableQuota'

describe('ResettableQuota', () => {
  let baseDate: Date

  beforeEach(() => {
    baseDate = new Date('2024-01-01T00:00:00Z')
    jest.useFakeTimers()
    jest.setSystemTime(baseDate)
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should create with initial quota and reset time', () => {
    const resetAt = new Date('2024-01-02T00:00:00Z')
    const quota = new ResettableQuota({ quota: 100, resetAt })
    expect(quota.remaining).toBe(100)
    expect(quota.resetTime).toEqual(resetAt)
  })

  it('should not be reseted before reset time', () => {
    const resetAt = new Date('2024-01-02T00:00:00Z')
    const quota = new ResettableQuota({ quota: 100, resetAt })
    expect(quota.isReset).toBe(false)
  })

  it('should be reseted after reset time', () => {
    const resetAt = new Date('2024-01-02T00:00:00Z')
    const quota = new ResettableQuota({ quota: 100, resetAt })

    jest.setSystemTime(new Date('2024-01-02T00:00:01Z'))
    expect(quota.isReset).toBe(true)
  })

  it('should be equal when props are the same', () => {
    const resetAt = new Date('2024-01-02T00:00:00Z')
    const quota1 = new ResettableQuota({ quota: 100, resetAt })
    const quota2 = new ResettableQuota({ quota: 100, resetAt })
    expect(quota1.is(quota2)).toBe(true)
  })

  it('should not be equal when props are different', () => {
    const resetAt1 = new Date('2024-01-02T00:00:00Z')
    const resetAt2 = new Date('2024-01-03T00:00:00Z')
    const quota1 = new ResettableQuota({ quota: 100, resetAt: resetAt1 })
    const quota2 = new ResettableQuota({ quota: 100, resetAt: resetAt2 })
    expect(quota1.is(quota2)).toBe(false)
  })
})
