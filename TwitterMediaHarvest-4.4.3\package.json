{"name": "twitter-media-harvest", "version": "4.4.3", "type": "module", "description": "The easiest way to get media from twitter-timeline.", "engines": {"node": ">=22"}, "dependencies": {"@aws-crypto/sha256-browser": "^5.2.0", "@aws-sdk/credential-provider-cognito-identity": "^3.723.0", "@aws-sdk/credential-providers": "^3.723.0", "@babel/runtime": "^7.26.10", "@chakra-ui/icons": "2.2.4", "@chakra-ui/react": "~2.10.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@sentry/browser": "^8.48.0", "@smithy/fetch-http-handler": "^5.0.0", "@smithy/protocol-http": "^5.0.0", "@smithy/signature-v4": "^5.0.0", "fp-ts": "^2.16.9", "framer-motion": "^11.16.1", "idb": "^8.0.1", "joi": "^17.13.3", "jwt-decode": "^4.0.0", "path-browserify": "^1.0.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-router-dom": "^7.1.1", "sanitize-filename": "^1.6.3", "select-dom": "^9.3.0", "semver": "^7.7.1", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@eslint/js": "^9.17.0", "@faker-js/faker": "^9.3.0", "@media-harvest-config/prettier": "workspace:^", "@media-harvest/webext-i18n-loader": "workspace:^", "@smithy/types": "^4.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/chrome": "^0.0.291", "@types/gettext-parser": "^4.0.4", "@types/jest": "^29.5.14", "@types/joi": "^17.2.3", "@types/jsdom": "^21.1.7", "@types/jws": "^3.2.10", "@types/node": "^22.10.5", "@types/path-browserify": "^1.0.3", "@types/react": "^19.0.4", "@types/react-dom": "^19.0.2", "@types/semver": "^7", "@types/webextension-polyfill": "^0.12.1", "@types/wicg-file-system-access": "^2023.10.6", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@typescript-eslint/typescript-estree": "^8.19.1", "babel-loader": "^9.2.1", "browserslist": "^4.24.4", "chalk": "~4.1.2", "copy-webpack-plugin": "^12.0.2", "core-js": "^3.40.0", "cross-env": "^7.0.3", "crypto": "^1.0.1", "css-loader": "^7.1.2", "dayjs": "^1.11.13", "dotenv-webpack": "^8.1.0", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.1.0", "fake-indexeddb": "^6.0.0", "file-loader": "^6.2.0", "filemanager-webpack-plugin": "^8.0.0", "gettext-extractor": "^3.8.0", "gettext-parser": "^8.0.0", "glob": "^11.0.0", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^4.0.2", "jest-transformer-svg": "^2.0.2", "jest-webextension-mock": "^4.0.0", "keep-a-changelog": "^2.5.3", "lint-staged": "^15.3.0", "mini-css-extract-plugin": "^2.9.2", "pinst": "^3.0.0", "prettier": "^3.4.2", "prettier-eslint": "^16.3.0", "sass": "^1.83.1", "sass-loader": "^16.0.4", "style-loader": "^4.0.0", "svg-inline-loader": "^0.8.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.7.3", "typescript-eslint": "^8.19.1", "url-loader": "^4.1.1", "web-ext": "^8.3.0", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-merge": "^6.0.1", "whatwg-fetch": "^3.6.20"}, "resolutions": {"cliui": "^7.0.0"}, "scripts": {"postinstall": "husky", "postpack": "pinst --enable", "test": "cross-env NODE_ENV=test yarn typecheck && jest --env=node --coverage", "test:all": "cross-env NODE_ENV=test yarn workspaces foreach -p -A run test", "test:tools": "cross-env NODE_ENV=test yarn workspaces foreach -p --include '@media-harvest/*' -A run test", "ci": "cross-env NODE_ENV=test jest --coverage --silent --ci", "ci:all": "yarn workspaces foreach -p -A run ci", "build:all": "yarn check-envfile && yarn build:tools && yarn build:chrome:all && yarn build:edge:all && yarn build:firefox:all:self-sign", "build:all:dev": "yarn check-envfile && yarn build:tools && yarn build:chrome:all:dev && yarn build:edge:all:dev && yarn build:firefox:all:dev", "build:tools": "yarn workspaces foreach -p --include '@media-harvest/*' -A run build && yarn install", "build:chrome:all": "webpack --mode=production --progress --color --env target=chrome --env zip=true", "build:edge:all": "webpack --mode=production --progress --color --env target=edge --env zip=true", "build:edge:all:dev": "webpack --mode=development --progress --color --env target=edge", "build:firefox:all": "webpack --mode=production --progress --color --env target=firefox --env zip=true", "build:firefox:all:dev": "webpack --mode=development --progress --color --env target=firefox", "build:firefox:all:self-sign": "webpack --mode=production --progress --color --env target=firefox --env self-sign --env zip=true", "build:chrome:service": "webpack --mode=production --progress --color --env target=chrome --config-name=service", "build:chrome:injection": "webpack --mode=production --progress --color --env target=chrome --config-name=content-script", "build:chrome:all:dev": "webpack --mode=development --progress --color --env target=chrome", "build:chrome:service:dev": "webpack --mode=development --progress --color --env target=chrome --config-name=service", "build:chrome:injection:dev": "webpack --mode=development --progress --color --env target=chrome --config-name=content-script", "watch:chrome:all:dev": "yarn build:chrome:all:dev --watch", "watch:chrome:service:dev": "yarn build:chrome:service:dev --watch", "watch:chrome:injection:dev": "yarn build:chrome:injection:dev --watch", "lint": "eslint --color --quiet --fix && prettier . -l --write", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "extract-locales": "cross-env ./utils/locales-extractor.mjs", "check-locales": "yarn extract-locales && cross-env ./utils/check-locales.mjs", "check-changelog": "cross-env ./utils/check-changelog.mjs", "mh-typegen": "cross-env ./utils/mh-typegen.mjs", "check-envfile": "cross-env ./utils/check-envfile.mjs", "check-flags": "cross-env ./utils/check-flags.mjs", "check:all": "yarn check-envfile && yarn check-locales && yarn check-changelog && yarn check-flags && yarn typecheck", "update-translations": "cross-env ./utils/update-translations.mjs"}, "repository": {"type": "git", "url": "git+https://github.com/EltonChou/TwitterMediaHarvest.git"}, "author": "<PERSON>", "license": "MPL-2.0", "bugs": {"url": "https://github.com/EltonChou/TwitterMediaHarvest/issues"}, "workspaces": {"packages": ["webpack/loaders/*", "webpack/plugins/*", "configs/*"]}, "homepage": "https://github.com/EltonChou/TwitterMediaHarvest#readme", "packageManager": "yarn@4.9.1"}