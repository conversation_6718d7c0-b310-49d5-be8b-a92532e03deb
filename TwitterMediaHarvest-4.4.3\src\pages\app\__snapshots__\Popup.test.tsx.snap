// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`unit test for Popup app component can render properly 1`] = `
<div>
  <div
    class="css-8lzuyo"
  >
    <div
      class="css-17xejub"
    />
    <button
      aria-label="settings"
      class="chakra-button css-1r7q37t"
      data-testid="navbar-options"
      type="button"
    >
      <svg
        aria-hidden="true"
        class="chakra-icon css-1lz7u3x"
        fill="currentColor"
        focusable="false"
        height="1em"
        stroke="currentColor"
        stroke-width="0"
        viewBox="0 0 512 512"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M413.967 276.8c1.06-6.235 1.06-13.518 1.06-20.8s-1.06-13.518-1.06-20.8l44.667-34.318c4.26-3.118 5.319-8.317 2.13-13.518L418.215 115.6c-2.129-4.164-8.507-6.235-12.767-4.164l-53.186 20.801c-10.638-8.318-23.394-15.601-36.16-20.801l-7.448-55.117c-1.06-4.154-5.319-8.318-10.638-8.318h-85.098c-5.318 0-9.577 4.164-10.637 8.318l-8.508 55.117c-12.767 5.2-24.464 12.482-36.171 20.801l-53.186-20.801c-5.319-2.071-10.638 0-12.767 4.164L49.1 187.365c-2.119 4.153-1.061 10.399 2.129 13.518L96.97 235.2c0 7.282-1.06 13.518-1.06 20.8s1.06 13.518 1.06 20.8l-44.668 34.318c-4.26 3.118-5.318 8.317-2.13 13.518L92.721 396.4c2.13 4.164 8.508 6.235 12.767 4.164l53.187-20.801c10.637 8.318 23.394 15.601 36.16 20.801l8.508 55.117c1.069 5.2 5.318 8.318 10.637 8.318h85.098c5.319 0 9.578-4.164 10.638-8.318l8.518-55.117c12.757-5.2 24.464-12.482 36.16-20.801l53.187 20.801c5.318 2.071 10.637 0 12.767-4.164l42.549-71.765c2.129-4.153 1.06-10.399-2.13-13.518l-46.8-34.317zm-158.499 52c-41.489 0-74.46-32.235-74.46-72.8s32.971-72.8 74.46-72.8 74.461 32.235 74.461 72.8-32.972 72.8-74.461 72.8z"
        />
      </svg>
    </button>
  </div>
  <div
    class="chakra-stack css-dz3ybt"
  >
    <div
      class="css-0"
    >
      <div
        class="css-gmuwbf"
      >
        <span
          class="chakra-text css-1ny4bat"
        >
          0
        </span>
      </div>
      <div
        class="css-gmuwbf"
      >
        <span
          class="chakra-text css-nz941v"
        >
          Downloads
        </span>
      </div>
    </div>
    <div
      class="chakra-stack css-1i9xnlj"
    >
      <div
        class="chakra-stack css-1igwmid"
      >
        <label
          class="chakra-form__label css-qculm7"
          data-testid="feature-switch-label"
          for=":r0:"
        >
          Translated&lt;popup_Auto-reveal NSFW&gt;
        </label>
        <label
          class="chakra-switch css-7knp5j"
          data-testid="feature-switch"
        >
          <input
            class="chakra-switch__input"
            id=":r0:"
            style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
            type="checkbox"
          />
          <span
            aria-hidden="true"
            class="chakra-switch__track css-14qxnv8"
          >
            <span
              class="chakra-switch__thumb css-0"
            />
          </span>
        </label>
      </div>
      <div
        class="chakra-stack css-1igwmid"
      >
        <label
          class="chakra-form__label css-qculm7"
          data-testid="feature-switch-label"
          for=":r1:"
        >
          Translated&lt;popup_Video thumbnail&gt;
        </label>
        <label
          class="chakra-switch css-7knp5j"
          data-testid="feature-switch"
        >
          <input
            class="chakra-switch__input"
            id=":r1:"
            style="border: 0px; clip: rect(0px, 0px, 0px, 0px); height: 1px; width: 1px; margin: -1px; padding: 0px; overflow: hidden; white-space: nowrap; position: absolute;"
            type="checkbox"
          />
          <span
            aria-hidden="true"
            class="chakra-switch__track css-14qxnv8"
          >
            <span
              class="chakra-switch__thumb css-0"
            />
          </span>
        </label>
      </div>
    </div>
    <div
      class="chakra-stack css-fvxvhw"
    >
      <div
        class="chakra-stack css-devchl"
      >
        <a
          class="chakra-link css-0"
          data-testid="reaction-rate"
          href="https://chrome.google.com/webstore/detail/hpcgabhdlnapolkkjpejieegfpehfdok"
          target="_blank"
        >
          <div
            class="chakra-stack css-1igwmid"
          >
            <svg
              class="chakra-icon css-1u06s7o"
              fill="currentColor"
              focusable="false"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0h24v24H0V0z"
                fill="none"
              />
              <circle
                cx="15.5"
                cy="9.5"
                r="1.5"
              />
              <circle
                cx="8.5"
                cy="9.5"
                r="1.5"
              />
              <path
                d="M12 16c-1.48 0-2.75-.81-3.45-2H6.88a5.495 5.495 0 0 0 10.24 0h-1.67c-.7 1.19-1.97 2-3.45 2zm-.01-14C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
              />
            </svg>
            <p
              class="chakra-text css-0"
            >
              Translated&lt;popup_Rate it&gt;
            </p>
          </div>
        </a>
        <a
          class="chakra-link css-0"
          data-testid="reaction-report"
          href="https://github.com/EltonChou/TwitterMediaHarvest/issues"
          target="_blank"
        >
          <div
            class="chakra-stack css-1igwmid"
          >
            <svg
              class="chakra-icon css-1ywlu1w"
              fill="currentColor"
              focusable="false"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0h24v24H0V0z"
                fill="none"
              />
              <circle
                cx="15.5"
                cy="9.5"
                r="1.5"
              />
              <circle
                cx="8.5"
                cy="9.5"
                r="1.5"
              />
              <path
                d="M12 14c-2.33 0-4.32 1.45-5.12 3.5h1.67c.69-1.19 1.97-2 3.45-2s2.75.81 3.45 2h1.67c-.8-2.05-2.79-3.5-5.12-3.5zm-.01-12C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"
              />
            </svg>
            <p
              class="chakra-text css-0"
            >
              Translated&lt;popup_Report issues&gt;
            </p>
          </div>
        </a>
      </div>
    </div>
    <div
      class="css-1ksdw0u"
    >
      <div
        class="css-n6kl7j"
      >
        <span
          data-testid="footer-info"
        >
          vundefined
        </span>
      </div>
      <div
        class="css-17xejub"
      />
      <button
        aria-label="Changelog"
        class="chakra-button css-17b2lfw"
        data-testid="footer-action-changelog"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="chakra-icon css-gg8vwm"
          fill="currentColor"
          focusable="false"
          height="1em"
          stroke="currentColor"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M19.903 8.586a.997.997 0 0 0-.196-.293l-6-6a.997.997 0 0 0-.293-.196c-.03-.014-.062-.022-.094-.033a.991.991 0 0 0-.259-.051C13.04 2.011 13.021 2 13 2H6c-1.103 0-2 .897-2 2v16c0 1.103.897 2 2 2h12c1.103 0 2-.897 2-2V9c0-.021-.011-.04-.013-.062a.952.952 0 0 0-.051-.259c-.01-.032-.019-.063-.033-.093zM16.586 8H14V5.414L16.586 8zM6 20V4h6v5a1 1 0 0 0 1 1h5l.002 10H6z"
          />
          <path
            d="M8 12h8v2H8zm0 4h8v2H8zm0-8h2v2H8z"
          />
        </svg>
      </button>
      <button
        aria-label="Github"
        class="chakra-button css-17b2lfw"
        data-testid="footer-action-github"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="chakra-icon css-gg8vwm"
          fill="currentColor"
          focusable="false"
          height="1em"
          stroke="currentColor"
          stroke-width="0"
          viewBox="0 0 496 512"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z"
          />
        </svg>
      </button>
      <button
        aria-label="Donate"
        class="chakra-button css-17b2lfw"
        data-testid="footer-action-kofi"
        type="button"
      >
        <svg
          aria-hidden="true"
          class="chakra-icon css-gg8vwm"
          fill="currentColor"
          focusable="false"
          height="1em"
          stroke="currentColor"
          stroke-width="0"
          viewBox="0 0 24 24"
          width="1em"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7 22h10a1 1 0 0 0 .99-.858L19.867 8H21V6h-1.382l-1.724-3.447A.998.998 0 0 0 17 2H7c-.379 0-.725.214-.895.553L4.382 6H3v2h1.133L6.01 21.142A1 1 0 0 0 7 22zm10.418-11H6.582l-.429-3h11.693l-.428 3zm-9.551 9-.429-3h9.123l-.429 3H7.867zM7.618 4h8.764l1 2H6.618l1-2z"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;
