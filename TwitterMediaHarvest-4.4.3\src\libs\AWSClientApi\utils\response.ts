/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */
import type { MetadataBearer } from '../commands/types'
import type { HttpResponse } from '@smithy/types'

export const responseToMetadataBearer = (
  response: HttpResponse
): MetadataBearer => ({
  $metadata: {
    httpStatusCode: response.statusCode,
    requestId: response.headers['x-amzn-requestid'],
  },
})
