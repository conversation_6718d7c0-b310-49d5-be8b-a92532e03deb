(()=>{var e={1958:(e,t,r)=>{"use strict";r.d(t,{I:()=>b});var n="undefined"!=typeof Buffer&&Buffer.from?function(e){return Buffer.from(e,"utf8")}:e=>(new TextEncoder).encode(e);function s(e){return e instanceof Uint8Array?e:"string"==typeof e?n(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}function i(e){return"string"==typeof e?0===e.length:0===e.byteLength}var o={name:"SHA-256"},a={name:"HMAC",hash:o},c=new Uint8Array([227,176,196,66,152,252,28,20,154,251,244,200,153,111,185,36,39,174,65,228,100,155,147,76,164,149,153,27,120,82,184,85]);const u={};function l(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:u}var d=function(){function e(e){this.toHash=new Uint8Array(0),this.secret=e,this.reset()}return e.prototype.update=function(e){if(!i(e)){var t=s(e),r=new Uint8Array(this.toHash.byteLength+t.byteLength);r.set(this.toHash,0),r.set(t,this.toHash.byteLength),this.toHash=r}},e.prototype.digest=function(){var e=this;return this.key?this.key.then((function(t){return l().crypto.subtle.sign(a,t,e.toHash).then((function(e){return new Uint8Array(e)}))})):i(this.toHash)?Promise.resolve(c):Promise.resolve().then((function(){return l().crypto.subtle.digest(o,e.toHash)})).then((function(e){return Promise.resolve(new Uint8Array(e))}))},e.prototype.reset=function(){var e=this;this.toHash=new Uint8Array(0),this.secret&&void 0!==this.secret&&(this.key=new Promise((function(t,r){l().crypto.subtle.importKey("raw",s(e.secret),a,!1,["sign"]).then(t,r)})),this.key.catch((function(){})))},e}();Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var f=64,p=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],m=Math.pow(2,53)-1,g=function(){function e(){this.state=Int32Array.from(h),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return e.prototype.update=function(e){if(this.finished)throw new Error("Attempted to update an already finished hash.");var t=0,r=e.byteLength;if(this.bytesHashed+=r,8*this.bytesHashed>m)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;r>0;)this.buffer[this.bufferLength++]=e[t++],r--,this.bufferLength===f&&(this.hashBuffer(),this.bufferLength=0)},e.prototype.digest=function(){if(!this.finished){var e=8*this.bytesHashed,t=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),r=this.bufferLength;if(t.setUint8(this.bufferLength++,128),r%f>=56){for(var n=this.bufferLength;n<f;n++)t.setUint8(n,0);this.hashBuffer(),this.bufferLength=0}for(n=this.bufferLength;n<56;n++)t.setUint8(n,0);t.setUint32(56,Math.floor(e/4294967296),!0),t.setUint32(60,e),this.hashBuffer(),this.finished=!0}var s=new Uint8Array(32);for(n=0;n<8;n++)s[4*n]=this.state[n]>>>24&255,s[4*n+1]=this.state[n]>>>16&255,s[4*n+2]=this.state[n]>>>8&255,s[4*n+3]=this.state[n]>>>0&255;return s},e.prototype.hashBuffer=function(){for(var e=this.buffer,t=this.state,r=t[0],n=t[1],s=t[2],i=t[3],o=t[4],a=t[5],c=t[6],u=t[7],l=0;l<f;l++){if(l<16)this.temp[l]=(255&e[4*l])<<24|(255&e[4*l+1])<<16|(255&e[4*l+2])<<8|255&e[4*l+3];else{var d=this.temp[l-2],h=(d>>>17|d<<15)^(d>>>19|d<<13)^d>>>10,m=((d=this.temp[l-15])>>>7|d<<25)^(d>>>18|d<<14)^d>>>3;this.temp[l]=(h+this.temp[l-7]|0)+(m+this.temp[l-16]|0)}var g=(((o>>>6|o<<26)^(o>>>11|o<<21)^(o>>>25|o<<7))+(o&a^~o&c)|0)+(u+(p[l]+this.temp[l]|0)|0)|0,y=((r>>>2|r<<30)^(r>>>13|r<<19)^(r>>>22|r<<10))+(r&n^r&s^n&s)|0;u=c,c=a,a=o,o=i+g|0,i=s,s=n,n=r,r=g+y|0}t[0]+=r,t[1]+=n,t[2]+=s,t[3]+=i,t[4]+=o,t[5]+=a,t[6]+=c,t[7]+=u},e}(),y=function(){function e(e){this.secret=e,this.hash=new g,this.reset()}return e.prototype.update=function(e){if(!i(e)&&!this.error)try{this.hash.update(s(e))}catch(e){this.error=e}},e.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},e.prototype.digest=function(){return e=this,t=void 0,n=function(){return function(e,t){var r,n,s,i={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(i=0)),i;)try{if(r=1,n&&(s=2&a[0]?n.return:a[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,a[1])).done)return s;switch(n=0,s&&(a=[2&a[0],s.value]),a[0]){case 0:case 1:s=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!((s=(s=i.trys).length>0&&s[s.length-1])||6!==a[0]&&2!==a[0])){i=0;continue}if(3===a[0]&&(!s||a[1]>s[0]&&a[1]<s[3])){i.label=a[1];break}if(6===a[0]&&i.label<s[1]){i.label=s[1],s=a;break}if(s&&i.label<s[2]){i.label=s[2],i.ops.push(a);break}s[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],n=0}finally{r=s=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}(this,(function(e){return[2,this.digestSync()]}))},new((r=void 0)||(r=Promise))((function(s,i){function o(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?s(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((n=n.apply(e,t||[])).next())}));var e,t,r,n},e.prototype.reset=function(){if(this.hash=new g,this.secret){this.outer=new g;var e=function(e){var t=s(e);if(t.byteLength>f){var r=new g;r.update(t),t=r.digest()}var n=new Uint8Array(f);return n.set(t),n}(this.secret),t=new Uint8Array(f);t.set(e);for(var r=0;r<f;r++)e[r]^=54,t[r]^=92;for(this.hash.update(e),this.outer.update(t),r=0;r<e.byteLength;r++)e[r]=0}},e}(),v=["decrypt","digest","encrypt","exportKey","generateKey","importKey","sign","verify"];var b=function(){function e(e){!function(e){return!(!function(e){return"object"==typeof e&&"object"==typeof e.crypto&&"function"==typeof e.crypto.getRandomValues}(e)||"object"!=typeof e.crypto.subtle)&&((t=e.crypto.subtle)&&v.every((function(e){return"function"==typeof t[e]})));var t}(l())?this.hash=new y(e):this.hash=new d(e)}return e.prototype.update=function(e,t){this.hash.update(s(e))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}()},2183:(e,t,r)=>{"use strict";r.d(t,{CognitoIdentityClient:()=>br,GetCredentialsForIdentityCommand:()=>cn,GetIdCommand:()=>un});var n=r(5479);const s={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},i={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},o="X-Amzn-Trace-Id",a={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"};var c=r(7523),u=r(7135);const l={step:"serialize",tags:["HTTP_AUTH_SCHEME"],name:"httpAuthSchemeMiddleware",override:!0,relation:"before",toMiddleware:"endpointV2Middleware"},d={name:"deserializerMiddleware",step:"deserialize",tags:["DESERIALIZER"],override:!0},f={name:"serializerMiddleware",step:"serialize",tags:["SERIALIZER"],override:!0};function p(e,t,r){return{applyToStack:n=>{n.add(((e,t)=>r=>async n=>{const{response:s}=await r(n);try{return{response:s,output:await t(s,e)}}catch(e){if(Object.defineProperty(e,"$response",{value:s}),!("$metadata"in e)){const t="Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.";e.message+="\n  "+t,void 0!==e.$responseBodyText&&e.$response&&(e.$response.body=e.$responseBodyText)}throw e}})(e,r),d),n.add(((e,t)=>(r,n)=>async s=>{const i=n.endpointV2?.url&&e.urlParser?async()=>e.urlParser(n.endpointV2.url):e.endpoint;if(!i)throw new Error("No valid endpoint provider available.");const o=await t(s.input,{...e,endpoint:i});return r({...s,request:o})})(e,t),f)}}}const h=e=>e=>{throw e},m=(e,t)=>{},g={step:"finalizeRequest",tags:["HTTP_SIGNING"],name:"httpSigningMiddleware",aliases:["apiKeyMiddleware","tokenMiddleware","awsAuthMiddleware"],override:!0,relation:"after",toMiddleware:"retryMiddleware"},y=e=>{if("function"==typeof e)return e;const t=Promise.resolve(e);return()=>t};var v=r(1302);const b=e=>{if("string"==typeof e)return e;if("object"!=typeof e||"number"!=typeof e.byteOffset||"number"!=typeof e.byteLength)throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");return new TextDecoder("utf-8").decode(e)};var w=r(2637);class _ extends Uint8Array{static fromString(e,t="utf-8"){if("string"==typeof e)return function(e,t){return"base64"===t?_.mutate((0,v.E)(e)):_.mutate((0,w.a)(e))}(e,t);throw new Error(`Unsupported conversion from ${typeof e} to Uint8ArrayBlobAdapter.`)}static mutate(e){return Object.setPrototypeOf(e,_.prototype),e}transformToString(e="utf-8"){return function(e,t="utf-8"){return"base64"===t?(0,v.n)(e):b(e)}(this,e)}}var x=r(9389);r(5623),"function"==typeof ReadableStream&&ReadableStream;class S{constructor(e){this.authSchemes=new Map;for(const[t,r]of Object.entries(e))void 0!==r&&this.authSchemes.set(t,r)}getIdentityProvider(e){return this.authSchemes.get(e)}}class A{async sign(e,t,r){return e}}const E=e=>k(e)&&e.expiration.getTime()-Date.now()<3e5;const k=e=>void 0!==e.expiration,R=new RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$"),I=e=>R.test(e)||e.startsWith("[")&&e.endsWith("]"),O=new RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$"),$=(e,t=!1)=>{if(!t)return O.test(e);const r=e.split(".");for(const e of r)if(!$(e))return!1;return!0},C={},T="endpoints";function P(e){return"object"!=typeof e||null==e?e:"ref"in e?`$${P(e.ref)}`:"fn"in e?`${e.fn}(${(e.argv||[]).map(P).join(", ")})`:JSON.stringify(e,null,2)}class j extends Error{constructor(e){super(e),this.name="EndpointError"}}const M=(e,t)=>(e=>{const t=e.split("."),r=[];for(const n of t){const t=n.indexOf("[");if(-1!==t){if(n.indexOf("]")!==n.length-1)throw new j(`Path: '${e}' does not end with ']'`);const s=n.slice(t+1,-1);if(Number.isNaN(parseInt(s)))throw new j(`Invalid array index: '${s}' in path: '${e}'`);0!==t&&r.push(n.slice(0,t)),r.push(s)}else r.push(n)}return r})(t).reduce(((r,n)=>{if("object"!=typeof r)throw new j(`Index '${n}' in '${t}' not found in '${JSON.stringify(e)}'`);return Array.isArray(r)?r[parseInt(n)]:r[n]}),e),D={[c.Ue.HTTP]:80,[c.Ue.HTTPS]:443},N={booleanEquals:(e,t)=>e===t,getAttr:M,isSet:e=>null!=e,isValidHostLabel:$,not:e=>!e,parseURL:e=>{const t=(()=>{try{if(e instanceof URL)return e;if("object"==typeof e&&"hostname"in e){const{hostname:t,port:r,protocol:n="",path:s="",query:i={}}=e,o=new URL(`${n}//${t}${r?`:${r}`:""}${s}`);return o.search=Object.entries(i).map((([e,t])=>`${e}=${t}`)).join("&"),o}return new URL(e)}catch(e){return null}})();if(!t)return console.error(`Unable to parse ${JSON.stringify(e)} as a whatwg URL.`),null;const r=t.href,{host:n,hostname:s,pathname:i,protocol:o,search:a}=t;if(a)return null;const u=o.slice(0,-1);if(!Object.values(c.Ue).includes(u))return null;const l=I(s);return{scheme:u,authority:`${n}${r.includes(`${n}:${D[u]}`)||"string"==typeof e&&e.includes(`${n}:${D[u]}`)?`:${D[u]}`:""}`,path:i,normalizedPath:i.endsWith("/")?i:`${i}/`,isIp:l}},stringEquals:(e,t)=>e===t,substring:(e,t,r,n)=>t>=r||e.length<r?null:n?e.substring(e.length-r,e.length-t):e.substring(t,r),uriEncode:e=>encodeURIComponent(e).replace(/[!*'()]/g,(e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`))},q=(e,t)=>{const r=[],n={...t.endpointParams,...t.referenceRecord};let s=0;for(;s<e.length;){const t=e.indexOf("{",s);if(-1===t){r.push(e.slice(s));break}r.push(e.slice(s,t));const i=e.indexOf("}",t);if(-1===i){r.push(e.slice(t));break}"{"===e[t+1]&&"}"===e[i+1]&&(r.push(e.slice(t+1,i)),s=i+2);const o=e.substring(t+1,i);if(o.includes("#")){const[e,t]=o.split("#");r.push(M(n[e],t))}else r.push(n[o]);s=i+1}return r.join("")},L=(e,t,r)=>{if("string"==typeof e)return q(e,r);if(e.fn)return F(e,r);if(e.ref)return(({ref:e},t)=>({...t.endpointParams,...t.referenceRecord}[e]))(e,r);throw new j(`'${t}': ${String(e)} is not a string, function or reference.`)},F=({fn:e,argv:t},r)=>{const n=t.map((e=>["boolean","number"].includes(typeof e)?e:L(e,"arg",r))),s=e.split(".");return s[0]in C&&null!=s[1]?C[s[0]][s[1]](...n):N[e](...n)},U=({assign:e,...t},r)=>{if(e&&e in r.referenceRecord)throw new j(`'${e}' is already defined in Reference Record.`);const n=F(t,r);return r.logger?.debug?.(`${T} evaluateCondition: ${P(t)} = ${P(n)}`),{result:""===n||!!n,...null!=e&&{toAssign:{name:e,value:n}}}},B=(e=[],t)=>{const r={};for(const n of e){const{result:e,toAssign:s}=U(n,{...t,referenceRecord:{...t.referenceRecord,...r}});if(!e)return{result:e};s&&(r[s.name]=s.value,t.logger?.debug?.(`${T} assign: ${s.name} := ${P(s.value)}`))}return{result:!0,referenceRecord:r}},z=(e,t)=>Object.entries(e).reduce(((e,[r,n])=>({...e,[r]:n.map((e=>{const n=L(e,"Header value entry",t);if("string"!=typeof n)throw new j(`Header '${r}' value '${n}' is not a string`);return n}))})),{}),W=(e,t)=>{if(Array.isArray(e))return e.map((e=>W(e,t)));switch(typeof e){case"string":return q(e,t);case"object":if(null===e)throw new j(`Unexpected endpoint property: ${e}`);return H(e,t);case"boolean":return e;default:throw new j("Unexpected endpoint property type: "+typeof e)}},H=(e,t)=>Object.entries(e).reduce(((e,[r,n])=>({...e,[r]:W(n,t)})),{}),V=(e,t)=>{const r=L(e,"Endpoint URL",t);if("string"==typeof r)try{return new URL(r)}catch(e){throw console.error(`Failed to construct URL with ${r}`,e),e}throw new j("Endpoint URL must be a string, got "+typeof r)},K=(e,t)=>{const{conditions:r,endpoint:n}=e,{result:s,referenceRecord:i}=B(r,t);if(!s)return;const o={...t,referenceRecord:{...t.referenceRecord,...i}},{url:a,properties:c,headers:u}=n;return t.logger?.debug?.(`${T} Resolving endpoint from template: ${P(n)}`),{...null!=u&&{headers:z(u,o)},...null!=c&&{properties:H(c,o)},url:V(a,o)}},G=(e,t)=>{const{conditions:r,error:n}=e,{result:s,referenceRecord:i}=B(r,t);if(s)throw new j(L(n,"Error",{...t,referenceRecord:{...t.referenceRecord,...i}}))},Q=(e,t)=>{const{conditions:r,rules:n}=e,{result:s,referenceRecord:i}=B(r,t);if(s)return J(n,{...t,referenceRecord:{...t.referenceRecord,...i}})},J=(e,t)=>{for(const r of e)if("endpoint"===r.type){const e=K(r,t);if(e)return e}else if("error"===r.type)G(r,t);else{if("tree"!==r.type)throw new j(`Unknown endpoint rule: ${r}`);{const e=Q(r,t);if(e)return e}}throw new j("Rules evaluation failed")},Z=(e,t=!1)=>{if(t){for(const t of e.split("."))if(!Z(t))return!1;return!0}return!(!$(e)||e.length<3||e.length>63||e!==e.toLowerCase()||I(e))};let Y=JSON.parse('{"partitions":[{"id":"aws","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-east-1","name":"aws","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^(us|eu|ap|sa|ca|me|af|il|mx)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"af-south-1":{"description":"Africa (Cape Town)"},"ap-east-1":{"description":"Asia Pacific (Hong Kong)"},"ap-northeast-1":{"description":"Asia Pacific (Tokyo)"},"ap-northeast-2":{"description":"Asia Pacific (Seoul)"},"ap-northeast-3":{"description":"Asia Pacific (Osaka)"},"ap-south-1":{"description":"Asia Pacific (Mumbai)"},"ap-south-2":{"description":"Asia Pacific (Hyderabad)"},"ap-southeast-1":{"description":"Asia Pacific (Singapore)"},"ap-southeast-2":{"description":"Asia Pacific (Sydney)"},"ap-southeast-3":{"description":"Asia Pacific (Jakarta)"},"ap-southeast-4":{"description":"Asia Pacific (Melbourne)"},"ap-southeast-5":{"description":"Asia Pacific (Malaysia)"},"ap-southeast-7":{"description":"Asia Pacific (Thailand)"},"aws-global":{"description":"AWS Standard global region"},"ca-central-1":{"description":"Canada (Central)"},"ca-west-1":{"description":"Canada West (Calgary)"},"eu-central-1":{"description":"Europe (Frankfurt)"},"eu-central-2":{"description":"Europe (Zurich)"},"eu-north-1":{"description":"Europe (Stockholm)"},"eu-south-1":{"description":"Europe (Milan)"},"eu-south-2":{"description":"Europe (Spain)"},"eu-west-1":{"description":"Europe (Ireland)"},"eu-west-2":{"description":"Europe (London)"},"eu-west-3":{"description":"Europe (Paris)"},"il-central-1":{"description":"Israel (Tel Aviv)"},"me-central-1":{"description":"Middle East (UAE)"},"me-south-1":{"description":"Middle East (Bahrain)"},"mx-central-1":{"description":"Mexico (Central)"},"sa-east-1":{"description":"South America (Sao Paulo)"},"us-east-1":{"description":"US East (N. Virginia)"},"us-east-2":{"description":"US East (Ohio)"},"us-west-1":{"description":"US West (N. California)"},"us-west-2":{"description":"US West (Oregon)"}}},{"id":"aws-cn","outputs":{"dnsSuffix":"amazonaws.com.cn","dualStackDnsSuffix":"api.amazonwebservices.com.cn","implicitGlobalRegion":"cn-northwest-1","name":"aws-cn","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^cn\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-cn-global":{"description":"AWS China global region"},"cn-north-1":{"description":"China (Beijing)"},"cn-northwest-1":{"description":"China (Ningxia)"}}},{"id":"aws-us-gov","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-gov-west-1","name":"aws-us-gov","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-us-gov-global":{"description":"AWS GovCloud (US) global region"},"us-gov-east-1":{"description":"AWS GovCloud (US-East)"},"us-gov-west-1":{"description":"AWS GovCloud (US-West)"}}},{"id":"aws-iso","outputs":{"dnsSuffix":"c2s.ic.gov","dualStackDnsSuffix":"c2s.ic.gov","implicitGlobalRegion":"us-iso-east-1","name":"aws-iso","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-global":{"description":"AWS ISO (US) global region"},"us-iso-east-1":{"description":"US ISO East"},"us-iso-west-1":{"description":"US ISO WEST"}}},{"id":"aws-iso-b","outputs":{"dnsSuffix":"sc2s.sgov.gov","dualStackDnsSuffix":"sc2s.sgov.gov","implicitGlobalRegion":"us-isob-east-1","name":"aws-iso-b","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-b-global":{"description":"AWS ISOB (US) global region"},"us-isob-east-1":{"description":"US ISOB East (Ohio)"}}},{"id":"aws-iso-e","outputs":{"dnsSuffix":"cloud.adc-e.uk","dualStackDnsSuffix":"cloud.adc-e.uk","implicitGlobalRegion":"eu-isoe-west-1","name":"aws-iso-e","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eu\\\\-isoe\\\\-\\\\w+\\\\-\\\\d+$","regions":{"eu-isoe-west-1":{"description":"EU ISOE West"}}},{"id":"aws-iso-f","outputs":{"dnsSuffix":"csp.hci.ic.gov","dualStackDnsSuffix":"csp.hci.ic.gov","implicitGlobalRegion":"us-isof-south-1","name":"aws-iso-f","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isof\\\\-\\\\w+\\\\-\\\\d+$","regions":{}}],"version":"1.1"}');const X={isVirtualHostableS3Bucket:Z,parseArn:e=>{const t=e.split(":");if(t.length<6)return null;const[r,n,s,i,o,...a]=t;return"arn"!==r||""===n||""===s||""===a.join(":")?null:{partition:n,service:s,region:i,accountId:o,resourceId:a.map((e=>e.split("/"))).flat()}},partition:e=>{const{partitions:t}=Y;for(const r of t){const{regions:t,outputs:n}=r;for(const[r,s]of Object.entries(t))if(r===e)return{...n,...s}}for(const r of t){const{regionRegex:t,outputs:n}=r;if(new RegExp(t).test(e))return{...n}}const r=t.find((e=>"aws"===e.id));if(!r)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...r.outputs}}};function ee(e,t,r){e.__aws_sdk_context?e.__aws_sdk_context.features||(e.__aws_sdk_context.features={}):e.__aws_sdk_context={features:{}},e.__aws_sdk_context.features[t]=r}C.aws=X;const te=/\d{12}\.ddb/,re="user-agent",ne="x-amz-user-agent",se=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,ie=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,oe=e=>{const t=e[0].split("/").map((e=>e.replace(se,"-"))).join("/"),r=e[1]?.replace(ie,"-"),n=t.indexOf("/"),s=t.substring(0,n);let i=t.substring(n+1);return"api"===s&&(i=i.toLowerCase()),[s,i,r].filter((e=>e&&e.length>0)).reduce(((e,t,r)=>{switch(r){case 0:return t;case 1:return`${e}/${t}`;default:return`${e}#${t}`}}),"")},ae={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0};var ce;!function(e){e.ENV="env",e.CONFIG="shared config entry"}(ce||(ce={}));const ue=e=>"string"==typeof e&&(e.startsWith("fips-")||e.endsWith("-fips")),le=e=>ue(e)?["fips-aws-global","aws-fips"].includes(e)?"us-east-1":e.replace(/fips-(dkr-|prod-)?|-fips/,""):e,de="content-length",fe={step:"build",tags:["SET_CONTENT_LENGTH","CONTENT_LENGTH"],name:"contentLengthMiddleware",override:!0},pe=/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/,he=/(\d+\.){3}\d+/,me=/\.\./,ge=(e,t,r)=>{const n=async()=>{const n=r[e]??r[t];return"function"==typeof n?n():n};return"credentialScope"===e||"CredentialScope"===t?async()=>{const e="function"==typeof r.credentials?await r.credentials():r.credentials;return e?.credentialScope??e?.CredentialScope}:"accountId"===e||"AccountId"===t?async()=>{const e="function"==typeof r.credentials?await r.credentials():r.credentials;return e?.accountId??e?.AccountId}:"endpoint"===e||"endpoint"===t?async()=>{const e=await n();if(e&&"object"==typeof e){if("url"in e)return e.url.href;if("hostname"in e){const{protocol:t,hostname:r,port:n,path:s}=e;return`${t}//${r}${n?":"+n:""}${s}`}}return e}:n},ye=async e=>{},ve=e=>{if("string"==typeof e)return ve(new URL(e));const{hostname:t,pathname:r,port:n,protocol:s,search:i}=e;let o;return i&&(o=function(e){const t={};if(e=e.replace(/^\?/,""))for(const r of e.split("&")){let[e,n=null]=r.split("=");e=decodeURIComponent(e),n&&(n=decodeURIComponent(n)),e in t?Array.isArray(t[e])?t[e].push(n):t[e]=[t[e],n]:t[e]=n}return t}(i)),{hostname:t,port:n?parseInt(n):void 0,protocol:s,path:r,query:o}},be=e=>"object"==typeof e?"url"in e?ve(e.url):e:ve(e),we=async(e,t,r)=>{const n={},s=t?.getEndpointParameterInstructions?.()||{};for(const[t,i]of Object.entries(s))switch(i.type){case"staticContextParams":n[t]=i.value;break;case"contextParams":n[t]=e[i.name];break;case"clientContextParams":case"builtInParams":n[t]=await ge(i.name,t,r)();break;case"operationContextParams":n[t]=i.get(e);break;default:throw new Error("Unrecognized endpoint parameter instruction: "+JSON.stringify(i))}return 0===Object.keys(s).length&&Object.assign(n,r),"s3"===String(r.serviceId).toLowerCase()&&await(async e=>{const t=e?.Bucket||"";if("string"==typeof e.Bucket&&(e.Bucket=t.replace(/#/g,encodeURIComponent("#")).replace(/\?/g,encodeURIComponent("?"))),(e=>{const[t,r,n,,,s]=e.split(":"),i="arn"===t&&e.split(":").length>=6,o=Boolean(i&&r&&n&&s);if(i&&!o)throw new Error(`Invalid ARN: ${e} was an invalid ARN.`);return o})(t)){if(!0===e.ForcePathStyle)throw new Error("Path-style addressing cannot be used with ARN buckets")}else r=t,(!pe.test(r)||he.test(r)||me.test(r)||-1!==t.indexOf(".")&&!String(e.Endpoint).startsWith("http:")||t.toLowerCase()!==t||t.length<3)&&(e.ForcePathStyle=!0);var r;return e.DisableMultiRegionAccessPoints&&(e.disableMultiRegionAccessPoints=!0,e.DisableMRAP=!0),e})(n),n},_e={step:"serialize",tags:["ENDPOINT_PARAMETERS","ENDPOINT_V2","ENDPOINT"],name:"endpointV2Middleware",override:!0,relation:"before",toMiddleware:f.name},xe=(e,t)=>({applyToStack:r=>{r.addRelativeTo((({config:e,instructions:t})=>(r,n)=>async s=>{e.endpoint&&function(e){e.__smithy_context?e.__smithy_context.features||(e.__smithy_context.features={}):e.__smithy_context={features:{}},e.__smithy_context.features.ENDPOINT_OVERRIDE="N"}(n);const i=await(async(e,t,r,n)=>{if(!r.endpoint){let e;e=r.serviceConfiguredEndpoint?await r.serviceConfiguredEndpoint():await ye(r.serviceId),e&&(r.endpoint=()=>Promise.resolve(be(e)))}const s=await we(e,t,r);if("function"!=typeof r.endpointProvider)throw new Error("config.endpointProvider is not set.");return r.endpointProvider(s,n)})(s.input,{getEndpointParameterInstructions:()=>t},{...e},n);n.endpointV2=i,n.authSchemes=i.properties?.authSchemes;const o=n.authSchemes?.[0];if(o){n.signing_region=o.signingRegion,n.signing_service=o.signingName;const e=(0,u.u)(n),t=e?.selectedHttpAuthScheme?.httpAuthOption;t&&(t.signingProperties=Object.assign(t.signingProperties||{},{signing_region:o.signingRegion,signingRegion:o.signingRegion,signing_service:o.signingName,signingName:o.signingName,signingRegionSet:o.signingRegionSet},o.properties))}return r({...s})})({config:e,instructions:t}),_e)}});var Se;!function(e){e.STANDARD="standard",e.ADAPTIVE="adaptive"}(Se||(Se={}));const Ae=Se.STANDARD,Ee=["BandwidthLimitExceeded","EC2ThrottledException","LimitExceededException","PriorRequestNotComplete","ProvisionedThroughputExceededException","RequestLimitExceeded","RequestThrottled","RequestThrottledException","SlowDown","ThrottledException","Throttling","ThrottlingException","TooManyRequestsException","TransactionInProgressException"],ke=["TimeoutError","RequestTimeout","RequestTimeoutException"],Re=[500,502,503,504],Ie=["ECONNRESET","ECONNREFUSED","EPIPE","ETIMEDOUT"],Oe=e=>429===e.$metadata?.httpStatusCode||Ee.includes(e.name)||1==e.$retryable?.throttling,$e=(e,t=0)=>(e=>e.$metadata?.clockSkewCorrected)(e)||ke.includes(e.name)||Ie.includes(e?.code||"")||Re.includes(e.$metadata?.httpStatusCode||0)||void 0!==e.cause&&t<=10&&$e(e.cause,t+1);class Ce{constructor(e){this.currentCapacity=0,this.enabled=!1,this.lastMaxRate=0,this.measuredTxRate=0,this.requestCount=0,this.lastTimestamp=0,this.timeWindow=0,this.beta=e?.beta??.7,this.minCapacity=e?.minCapacity??1,this.minFillRate=e?.minFillRate??.5,this.scaleConstant=e?.scaleConstant??.4,this.smooth=e?.smooth??.8;const t=this.getCurrentTimeInSeconds();this.lastThrottleTime=t,this.lastTxRateBucket=Math.floor(this.getCurrentTimeInSeconds()),this.fillRate=this.minFillRate,this.maxCapacity=this.minCapacity}getCurrentTimeInSeconds(){return Date.now()/1e3}async getSendToken(){return this.acquireTokenBucket(1)}async acquireTokenBucket(e){if(this.enabled){if(this.refillTokenBucket(),e>this.currentCapacity){const t=(e-this.currentCapacity)/this.fillRate*1e3;await new Promise((e=>Ce.setTimeoutFn(e,t)))}this.currentCapacity=this.currentCapacity-e}}refillTokenBucket(){const e=this.getCurrentTimeInSeconds();if(!this.lastTimestamp)return void(this.lastTimestamp=e);const t=(e-this.lastTimestamp)*this.fillRate;this.currentCapacity=Math.min(this.maxCapacity,this.currentCapacity+t),this.lastTimestamp=e}updateClientSendingRate(e){let t;if(this.updateMeasuredRate(),Oe(e)){const e=this.enabled?Math.min(this.measuredTxRate,this.fillRate):this.measuredTxRate;this.lastMaxRate=e,this.calculateTimeWindow(),this.lastThrottleTime=this.getCurrentTimeInSeconds(),t=this.cubicThrottle(e),this.enableTokenBucket()}else this.calculateTimeWindow(),t=this.cubicSuccess(this.getCurrentTimeInSeconds());const r=Math.min(t,2*this.measuredTxRate);this.updateTokenBucketRate(r)}calculateTimeWindow(){this.timeWindow=this.getPrecise(Math.pow(this.lastMaxRate*(1-this.beta)/this.scaleConstant,1/3))}cubicThrottle(e){return this.getPrecise(e*this.beta)}cubicSuccess(e){return this.getPrecise(this.scaleConstant*Math.pow(e-this.lastThrottleTime-this.timeWindow,3)+this.lastMaxRate)}enableTokenBucket(){this.enabled=!0}updateTokenBucketRate(e){this.refillTokenBucket(),this.fillRate=Math.max(e,this.minFillRate),this.maxCapacity=Math.max(e,this.minCapacity),this.currentCapacity=Math.min(this.currentCapacity,this.maxCapacity)}updateMeasuredRate(){const e=this.getCurrentTimeInSeconds(),t=Math.floor(2*e)/2;if(this.requestCount++,t>this.lastTxRateBucket){const e=this.requestCount/(t-this.lastTxRateBucket);this.measuredTxRate=this.getPrecise(e*this.smooth+this.measuredTxRate*(1-this.smooth)),this.requestCount=0,this.lastTxRateBucket=t}}getPrecise(e){return parseFloat(e.toFixed(8))}}Ce.setTimeoutFn=setTimeout;const Te=({retryDelay:e,retryCount:t,retryCost:r})=>({getRetryCount:()=>t,getRetryDelay:()=>Math.min(2e4,e),getRetryCost:()=>r});class Pe{constructor(e){this.maxAttempts=e,this.mode=Se.STANDARD,this.capacity=500,this.retryBackoffStrategy=(()=>{let e=100;return{computeNextBackoffDelay:t=>Math.floor(Math.min(2e4,Math.random()*2**t*e)),setDelayBase:t=>{e=t}}})(),this.maxAttemptsProvider="function"==typeof e?e:async()=>e}async acquireInitialRetryToken(e){return Te({retryDelay:100,retryCount:0})}async refreshRetryTokenForRetry(e,t){const r=await this.getMaxAttempts();if(this.shouldRetry(e,t,r)){const r=t.errorType;this.retryBackoffStrategy.setDelayBase("THROTTLING"===r?500:100);const n=this.retryBackoffStrategy.computeNextBackoffDelay(e.getRetryCount()),s=t.retryAfterHint?Math.max(t.retryAfterHint.getTime()-Date.now()||0,n):n,i=this.getCapacityCost(r);return this.capacity-=i,Te({retryDelay:s,retryCount:e.getRetryCount()+1,retryCost:i})}throw new Error("No retry token available")}recordSuccess(e){this.capacity=Math.max(500,this.capacity+(e.getRetryCost()??1))}getCapacity(){return this.capacity}async getMaxAttempts(){try{return await this.maxAttemptsProvider()}catch(e){return console.warn("Max attempts provider could not resolve. Using default of 3"),3}}shouldRetry(e,t,r){return e.getRetryCount()+1<r&&this.capacity>=this.getCapacityCost(t.errorType)&&this.isRetryableError(t.errorType)}getCapacityCost(e){return"TRANSIENT"===e?10:5}isRetryableError(e){return"THROTTLING"===e||"TRANSIENT"===e}}class je{constructor(e,t){this.maxAttemptsProvider=e,this.mode=Se.ADAPTIVE;const{rateLimiter:r}=t??{};this.rateLimiter=r??new Ce,this.standardRetryStrategy=new Pe(e)}async acquireInitialRetryToken(e){return await this.rateLimiter.getSendToken(),this.standardRetryStrategy.acquireInitialRetryToken(e)}async refreshRetryTokenForRetry(e,t){return this.rateLimiter.updateClientSendingRate(t),this.standardRetryStrategy.refreshRetryTokenForRetry(e,t)}recordSuccess(e){this.rateLimiter.updateClientSendingRate({}),this.standardRetryStrategy.recordSuccess(e)}}const Me={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let De;const Ne=new Uint8Array(16);function qe(){if(!De&&(De="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!De))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return De(Ne)}const Le=[];for(let e=0;e<256;++e)Le.push((e+256).toString(16).slice(1));const Fe=function(e,t,r){if(Me.randomUUID&&!t&&!e)return Me.randomUUID();const n=(e=e||{}).random||(e.rng||qe)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return Le[e[t+0]]+Le[e[t+1]]+Le[e[t+2]]+Le[e[t+3]]+"-"+Le[e[t+4]]+Le[e[t+5]]+"-"+Le[e[t+6]]+Le[e[t+7]]+"-"+Le[e[t+8]]+Le[e[t+9]]+"-"+Le[e[t+10]]+Le[e[t+11]]+Le[e[t+12]]+Le[e[t+13]]+Le[e[t+14]]+Le[e[t+15]]}(n)},Ue=(e,t)=>{const r=[];if(e&&r.push(e),t)for(const e of t)r.push(e);return r},Be=(e,t)=>`${e||"anonymous"}${t&&t.length>0?` (a.k.a. ${t.join(",")})`:""}`,ze=()=>{let e=[],t=[],r=!1;const n=new Set,s=r=>(e.forEach((e=>{r.add(e.middleware,{...e})})),t.forEach((e=>{r.addRelativeTo(e.middleware,{...e})})),r.identifyOnResolve?.(a.identifyOnResolve()),r),i=e=>{const t=[];return e.before.forEach((e=>{0===e.before.length&&0===e.after.length?t.push(e):t.push(...i(e))})),t.push(e),e.after.reverse().forEach((e=>{0===e.before.length&&0===e.after.length?t.push(e):t.push(...i(e))})),t},o=(r=!1)=>{const n=[],s=[],o={};e.forEach((e=>{const t={...e,before:[],after:[]};for(const e of Ue(t.name,t.aliases))o[e]=t;n.push(t)})),t.forEach((e=>{const t={...e,before:[],after:[]};for(const e of Ue(t.name,t.aliases))o[e]=t;s.push(t)})),s.forEach((e=>{if(e.toMiddleware){const t=o[e.toMiddleware];if(void 0===t){if(r)return;throw new Error(`${e.toMiddleware} is not found when adding ${Be(e.name,e.aliases)} middleware ${e.relation} ${e.toMiddleware}`)}"after"===e.relation&&t.after.push(e),"before"===e.relation&&t.before.push(e)}}));const a=(c=n,c.sort(((e,t)=>We[t.step]-We[e.step]||He[t.priority||"normal"]-He[e.priority||"normal"]))).map(i).reduce(((e,t)=>(e.push(...t),e)),[]);var c;return a},a={add:(t,r={})=>{const{name:s,override:i,aliases:o}=r,a={step:"initialize",priority:"normal",middleware:t,...r},c=Ue(s,o);if(c.length>0){if(c.some((e=>n.has(e)))){if(!i)throw new Error(`Duplicate middleware name '${Be(s,o)}'`);for(const t of c){const r=e.findIndex((e=>e.name===t||e.aliases?.some((e=>e===t))));if(-1===r)continue;const n=e[r];if(n.step!==a.step||a.priority!==n.priority)throw new Error(`"${Be(n.name,n.aliases)}" middleware with ${n.priority} priority in ${n.step} step cannot be overridden by "${Be(s,o)}" middleware with ${a.priority} priority in ${a.step} step.`);e.splice(r,1)}}for(const e of c)n.add(e)}e.push(a)},addRelativeTo:(e,r)=>{const{name:s,override:i,aliases:o}=r,a={middleware:e,...r},c=Ue(s,o);if(c.length>0){if(c.some((e=>n.has(e)))){if(!i)throw new Error(`Duplicate middleware name '${Be(s,o)}'`);for(const e of c){const r=t.findIndex((t=>t.name===e||t.aliases?.some((t=>t===e))));if(-1===r)continue;const n=t[r];if(n.toMiddleware!==a.toMiddleware||n.relation!==a.relation)throw new Error(`"${Be(n.name,n.aliases)}" middleware ${n.relation} "${n.toMiddleware}" middleware cannot be overridden by "${Be(s,o)}" middleware ${a.relation} "${a.toMiddleware}" middleware.`);t.splice(r,1)}}for(const e of c)n.add(e)}t.push(a)},clone:()=>s(ze()),use:e=>{e.applyToStack(a)},remove:r=>"string"==typeof r?(r=>{let s=!1;const i=e=>{const t=Ue(e.name,e.aliases);if(t.includes(r)){s=!0;for(const e of t)n.delete(e);return!1}return!0};return e=e.filter(i),t=t.filter(i),s})(r):(r=>{let s=!1;const i=e=>{if(e.middleware===r){s=!0;for(const t of Ue(e.name,e.aliases))n.delete(t);return!1}return!0};return e=e.filter(i),t=t.filter(i),s})(r),removeByTag:r=>{let s=!1;const i=e=>{const{tags:t,name:i,aliases:o}=e;if(t&&t.includes(r)){const e=Ue(i,o);for(const t of e)n.delete(t);return s=!0,!1}return!0};return e=e.filter(i),t=t.filter(i),s},concat:e=>{const t=s(ze());return t.use(e),t.identifyOnResolve(r||t.identifyOnResolve()||(e.identifyOnResolve?.()??!1)),t},applyToStack:s,identify:()=>o(!0).map((e=>{const t=e.step??e.relation+" "+e.toMiddleware;return Be(e.name,e.aliases)+" - "+t})),identifyOnResolve:e=>("boolean"==typeof e&&(r=e),r),resolve:(e,t)=>{for(const r of o().map((e=>e.middleware)).reverse())e=r(e,t);return r&&console.log(a.identify()),e}};return a},We={initialize:5,serialize:4,build:3,finalizeRequest:2,deserialize:1},He={high:3,normal:2,low:1};class Ve{constructor(e){this.config=e,this.middlewareStack=ze()}send(e,t,r){const n="function"!=typeof t?t:void 0,s="function"==typeof t?t:r;let i;if(void 0===n&&!0===this.config.cacheMiddleware){this.handlers||(this.handlers=new WeakMap);const t=this.handlers;t.has(e.constructor)?i=t.get(e.constructor):(i=e.resolveMiddleware(this.middlewareStack,this.config,n),t.set(e.constructor,i))}else delete this.handlers,i=e.resolveMiddleware(this.middlewareStack,this.config,n);if(!s)return i(e).then((e=>e.output));i(e).then((e=>s(null,e.output)),(e=>s(e))).catch((()=>{}))}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}}class Ke{constructor(){this.middlewareStack=ze()}static classBuilder(){return new Ge}resolveMiddlewareWithContext(e,t,r,{middlewareFn:n,clientName:s,commandName:i,inputFilterSensitiveLog:o,outputFilterSensitiveLog:a,smithyContext:u,additionalContext:l,CommandCtor:d}){for(const s of n.bind(this)(d,e,t,r))this.middlewareStack.use(s);const f=e.concat(this.middlewareStack),{logger:p}=t,h={logger:p,clientName:s,commandName:i,inputFilterSensitiveLog:o,outputFilterSensitiveLog:a,[c.Vf]:{commandInstance:this,...u},...l},{requestHandler:m}=t;return f.resolve((e=>m.handle(e.request,r||{})),h)}}class Ge{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=e=>e,this._outputFilterSensitiveLog=e=>e,this._serializer=null,this._deserializer=null}init(e){this._init=e}ep(e){return this._ep=e,this}m(e){return this._middlewareFn=e,this}s(e,t,r={}){return this._smithyContext={service:e,operation:t,...r},this}c(e={}){return this._additionalContext=e,this}n(e,t){return this._clientName=e,this._commandName=t,this}f(e=e=>e,t=e=>e){return this._inputFilterSensitiveLog=e,this._outputFilterSensitiveLog=t,this}ser(e){return this._serializer=e,this}de(e){return this._deserializer=e,this}build(){const e=this;let t;return t=class extends Ke{static getEndpointParameterInstructions(){return e._ep}constructor(...[t]){super(),this.serialize=e._serializer,this.deserialize=e._deserializer,this.input=t??{},e._init(this)}resolveMiddleware(r,n,s){return this.resolveMiddlewareWithContext(r,n,s,{CommandCtor:t,middlewareFn:e._middlewareFn,clientName:e._clientName,commandName:e._commandName,inputFilterSensitiveLog:e._inputFilterSensitiveLog,outputFilterSensitiveLog:e._outputFilterSensitiveLog,smithyContext:e._smithyContext,additionalContext:e._additionalContext})}}}}const Qe="***SensitiveInformation***",Je=e=>{if(null!=e){if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return String(t)!==String(e)&&tt.warn(et(`Expected number but observed string: ${e}`)),t}if("number"==typeof e)return e;throw new TypeError(`Expected number, got ${typeof e}: ${e}`)}},Ze=(Math.ceil(2**127*(2-2**-23)),e=>{if(null!=e){if("string"==typeof e)return e;if(["boolean","number","bigint"].includes(typeof e))return tt.warn(et(`Expected string, got ${typeof e}: ${e}`)),String(e);throw new TypeError(`Expected string, got ${typeof e}: ${e}`)}}),Ye=/(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g,Xe=e=>{const t=e.match(Ye);if(null===t||t[0].length!==e.length)throw new TypeError("Expected real number, got implicit NaN");return parseFloat(e)},et=e=>String(new TypeError(e).stack||e).split("\n").slice(0,5).filter((e=>!e.includes("stackTraceWarning"))).join("\n"),tt={warn:console.warn};class rt extends Error{constructor(e){super(e.message),Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=e.name,this.$fault=e.$fault,this.$metadata=e.$metadata}static isInstance(e){if(!e)return!1;const t=e;return rt.prototype.isPrototypeOf(t)||Boolean(t.$fault)&&Boolean(t.$metadata)&&("client"===t.$fault||"server"===t.$fault)}static[Symbol.hasInstance](e){if(!e)return!1;const t=e;return this===rt?rt.isInstance(e):!!rt.isInstance(e)&&(t.name&&this.name?this.prototype.isPrototypeOf(e)||t.name===this.name:this.prototype.isPrototypeOf(e))}}const nt=(e,t={})=>{Object.entries(t).filter((([,e])=>void 0!==e)).forEach((([t,r])=>{null!=e[t]&&""!==e[t]||(e[t]=r)}));const r=e.message||e.Message||"UnknownError";return e.message=r,delete e.Message,e},st=e=>{switch(e){case"standard":case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"mobile":return{retryMode:"standard",connectionTimeout:3e4};default:return{}}},it=e=>{const t=[];for(const r in c.dB){const n=c.dB[r];void 0!==e[n]&&t.push({algorithmId:()=>n,checksumConstructor:()=>e[n]})}return{_checksumAlgorithms:t,addChecksumAlgorithm(e){this._checksumAlgorithms.push(e)},checksumAlgorithms(){return this._checksumAlgorithms}}},ot=e=>{const t={};return e.checksumAlgorithms().forEach((e=>{t[e.algorithmId()]=e.checksumConstructor()})),t},at=e=>{let t=e.retryStrategy;return{setRetryStrategy(e){t=e},retryStrategy:()=>t}},ct=e=>{const t={};return t.retryStrategy=e.retryStrategy(),t},ut=e=>({...it(e),...at(e)}),lt=function(e){return Object.assign(new String(e),{deserializeJSON:()=>JSON.parse(String(e)),toString:()=>String(e),toJSON:()=>String(e)})};lt.from=e=>e&&"object"==typeof e&&(e instanceof lt||"deserializeJSON"in e)?e:"string"==typeof e||Object.getPrototypeOf(e)===String.prototype?lt(String(e)):lt(JSON.stringify(e)),lt.fromObject=lt.from;class dt{trace(){}debug(){}info(){}warn(){}error(){}}const ft=(e,t)=>{const r={};for(const n in t)pt(r,e,t,n);return r},pt=(e,t,r,n)=>{if(null!==t){let s=r[n];"function"==typeof s&&(s=[,s]);const[i=ht,o=mt,a=n]=s;return void(("function"==typeof i&&i(t[a])||"function"!=typeof i&&i)&&(e[n]=o(t[a])))}let[s,i]=r[n];if("function"==typeof i){let t;const r=void 0===s&&null!=(t=i()),o="function"==typeof s&&!!s(void 0)||"function"!=typeof s&&!!s;r?e[n]=t:o&&(e[n]=i())}else{const t=void 0===s&&null!=i,r="function"==typeof s&&!!s(i)||"function"!=typeof s&&!!s;(t||r)&&(e[n]=i)}},ht=e=>null!=e,mt=e=>e,gt=e=>{if(null==e)return{};if(Array.isArray(e))return e.filter((e=>null!=e)).map(gt);if("object"==typeof e){const t={};for(const r of Object.keys(e))null!=e[r]&&(t[r]=gt(e[r]));return t}return e},yt=e=>e?.body instanceof ReadableStream,vt=e=>{const t={error:e,errorType:bt(e)},r=_t(e.$response);return r&&(t.retryAfterHint=r),t},bt=e=>Oe(e)?"THROTTLING":$e(e)?"TRANSIENT":(e=>{if(void 0!==e.$metadata?.httpStatusCode){const t=e.$metadata.httpStatusCode;return 500<=t&&t<=599&&!$e(e)}return!1})(e)?"SERVER_ERROR":"CLIENT_ERROR",wt={name:"retryMiddleware",tags:["RETRY"],step:"finalizeRequest",priority:"high",override:!0},_t=e=>{if(!n.cS.isInstance(e))return;const t=Object.keys(e.headers).find((e=>"retry-after"===e.toLowerCase()));if(!t)return;const r=e.headers[t],s=Number(r);return Number.isNaN(s)?new Date(r):new Date(1e3*s)};var xt=r(4428);const St=e=>{let t,r=!1;e.credentials&&(r=!0,t=((e,t,r)=>{if(void 0===e)return;const n="function"!=typeof e?async()=>Promise.resolve(e):e;let s,i,o,a=!1;const c=async e=>{i||(i=n(e));try{s=await i,o=!0,a=!1}finally{i=void 0}return s};return void 0===t?async e=>(o&&!e?.forceRefresh||(s=await c(e)),s):async e=>(o&&!e?.forceRefresh||(s=await c(e)),a?s:r(s)?t(s)?(await c(e),s):s:(a=!0,s))})(e.credentials,E,k)),t||(t=e.credentialDefaultProvider?y(e.credentialDefaultProvider(Object.assign({},e,{parentClientConfig:e}))):async()=>{throw new Error("`credentials` is missing")});const n=async()=>t({callerClientConfig:e}),{signingEscapePath:s=!0,systemClockOffset:i=e.systemClockOffset||0,sha256:o}=e;let a;return a=e.signer?y(e.signer):e.regionInfoProvider?()=>y(e.region)().then((async t=>[await e.regionInfoProvider(t,{useFipsEndpoint:await e.useFipsEndpoint(),useDualstackEndpoint:await e.useDualstackEndpoint()})||{},t])).then((([t,r])=>{const{signingRegion:i,signingService:a}=t;e.signingRegion=e.signingRegion||i||r,e.signingName=e.signingName||a||e.serviceId;const c={...e,credentials:n,region:e.signingRegion,service:e.signingName,sha256:o,uriEscapePath:s};return new(e.signerConstructor||xt.BB)(c)})):async t=>{const r=(t=Object.assign({},{name:"sigv4",signingName:e.signingName||e.defaultSigningName,signingRegion:await y(e.region)(),properties:{}},t)).signingRegion,i=t.signingName;e.signingRegion=e.signingRegion||r,e.signingName=e.signingName||i||e.serviceId;const a={...e,credentials:n,region:e.signingRegion,service:e.signingName,sha256:o,uriEscapePath:s};return new(e.signerConstructor||xt.BB)(a)},{...e,systemClockOffset:i,signingEscapePath:s,credentials:r?async()=>n().then((e=>{return(t=e).$source||(t.$source={}),t.$source.CREDENTIALS_CODE="e",t;var t})):n,signer:a}},At=async(e,t,r)=>({operation:(0,u.u)(t).operation,region:await(0,u.t)(e.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),Et=e=>{const t=[];switch(e.operation){case"GetCredentialsForIdentity":case"GetId":case"GetOpenIdToken":case"UnlinkIdentity":t.push({schemeId:"smithy.api#noAuth"});break;default:t.push(function(e){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"cognito-identity",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}}(e))}return t},kt={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var Rt=r(1958),It=r(6880),Ot=r.n(It);const $t=({serviceId:e,clientVersion:t})=>async r=>{const n="undefined"!=typeof window&&window?.navigator?.userAgent?Ot().parse(window.navigator.userAgent):void 0,s=[["aws-sdk-js",t],["ua","2.1"],[`os/${n?.os?.name||"other"}`,n?.os?.version],["lang/js"],["md/browser",`${n?.browser?.name??"unknown"}_${n?.browser?.version??"unknown"}`]];e&&s.push([`api/${e}`,t]);const i=await(r?.userAgentAppId?.());return i&&s.push([`app/${i}`]),s},Ct="function"==typeof TextEncoder?new TextEncoder:null,Tt=e=>{if("string"==typeof e){if(Ct)return Ct.encode(e).byteLength;let t=e.length;for(let r=t-1;r>=0;r--){const n=e.charCodeAt(r);n>127&&n<=2047?t++:n>2047&&n<=65535&&(t+=2),n>=56320&&n<=57343&&r--}return t}if("number"==typeof e.byteLength)return e.byteLength;if("number"==typeof e.size)return e.size;throw new Error(`Body Length computation failed for ${e}`)},Pt=e=>new Date(Date.now()+e),jt=e=>n.cS.isInstance(e)?e.headers?.date??e.headers?.Date:void 0,Mt=(e,t)=>{const r=Date.parse(e);return((e,t)=>Math.abs(Pt(t).getTime()-e)>=3e5)(r,t)?r-Date.now():t},Dt=(e,t)=>{if(!t)throw new Error(`Property \`${e}\` is not resolved for AWS SDK SigV4Auth`);return t};class Nt{async sign(e,t,r){if(!n.Kd.isInstance(e))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");const s=await(async e=>{const t=Dt("context",e.context),r=Dt("config",e.config),n=t.endpointV2?.properties?.authSchemes?.[0],s=Dt("signer",r.signer),i=await s(n),o=e?.signingRegion,a=e?.signingRegionSet,c=e?.signingName;return{config:r,signer:i,signingRegion:o,signingRegionSet:a,signingName:c}})(r),{config:i,signer:o}=s;let{signingRegion:a,signingName:c}=s;const u=r.context;if(u?.authSchemes?.length){const[e,t]=u.authSchemes;"sigv4a"===e?.name&&"sigv4"===t?.name&&(a=t?.signingRegion??a,c=t?.signingName??c)}return await o.sign(e,{signingDate:Pt(i.systemClockOffset),signingRegion:a,signingService:c})}errorHandler(e){return t=>{const r=t.ServerTime??jt(t.$response);if(r){const n=Dt("config",e.config),s=n.systemClockOffset;n.systemClockOffset=Mt(r,n.systemClockOffset),n.systemClockOffset!==s&&t.$metadata&&(t.$metadata.clockSkewCorrected=!0)}throw t}}successHandler(e,t){const r=jt(e);if(r){const e=Dt("config",t.config);e.systemClockOffset=Mt(r,e.systemClockOffset)}}}const qt="required",Lt="fn",Ft="argv",Ut="ref",Bt=!0,zt="isSet",Wt="booleanEquals",Ht="error",Vt="endpoint",Kt="tree",Gt="PartitionResult",Qt="getAttr",Jt="stringEquals",Zt={[qt]:!1,type:"String"},Yt={[qt]:!0,default:!1,type:"Boolean"},Xt={[Ut]:"Endpoint"},er={[Lt]:Wt,[Ft]:[{[Ut]:"UseFIPS"},!0]},tr={[Lt]:Wt,[Ft]:[{[Ut]:"UseDualStack"},!0]},rr={},nr={[Ut]:"Region"},sr={[Lt]:Qt,[Ft]:[{[Ut]:Gt},"supportsFIPS"]},ir={[Ut]:Gt},or={[Lt]:Wt,[Ft]:[!0,{[Lt]:Qt,[Ft]:[ir,"supportsDualStack"]}]},ar=[er],cr=[tr],ur=[nr],lr={version:"1.0",parameters:{Region:Zt,UseDualStack:Yt,UseFIPS:Yt,Endpoint:Zt},rules:[{conditions:[{[Lt]:zt,[Ft]:[Xt]}],rules:[{conditions:ar,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Ht},{conditions:cr,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Ht},{endpoint:{url:Xt,properties:rr,headers:rr},type:Vt}],type:Kt},{conditions:[{[Lt]:zt,[Ft]:ur}],rules:[{conditions:[{[Lt]:"aws.partition",[Ft]:ur,assign:Gt}],rules:[{conditions:[er,tr],rules:[{conditions:[{[Lt]:Wt,[Ft]:[Bt,sr]},or],rules:[{conditions:[{[Lt]:Jt,[Ft]:[nr,"us-east-1"]}],endpoint:{url:"https://cognito-identity-fips.us-east-1.amazonaws.com",properties:rr,headers:rr},type:Vt},{conditions:[{[Lt]:Jt,[Ft]:[nr,"us-east-2"]}],endpoint:{url:"https://cognito-identity-fips.us-east-2.amazonaws.com",properties:rr,headers:rr},type:Vt},{conditions:[{[Lt]:Jt,[Ft]:[nr,"us-west-1"]}],endpoint:{url:"https://cognito-identity-fips.us-west-1.amazonaws.com",properties:rr,headers:rr},type:Vt},{conditions:[{[Lt]:Jt,[Ft]:[nr,"us-west-2"]}],endpoint:{url:"https://cognito-identity-fips.us-west-2.amazonaws.com",properties:rr,headers:rr},type:Vt},{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:rr,headers:rr},type:Vt}],type:Kt},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Ht}],type:Kt},{conditions:ar,rules:[{conditions:[{[Lt]:Wt,[Ft]:[sr,Bt]}],rules:[{endpoint:{url:"https://cognito-identity-fips.{Region}.{PartitionResult#dnsSuffix}",properties:rr,headers:rr},type:Vt}],type:Kt},{error:"FIPS is enabled but this partition does not support FIPS",type:Ht}],type:Kt},{conditions:cr,rules:[{conditions:[or],rules:[{conditions:[{[Lt]:Jt,[Ft]:["aws",{[Lt]:Qt,[Ft]:[ir,"name"]}]}],endpoint:{url:"https://cognito-identity.{Region}.amazonaws.com",properties:rr,headers:rr},type:Vt},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:rr,headers:rr},type:Vt}],type:Kt},{error:"DualStack is enabled but this partition does not support DualStack",type:Ht}],type:Kt},{endpoint:{url:"https://cognito-identity.{Region}.{PartitionResult#dnsSuffix}",properties:rr,headers:rr},type:Vt}],type:Kt}],type:Kt},{error:"Invalid Configuration: Missing Region",type:Ht}]},dr=new class{constructor({size:e,params:t}){this.data=new Map,this.parameters=[],this.capacity=e??50,t&&(this.parameters=t)}get(e,t){const r=this.hash(e);if(!1===r)return t();if(!this.data.has(r)){if(this.data.size>this.capacity+10){const e=this.data.keys();let t=0;for(;;){const{value:r,done:n}=e.next();if(this.data.delete(r),n||++t>10)break}}this.data.set(r,t())}return this.data.get(r)}size(){return this.data.size}hash(e){let t="";const{parameters:r}=this;if(0===r.length)return!1;for(const n of r){const r=String(e[n]??"");if(r.includes("|;"))return!1;t+=r+"|;"}return t}}({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),fr=(e,t={})=>dr.get(e,(()=>((e,t)=>{const{endpointParams:r,logger:n}=t,{parameters:s,rules:i}=e;t.logger?.debug?.(`${T} Initial EndpointParams: ${P(r)}`);const o=Object.entries(s).filter((([,e])=>null!=e.default)).map((([e,t])=>[e,t.default]));if(o.length>0)for(const[e,t]of o)r[e]=r[e]??t;const a=Object.entries(s).filter((([,e])=>e.required)).map((([e])=>e));for(const e of a)if(null==r[e])throw new j(`Missing required parameter: '${e}'`);const c=J(i,{endpointParams:r,logger:n,referenceRecord:{}});return t.logger?.debug?.(`${T} Resolved endpoint: ${P(c)}`),c})(lr,{endpointParams:e,logger:t.logger})));C.aws=X;var pr=r(6055);const hr=["in-region","cross-region","mobile","standard","legacy"],mr=e=>{const t=(({defaultsMode:e}={})=>(0,pr.Bj)((async()=>{const t="function"==typeof e?await e():e;switch(t?.toLowerCase()){case"auto":return Promise.resolve((()=>{const e="undefined"!=typeof window&&window?.navigator?.userAgent?Ot().parse(window.navigator.userAgent):void 0,t=e?.platform?.type;return"tablet"===t||"mobile"===t})()?"mobile":"standard");case"mobile":case"in-region":case"cross-region":case"standard":case"legacy":return Promise.resolve(t?.toLocaleLowerCase());case void 0:return Promise.resolve("legacy");default:throw new Error(`Invalid parameter for "defaultsMode", expect ${hr.join(", ")}, got ${t}`)}})))(e),r=()=>t().then(st),n=(e=>({apiVersion:"2014-06-30",base64Decoder:e?.base64Decoder??v.E,base64Encoder:e?.base64Encoder??v.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??fr,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??Et,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new Nt},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new A}],logger:e?.logger??new dt,serviceId:e?.serviceId??"Cognito Identity",urlParser:e?.urlParser??ve,utf8Decoder:e?.utf8Decoder??w.a,utf8Encoder:e?.utf8Encoder??b}))(e);return{...n,...e,runtime:"browser",defaultsMode:t,bodyLengthChecker:e?.bodyLengthChecker??Tt,credentialDefaultProvider:e?.credentialDefaultProvider??(e=>()=>Promise.reject(new Error("Credential is missing"))),defaultUserAgentProvider:e?.defaultUserAgentProvider??$t({serviceId:n.serviceId,clientVersion:"3.731.1"}),maxAttempts:e?.maxAttempts??3,region:e?.region??(()=>Promise.reject("Region is missing")),requestHandler:x.NC.create(e?.requestHandler??r),retryMode:e?.retryMode??(async()=>(await r()).retryMode||Ae),sha256:e?.sha256??Rt.I,streamCollector:e?.streamCollector??x.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(()=>Promise.resolve(!1)),useFipsEndpoint:e?.useFipsEndpoint??(()=>Promise.resolve(!1))}},gr=e=>{let t=async()=>{if(void 0===e.region)throw new Error("Region is missing from runtimeConfig");const t=e.region;return"string"==typeof t?t:t()};return{setRegion(e){t=e},region:()=>t}},yr=e=>{const t=e.httpAuthSchemes;let r=e.httpAuthSchemeProvider,n=e.credentials;return{setHttpAuthScheme(e){const r=t.findIndex((t=>t.schemeId===e.schemeId));-1===r?t.push(e):t.splice(r,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){r=e},httpAuthSchemeProvider:()=>r,setCredentials(e){n=e},credentials:()=>n}},vr=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()});class br extends Ve{config;constructor(...[e]){var t;const r=function(e){const t=y(e.userAgentAppId??void 0);return{...e,customUserAgent:"string"==typeof e.customUserAgent?[[e.customUserAgent]]:e.customUserAgent,userAgentAppId:async()=>{const r=await t();if(!function(e){return void 0===e||"string"==typeof e&&e.length<=50}(r)){const t="NoOpLogger"!==e.logger?.constructor?.name&&e.logger?e.logger:console;"string"!=typeof r?t?.warn("userAgentAppId must be a string or undefined."):r.length>50&&t?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return r}}}((t=mr(e||{}),{...t,useDualstackEndpoint:t.useDualstackEndpoint??!1,useFipsEndpoint:t.useFipsEndpoint??!1,defaultSigningName:"cognito-identity"})),c=(e=>{const t=e.tls??!0,{endpoint:r}=e,n=null!=r?async()=>be(await(0,u.t)(r)()):void 0,s=!!r,i={...e,endpoint:n,tls:t,isCustomEndpoint:s,useDualstackEndpoint:(0,u.t)(e.useDualstackEndpoint??!1),useFipsEndpoint:(0,u.t)(e.useFipsEndpoint??!1)};let o;return i.serviceConfiguredEndpoint=async()=>(e.serviceId&&!o&&(o=ye(e.serviceId)),o),i})((e=>{const{region:t,useFipsEndpoint:r}=e;if(!t)throw new Error("Region is missing");return{...e,region:async()=>{if("string"==typeof t)return le(t);const e=await t();return le(e)},useFipsEndpoint:async()=>{const e="string"==typeof t?t:await t();return!!ue(e)||("function"!=typeof r?Promise.resolve(!!r):r())}}})((e=>{const{retryStrategy:t}=e,r=(0,u.t)(e.maxAttempts??3);return{...e,maxAttempts:r,retryStrategy:async()=>t||(await(0,u.t)(e.retryMode)()===Se.ADAPTIVE?new je(r):new Pe(r))}})(r))),d=((e,t)=>{const r={...gr(e),...ut(e),...(0,n.eS)(e),...yr(e)};return t.forEach((e=>e.configure(r))),{...e,...(i=r,{region:i.region()}),...(s=r,{...ot(s),...ct(s)}),...(0,n.jt)(r),...vr(r)};var s,i})({...St(c)},e?.extensions||[]);var f;super(d),this.config=d,this.middlewareStack.use((f=this.config,{applyToStack:e=>{e.add((e=>(t,r)=>async s=>{const{request:i}=s;if(!n.Kd.isInstance(i))return t(s);const{headers:o}=i,a=r?.userAgent?.map(oe)||[],c=(await e.defaultUserAgentProvider()).map(oe);await async function(e,t,r){const n=r.request;if("rpc-v2-cbor"===n?.headers?.["smithy-protocol"]&&ee(e,"PROTOCOL_RPC_V2_CBOR","M"),"function"==typeof t.retryStrategy){const r=await t.retryStrategy();"function"==typeof r.acquireInitialRetryToken?r.constructor?.name?.includes("Adaptive")?ee(e,"RETRY_MODE_ADAPTIVE","F"):ee(e,"RETRY_MODE_STANDARD","E"):ee(e,"RETRY_MODE_LEGACY","D")}if("function"==typeof t.accountIdEndpointMode){const r=e.endpointV2;switch(String(r?.url?.hostname).match(te)&&ee(e,"ACCOUNT_ID_ENDPOINT","O"),await(t.accountIdEndpointMode?.())){case"disabled":ee(e,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":ee(e,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":ee(e,"ACCOUNT_ID_MODE_REQUIRED","R")}}const s=e.__smithy_context?.selectedHttpAuthScheme?.identity;if(s?.$source){const t=s;t.accountId&&ee(e,"RESOLVED_ACCOUNT_ID","T");for(const[r,n]of Object.entries(t.$source??{}))ee(e,r,n)}}(r,e,s);const u=r;c.push(`m/${function(e){let t="";for(const r in e){const n=e[r];if(!(t.length+n.length+1<=1024))break;t.length?t+=","+n:t+=n}return t}(Object.assign({},r.__smithy_context?.features,u.__aws_sdk_context?.features))}`);const l=e?.customUserAgent?.map(oe)||[],d=await e.userAgentAppId();d&&c.push(oe([`app/${d}`]));const f=[].concat([...c,...a,...l]).join(" "),p=[...c.filter((e=>e.startsWith("aws-sdk-"))),...l].join(" ");return"browser"!==e.runtime?(p&&(o[ne]=o[ne]?`${o[re]} ${p}`:p),o[re]=f):o[ne]=f,t({...s,request:i})})(f),ae)}})),this.middlewareStack.use((e=>({applyToStack:t=>{t.add((e=>(t,r)=>async s=>{let i=await e.retryStrategy();const o=await e.maxAttempts();if(!(e=>void 0!==e.acquireInitialRetryToken&&void 0!==e.refreshRetryTokenForRetry&&void 0!==e.recordSuccess)(i))return i?.mode&&(r.userAgent=[...r.userAgent||[],["cfg/retry-mode",i.mode]]),i.retry(t,s);{let e=await i.acquireInitialRetryToken(r.partition_id),c=new Error,u=0,l=0;const{request:d}=s,f=n.Kd.isInstance(d);for(f&&(d.headers["amz-sdk-invocation-id"]=Fe());;)try{f&&(d.headers["amz-sdk-request"]=`attempt=${u+1}; max=${o}`);const{response:r,output:n}=await t(s);return i.recordSuccess(e),n.$metadata.attempts=u+1,n.$metadata.totalRetryDelay=l,{response:r,output:n}}catch(t){const n=vt(t);if(c=(a=t)instanceof Error?a:a instanceof Object?Object.assign(new Error,a):"string"==typeof a?new Error(a):new Error(`AWS SDK error wrapper for ${a}`),f&&yt(d))throw(r.logger instanceof dt?console:r.logger)?.warn("An error was encountered in a non-retryable streaming request."),c;try{e=await i.refreshRetryTokenForRetry(e,n)}catch(e){throw c.$metadata||(c.$metadata={}),c.$metadata.attempts=u+1,c.$metadata.totalRetryDelay=l,c}u=e.getRetryCount();const s=e.getRetryDelay();l+=s,await new Promise((e=>setTimeout(e,s)))}}var a})(e),wt)}}))(this.config)),this.middlewareStack.use((e=>({applyToStack:t=>{var r;t.add((r=e.bodyLengthChecker,e=>async t=>{const s=t.request;if(n.Kd.isInstance(s)){const{body:e,headers:t}=s;if(e&&-1===Object.keys(t).map((e=>e.toLowerCase())).indexOf(de))try{const t=r(e);s.headers={...s.headers,[de]:String(t)}}catch(e){}}return e({...t,request:s})}),fe)}}))(this.config)),this.middlewareStack.use((e=>({applyToStack:t=>{t.add((e=>t=>async r=>{if(!n.Kd.isInstance(r.request))return t(r);const{request:s}=r,{handlerProtocol:i=""}=e.requestHandler.metadata||{};if(i.indexOf("h2")>=0&&!s.headers[":authority"])delete s.headers.host,s.headers[":authority"]=s.hostname+(s.port?":"+s.port:"");else if(!s.headers.host){let e=s.hostname;null!=s.port&&(e+=`:${s.port}`),s.headers.host=e}return t(r)})(e),s)}}))(this.config)),this.middlewareStack.use((this.config,{applyToStack:e=>{e.add(((e,t)=>async r=>{try{const n=await e(r),{clientName:s,commandName:i,logger:o,dynamoDbDocumentClientOptions:a={}}=t,{overrideInputFilterSensitiveLog:c,overrideOutputFilterSensitiveLog:u}=a,l=c??t.inputFilterSensitiveLog,d=u??t.outputFilterSensitiveLog,{$metadata:f,...p}=n.output;return o?.info?.({clientName:s,commandName:i,input:l(r.input),output:d(p),metadata:f}),n}catch(e){const{clientName:n,commandName:s,logger:i,dynamoDbDocumentClientOptions:o={}}=t,{overrideInputFilterSensitiveLog:a}=o,c=a??t.inputFilterSensitiveLog;throw i?.error?.({clientName:n,commandName:s,input:c(r.input),error:e,metadata:e.$metadata}),e}}),i)}})),this.middlewareStack.use((e=>({applyToStack:t=>{t.add((e=>t=>async r=>{const{request:s}=r;if(!n.Kd.isInstance(s)||"node"!==e.runtime||s.headers.hasOwnProperty(o))return t(r);const i="MISSING_ENV_VAR"._X_AMZN_TRACE_ID,a=e=>"string"==typeof e&&e.length>0;return a("MISSING_ENV_VAR".AWS_LAMBDA_FUNCTION_NAME)&&a(i)&&(s.headers[o]=i),t({...r,request:s})})(e),a)}}))(this.config)),this.middlewareStack.use(((e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r})=>({applyToStack:n=>{n.addRelativeTo(((e,t)=>(r,n)=>async s=>{const i=e.httpAuthSchemeProvider(await t.httpAuthSchemeParametersProvider(e,n,s.input)),o=function(e){const t=new Map;for(const r of e)t.set(r.schemeId,r);return t}(e.httpAuthSchemes),a=(0,u.u)(n),c=[];for(const r of i){const s=o.get(r.schemeId);if(!s){c.push(`HttpAuthScheme \`${r.schemeId}\` was not enabled for this service.`);continue}const i=s.identityProvider(await t.identityProviderConfigProvider(e));if(!i){c.push(`HttpAuthScheme \`${r.schemeId}\` did not have an IdentityProvider configured.`);continue}const{identityProperties:u={},signingProperties:l={}}=r.propertiesExtractor?.(e,n)||{};r.identityProperties=Object.assign(r.identityProperties||{},u),r.signingProperties=Object.assign(r.signingProperties||{},l),a.selectedHttpAuthScheme={httpAuthOption:r,identity:await i(r.identityProperties),signer:s.signer};break}if(!a.selectedHttpAuthScheme)throw new Error(c.join("\n"));return r(s)})(e,{httpAuthSchemeParametersProvider:t,identityProviderConfigProvider:r}),l)}}))(this.config,{httpAuthSchemeParametersProvider:At,identityProviderConfigProvider:async e=>new S({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((this.config,{applyToStack:e=>{e.addRelativeTo(((e,t)=>async r=>{if(!n.Kd.isInstance(r.request))return e(r);const s=(0,u.u)(t).selectedHttpAuthScheme;if(!s)throw new Error("No HttpAuthScheme was selected: unable to sign request");const{httpAuthOption:{signingProperties:i={}},identity:o,signer:a}=s,c=await e({...r,request:await a.sign(r.request,o,i)}).catch((a.errorHandler||h)(i));return(a.successHandler||m)(c.response,i),c}),g)}}))}destroy(){super.destroy()}}class wr extends rt{constructor(e){super(e),Object.setPrototypeOf(this,wr.prototype)}}class _r extends wr{name="InternalErrorException";$fault="server";constructor(e){super({name:"InternalErrorException",$fault:"server",...e}),Object.setPrototypeOf(this,_r.prototype)}}class xr extends wr{name="InvalidParameterException";$fault="client";constructor(e){super({name:"InvalidParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,xr.prototype)}}class Sr extends wr{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,Sr.prototype)}}class Ar extends wr{name="NotAuthorizedException";$fault="client";constructor(e){super({name:"NotAuthorizedException",$fault:"client",...e}),Object.setPrototypeOf(this,Ar.prototype)}}class Er extends wr{name="ResourceConflictException";$fault="client";constructor(e){super({name:"ResourceConflictException",$fault:"client",...e}),Object.setPrototypeOf(this,Er.prototype)}}class kr extends wr{name="TooManyRequestsException";$fault="client";constructor(e){super({name:"TooManyRequestsException",$fault:"client",...e}),Object.setPrototypeOf(this,kr.prototype)}}class Rr extends wr{name="ResourceNotFoundException";$fault="client";constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,Rr.prototype)}}class Ir extends wr{name="ExternalServiceException";$fault="client";constructor(e){super({name:"ExternalServiceException",$fault:"client",...e}),Object.setPrototypeOf(this,Ir.prototype)}}class Or extends wr{name="InvalidIdentityPoolConfigurationException";$fault="client";constructor(e){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,Or.prototype)}}class $r extends wr{name="DeveloperUserAlreadyRegisteredException";$fault="client";constructor(e){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...e}),Object.setPrototypeOf(this,$r.prototype)}}class Cr extends wr{name="ConcurrentModificationException";$fault="client";constructor(e){super({name:"ConcurrentModificationException",$fault:"client",...e}),Object.setPrototypeOf(this,Cr.prototype)}}const Tr=e=>({...e,...e.Logins&&{Logins:Qe}}),Pr=e=>({...e,...e.SecretKey&&{SecretKey:Qe}}),jr=e=>({...e,...e.Credentials&&{Credentials:Pr(e.Credentials)}}),Mr=e=>({...e,...e.Logins&&{Logins:Qe}}),Dr=(e,t)=>((e,t)=>(async(e=new Uint8Array,t)=>{if(e instanceof Uint8Array)return _.mutate(e);if(!e)return _.mutate(new Uint8Array);const r=t.streamCollector(e);return _.mutate(await r)})(e,t).then((e=>t.utf8Encoder(e))))(e,t).then((e=>{if(e.length)try{return JSON.parse(e)}catch(t){throw"SyntaxError"===t?.name&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}return{}})),Nr=async(e,t)=>{const r=await Dr(e,t);return r.message=r.message??r.Message,r},qr=async(e,t)=>{const r=an("GetCredentialsForIdentity");let n;return n=JSON.stringify(gt(e)),on(t,r,"/",void 0,n)},Lr=async(e,t)=>{const r=an("GetId");let n;return n=JSON.stringify(gt(e)),on(t,r,"/",void 0,n)},Fr=async(e,t)=>{if(e.statusCode>=300)return Br(e,t);const r=await Dr(e.body,t);let n={};return n=tn(r,t),{$metadata:rn(e),...n}},Ur=async(e,t)=>{if(e.statusCode>=300)return Br(e,t);const r=await Dr(e.body,t);let n={};return n=gt(r),{$metadata:rn(e),...n}},Br=async(e,t)=>{const r={...e,body:await Nr(e.body,t)},n=((e,t)=>{const r=e=>{let t=e;return"number"==typeof t&&(t=t.toString()),t.indexOf(",")>=0&&(t=t.split(",")[0]),t.indexOf(":")>=0&&(t=t.split(":")[0]),t.indexOf("#")>=0&&(t=t.split("#")[1]),t},n=(s=e.headers,Object.keys(s).find((e=>e.toLowerCase()==="x-amzn-errortype".toLowerCase())));var s;return void 0!==n?r(e.headers[n]):void 0!==t.code?r(t.code):void 0!==t.__type?r(t.__type):void 0})(e,r.body);switch(n){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await Vr(r,t);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await Gr(r,t);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await Qr(r,t);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await Jr(r,t);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await Zr(r,t);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await Xr(r,t);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await Yr(r,t);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await Hr(r,t);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await Kr(r,t);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await Wr(r,t);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await zr(r,t);default:const s=r.body;return nn({output:e,parsedBody:s,errorCode:n})}},zr=async(e,t)=>{const r=e.body,n=gt(r),s=new Cr({$metadata:rn(e),...n});return nt(s,r)},Wr=async(e,t)=>{const r=e.body,n=gt(r),s=new $r({$metadata:rn(e),...n});return nt(s,r)},Hr=async(e,t)=>{const r=e.body,n=gt(r),s=new Ir({$metadata:rn(e),...n});return nt(s,r)},Vr=async(e,t)=>{const r=e.body,n=gt(r),s=new _r({$metadata:rn(e),...n});return nt(s,r)},Kr=async(e,t)=>{const r=e.body,n=gt(r),s=new Or({$metadata:rn(e),...n});return nt(s,r)},Gr=async(e,t)=>{const r=e.body,n=gt(r),s=new xr({$metadata:rn(e),...n});return nt(s,r)},Qr=async(e,t)=>{const r=e.body,n=gt(r),s=new Sr({$metadata:rn(e),...n});return nt(s,r)},Jr=async(e,t)=>{const r=e.body,n=gt(r),s=new Ar({$metadata:rn(e),...n});return nt(s,r)},Zr=async(e,t)=>{const r=e.body,n=gt(r),s=new Er({$metadata:rn(e),...n});return nt(s,r)},Yr=async(e,t)=>{const r=e.body,n=gt(r),s=new Rr({$metadata:rn(e),...n});return nt(s,r)},Xr=async(e,t)=>{const r=e.body,n=gt(r),s=new kr({$metadata:rn(e),...n});return nt(s,r)},en=(e,t)=>ft(e,{AccessKeyId:Ze,Expiration:e=>(e=>{if(null==e)throw new TypeError("Expected a non-null value");return e})((e=>{if(null==e)return;let t;if("number"==typeof e)t=e;else if("string"==typeof e)t=(e=>Je("string"==typeof e?Xe(e):e))(e);else{if("object"!=typeof e||1!==e.tag)throw new TypeError("Epoch timestamps must be expressed as floating point numbers or their string representation");t=e.value}if(Number.isNaN(t)||t===1/0||t===-1/0)throw new TypeError("Epoch timestamps must be valid, non-Infinite, non-NaN numerics");return new Date(Math.round(1e3*t))})(Je(e))),SecretKey:Ze,SessionToken:Ze}),tn=(e,t)=>ft(e,{Credentials:e=>en(e),IdentityId:Ze}),rn=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),nn=(sn=wr,({output:e,parsedBody:t,errorCode:r})=>{(({output:e,parsedBody:t,exceptionCtor:r,errorCode:n})=>{const s=(e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}))(e),i=s.httpStatusCode?s.httpStatusCode+"":void 0,o=new r({name:t?.code||t?.Code||n||i||"UnknownError",$fault:"client",$metadata:s});throw nt(o,t)})({output:e,parsedBody:t,exceptionCtor:sn,errorCode:r})});var sn;const on=async(e,t,r,s,i)=>{const{hostname:o,protocol:a="https",port:c,path:u}=await e.endpoint(),l={protocol:a,hostname:o,port:c,method:"POST",path:u.endsWith("/")?u.slice(0,-1)+r:u+r,headers:t};return void 0!==s&&(l.hostname=s),void 0!==i&&(l.body=i),new n.Kd(l)};function an(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${e}`}}class cn extends(Ke.classBuilder().ep(kt).m((function(e,t,r,n){return[p(r,this.serialize,this.deserialize),xe(r,e.getEndpointParameterInstructions())]})).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(Tr,jr).ser(qr).de(Fr).build()){}class un extends(Ke.classBuilder().ep(kt).m((function(e,t,r,n){return[p(r,this.serialize,this.deserialize),xe(r,e.getEndpointParameterInstructions())]})).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(Mr,void 0).ser(Lr).de(Ur).build()){}},9389:(e,t,r)=>{"use strict";r.d(t,{NC:()=>c,kv:()=>l});var n=r(5479),s=r(2531);function i(e,t){return new Request(e,t)}function o(e=0){return new Promise(((t,r)=>{e&&setTimeout((()=>{const t=new Error(`Request did not complete within ${e} ms`);t.name="TimeoutError",r(t)}),e)}))}const a={supported:void 0};class c{static create(e){return"function"==typeof e?.handle?e:new c(e)}constructor(e){"function"==typeof e?this.configProvider=e().then((e=>e||{})):(this.config=e??{},this.configProvider=Promise.resolve(this.config)),void 0===a.supported&&(a.supported=Boolean("undefined"!=typeof Request&&"keepalive"in i("https://[::1]")))}destroy(){}async handle(e,{abortSignal:t}={}){this.config||(this.config=await this.configProvider);const r=this.config.requestTimeout,c=!0===this.config.keepAlive,u=this.config.credentials;if(t?.aborted){const e=new Error("Request aborted");return e.name="AbortError",Promise.reject(e)}let l=e.path;const d=function(e){const t=[];for(let r of Object.keys(e).sort()){const n=e[r];if(r=(0,s.o)(r),Array.isArray(n))for(let e=0,i=n.length;e<i;e++)t.push(`${r}=${(0,s.o)(n[e])}`);else{let e=r;(n||"string"==typeof n)&&(e+=`=${(0,s.o)(n)}`),t.push(e)}}return t.join("&")}(e.query||{});d&&(l+=`?${d}`),e.fragment&&(l+=`#${e.fragment}`);let f="";null==e.username&&null==e.password||(f=`${e.username??""}:${e.password??""}@`);const{port:p,method:h}=e,m=`${e.protocol}//${f}${e.hostname}${p?`:${p}`:""}${l}`,g="GET"===h||"HEAD"===h?void 0:e.body,y={body:g,headers:new Headers(e.headers),method:h,credentials:u};this.config?.cache&&(y.cache=this.config.cache),g&&(y.duplex="half"),"undefined"!=typeof AbortController&&(y.signal=t),a.supported&&(y.keepalive=c),"function"==typeof this.config.requestInit&&Object.assign(y,this.config.requestInit(e));let v=()=>{};const b=i(m,y),w=[fetch(b).then((e=>{const t=e.headers,r={};for(const e of t.entries())r[e[0]]=e[1];return null!=e.body?{response:new n.cS({headers:r,reason:e.statusText,statusCode:e.status,body:e.body})}:e.blob().then((t=>({response:new n.cS({headers:r,reason:e.statusText,statusCode:e.status,body:t})})))})),o(r)];return t&&w.push(new Promise(((e,r)=>{const n=()=>{const e=new Error("Request aborted");e.name="AbortError",r(e)};if("function"==typeof t.addEventListener){const e=t;e.addEventListener("abort",n,{once:!0}),v=()=>e.removeEventListener("abort",n)}else t.onabort=n}))),Promise.race(w).finally(v)}updateHttpClientConfig(e,t){this.config=void 0,this.configProvider=this.configProvider.then((r=>(r[e]=t,r)))}httpHandlerConfigs(){return this.config??{}}}var u=r(1302);const l=async e=>"function"==typeof Blob&&e instanceof Blob||"Blob"===e.constructor?.name?void 0!==Blob.prototype.arrayBuffer?new Uint8Array(await e.arrayBuffer()):async function(e){const t=await function(e){return new Promise(((t,r)=>{const n=new FileReader;n.onloadend=()=>{if(2!==n.readyState)return r(new Error("Reader aborted too early"));const e=n.result??"",s=e.indexOf(","),i=s>-1?s+1:e.length;t(e.substring(i))},n.onabort=()=>r(new Error("Read aborted")),n.onerror=()=>r(n.error),n.readAsDataURL(e)}))}(e),r=(0,u.E)(t);return new Uint8Array(r)}(e):async function(e){const t=[],r=e.getReader();let n=!1,s=0;for(;!n;){const{done:e,value:i}=await r.read();i&&(t.push(i),s+=i.length),n=e}const i=new Uint8Array(s);let o=0;for(const e of t)i.set(e,o),o+=e.length;return i}(e)},6055:(e,t,r)=>{"use strict";r.d(t,{C1:()=>s,Bj:()=>i});class n extends Error{constructor(e,t=!0){let r,s=!0;"boolean"==typeof t?(r=void 0,s=t):null!=t&&"object"==typeof t&&(r=t.logger,s=t.tryNextLink??!0),super(e),this.name="ProviderError",this.tryNextLink=s,Object.setPrototypeOf(this,n.prototype),r?.debug?.(`@smithy/property-provider ${s?"->":"(!)"} ${e}`)}static from(e,t=!0){return Object.assign(new this(e.message,t),e)}}class s extends n{constructor(e,t=!0){super(e,t),this.name="CredentialsProviderError",Object.setPrototypeOf(this,s.prototype)}}const i=(e,t,r)=>{let n,s,i,o=!1;const a=async()=>{s||(s=e());try{n=await s,i=!0,o=!1}finally{s=void 0}return n};return void 0===t?async e=>(i&&!e?.forceRefresh||(n=await a()),n):async e=>(i&&!e?.forceRefresh||(n=await a()),o?n:r&&!r(n)?(o=!0,n):t(n)?(await a(),n):n)}},5479:(e,t,r)=>{"use strict";r.d(t,{Kd:()=>i,cS:()=>o,eS:()=>n,jt:()=>s});const n=e=>{let t=e.httpHandler;return{setHttpHandler(e){t=e},httpHandler:()=>t,updateHttpClientConfig(e,r){t.updateHttpClientConfig(e,r)},httpHandlerConfigs:()=>t.httpHandlerConfigs()}},s=e=>({httpHandler:e.httpHandler()});r(7523);class i{constructor(e){this.method=e.method||"GET",this.hostname=e.hostname||"localhost",this.port=e.port,this.query=e.query||{},this.headers=e.headers||{},this.body=e.body,this.protocol=e.protocol?":"!==e.protocol.slice(-1)?`${e.protocol}:`:e.protocol:"https:",this.path=e.path?"/"!==e.path.charAt(0)?`/${e.path}`:e.path:"/",this.username=e.username,this.password=e.password,this.fragment=e.fragment}static clone(e){const t=new i({...e,headers:{...e.headers}});var r;return t.query&&(t.query=(r=t.query,Object.keys(r).reduce(((e,t)=>{const n=r[t];return{...e,[t]:Array.isArray(n)?[...n]:n}}),{}))),t}static isInstance(e){if(!e)return!1;const t=e;return"method"in t&&"protocol"in t&&"hostname"in t&&"path"in t&&"object"==typeof t.query&&"object"==typeof t.headers}clone(){return i.clone(this)}}class o{constructor(e){this.statusCode=e.statusCode,this.reason=e.reason,this.headers=e.headers||{},this.body=e.body}static isInstance(e){if(!e)return!1;const t=e;return"number"==typeof t.statusCode&&"object"==typeof t.headers}}},4428:(e,t,r)=>{"use strict";r.d(t,{BB:()=>D});var n=r(5623),s=r(7135),i=r(2531),o=r(2637);const a=e=>"string"==typeof e?(0,o.a)(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e),c="X-Amz-Date",u="X-Amz-Signature",l="X-Amz-Security-Token",d="authorization",f=c.toLowerCase(),p=[d,f,"date"],h=u.toLowerCase(),m="x-amz-content-sha256",g=l.toLowerCase(),y={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},v=/^proxy-/,b=/^sec-/,w="AWS4-HMAC-SHA256",_="AWS4-HMAC-SHA256-PAYLOAD",x="aws4_request",S={},A=[],E=(e,t,r)=>`${e}/${t}/${r}/${x}`,k=(e,t,r)=>{const n=new e(t);return n.update(a(r)),n.digest()},R=({headers:e},t,r)=>{const n={};for(const s of Object.keys(e).sort()){if(null==e[s])continue;const i=s.toLowerCase();(i in y||t?.has(i)||v.test(i)||b.test(i))&&(!r||r&&!r.has(i))||(n[i]=e[s].trim().replace(/\s+/g," "))}return n},I=async({headers:e,body:t},r)=>{for(const t of Object.keys(e))if(t.toLowerCase()===m)return e[t];if(null==t)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";if("string"==typeof t||ArrayBuffer.isView(t)||(s=t,"function"==typeof ArrayBuffer&&s instanceof ArrayBuffer||"[object ArrayBuffer]"===Object.prototype.toString.call(s))){const e=new r;return e.update(a(t)),(0,n.n)(await e.digest())}var s;return"UNSIGNED-PAYLOAD"};class O{format(e){const t=[];for(const r of Object.keys(e)){const n=(0,o.a)(r);t.push(Uint8Array.from([n.byteLength]),n,this.formatHeaderValue(e[r]))}const r=new Uint8Array(t.reduce(((e,t)=>e+t.byteLength),0));let n=0;for(const e of t)r.set(e,n),n+=e.byteLength;return r}formatHeaderValue(e){switch(e.type){case"boolean":return Uint8Array.from([e.value?0:1]);case"byte":return Uint8Array.from([2,e.value]);case"short":const t=new DataView(new ArrayBuffer(3));return t.setUint8(0,3),t.setInt16(1,e.value,!1),new Uint8Array(t.buffer);case"integer":const r=new DataView(new ArrayBuffer(5));return r.setUint8(0,4),r.setInt32(1,e.value,!1),new Uint8Array(r.buffer);case"long":const s=new Uint8Array(9);return s[0]=5,s.set(e.value.bytes,1),s;case"binary":const i=new DataView(new ArrayBuffer(3+e.value.byteLength));i.setUint8(0,6),i.setUint16(1,e.value.byteLength,!1);const a=new Uint8Array(i.buffer);return a.set(e.value,3),a;case"string":const c=(0,o.a)(e.value),u=new DataView(new ArrayBuffer(3+c.byteLength));u.setUint8(0,7),u.setUint16(1,c.byteLength,!1);const l=new Uint8Array(u.buffer);return l.set(c,3),l;case"timestamp":const d=new Uint8Array(9);return d[0]=8,d.set(T.fromNumber(e.value.valueOf()).bytes,1),d;case"uuid":if(!C.test(e.value))throw new Error(`Invalid UUID received: ${e.value}`);const f=new Uint8Array(17);return f[0]=9,f.set((0,n.a)(e.value.replace(/\-/g,"")),1),f}}}var $;!function(e){e[e.boolTrue=0]="boolTrue",e[e.boolFalse=1]="boolFalse",e[e.byte=2]="byte",e[e.short=3]="short",e[e.integer=4]="integer",e[e.long=5]="long",e[e.byteArray=6]="byteArray",e[e.string=7]="string",e[e.timestamp=8]="timestamp",e[e.uuid=9]="uuid"}($||($={}));const C=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;class T{constructor(e){if(this.bytes=e,8!==e.byteLength)throw new Error("Int64 buffers must be exactly 8 bytes")}static fromNumber(e){if(e>0x8000000000000000||e<-0x8000000000000000)throw new Error(`${e} is too large (or, if negative, too small) to represent as an Int64`);const t=new Uint8Array(8);for(let r=7,n=Math.abs(Math.round(e));r>-1&&n>0;r--,n/=256)t[r]=n;return e<0&&P(t),new T(t)}valueOf(){const e=this.bytes.slice(0),t=128&e[0];return t&&P(e),parseInt((0,n.n)(e),16)*(t?-1:1)}toString(){return String(this.valueOf())}}function P(e){for(let t=0;t<8;t++)e[t]^=255;for(let t=7;t>-1&&(e[t]++,0===e[t]);t--);}var j=r(5479);const M=e=>{e=j.Kd.clone(e);for(const t of Object.keys(e.headers))p.indexOf(t.toLowerCase())>-1&&delete e.headers[t];return e};class D{constructor({applyChecksum:e,credentials:t,region:r,service:n,sha256:i,uriEscapePath:o=!0}){this.headerFormatter=new O,this.service=n,this.sha256=i,this.uriEscapePath=o,this.applyChecksum="boolean"!=typeof e||e,this.regionProvider=(0,s.t)(r),this.credentialProvider=(0,s.t)(t)}async presign(e,t={}){const{signingDate:r=new Date,expiresIn:n=3600,unsignableHeaders:s,unhoistableHeaders:i,signableHeaders:o,hoistableHeaders:a,signingRegion:d,signingService:f}=t,p=await this.credentialProvider();this.validateResolvedCredentials(p);const h=d??await this.regionProvider(),{longDate:m,shortDate:g}=N(r);if(n>604800)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");const y=E(g,h,f??this.service),v=((e,t={})=>{const{headers:r,query:n={}}=j.Kd.clone(e);for(const e of Object.keys(r)){const s=e.toLowerCase();("x-amz-"===s.slice(0,6)&&!t.unhoistableHeaders?.has(s)||t.hoistableHeaders?.has(s))&&(n[e]=r[e],delete r[e])}return{...e,headers:r,query:n}})(M(e),{unhoistableHeaders:i,hoistableHeaders:a});p.sessionToken&&(v.query[l]=p.sessionToken),v.query["X-Amz-Algorithm"]=w,v.query["X-Amz-Credential"]=`${p.accessKeyId}/${y}`,v.query[c]=m,v.query["X-Amz-Expires"]=n.toString(10);const b=R(v,s,o);return v.query["X-Amz-SignedHeaders"]=q(b),v.query[u]=await this.getSignature(m,y,this.getSigningKey(p,h,g,f),this.createCanonicalRequest(v,b,await I(e,this.sha256))),v}async sign(e,t){return"string"==typeof e?this.signString(e,t):e.headers&&e.payload?this.signEvent(e,t):e.message?this.signMessage(e,t):this.signRequest(e,t)}async signEvent({headers:e,payload:t},{signingDate:r=new Date,priorSignature:s,signingRegion:i,signingService:o}){const a=i??await this.regionProvider(),{shortDate:c,longDate:u}=N(r),l=E(c,a,o??this.service),d=await I({headers:{},body:t},this.sha256),f=new this.sha256;f.update(e);const p=(0,n.n)(await f.digest()),h=[_,u,l,s,p,d].join("\n");return this.signString(h,{signingDate:r,signingRegion:a,signingService:o})}async signMessage(e,{signingDate:t=new Date,signingRegion:r,signingService:n}){return this.signEvent({headers:this.headerFormatter.format(e.message.headers),payload:e.message.body},{signingDate:t,signingRegion:r,signingService:n,priorSignature:e.priorSignature}).then((t=>({message:e.message,signature:t})))}async signString(e,{signingDate:t=new Date,signingRegion:r,signingService:s}={}){const i=await this.credentialProvider();this.validateResolvedCredentials(i);const o=r??await this.regionProvider(),{shortDate:c}=N(t),u=new this.sha256(await this.getSigningKey(i,o,c,s));return u.update(a(e)),(0,n.n)(await u.digest())}async signRequest(e,{signingDate:t=new Date,signableHeaders:r,unsignableHeaders:n,signingRegion:s,signingService:i}={}){const o=await this.credentialProvider();this.validateResolvedCredentials(o);const a=s??await this.regionProvider(),c=M(e),{longDate:u,shortDate:l}=N(t),p=E(l,a,i??this.service);c.headers[f]=u,o.sessionToken&&(c.headers[g]=o.sessionToken);const h=await I(c,this.sha256);!((e,t)=>{e=e.toLowerCase();for(const r of Object.keys(t))if(e===r.toLowerCase())return!0;return!1})(m,c.headers)&&this.applyChecksum&&(c.headers[m]=h);const y=R(c,n,r),v=await this.getSignature(u,p,this.getSigningKey(o,a,l,i),this.createCanonicalRequest(c,y,h));return c.headers[d]=`${w} Credential=${o.accessKeyId}/${p}, SignedHeaders=${q(y)}, Signature=${v}`,c}createCanonicalRequest(e,t,r){const n=Object.keys(t).sort();return`${e.method}\n${this.getCanonicalPath(e)}\n${(({query:e={}})=>{const t=[],r={};for(const n of Object.keys(e)){if(n.toLowerCase()===h)continue;const s=(0,i.o)(n);t.push(s);const o=e[n];"string"==typeof o?r[s]=`${s}=${(0,i.o)(o)}`:Array.isArray(o)&&(r[s]=o.slice(0).reduce(((e,t)=>e.concat([`${s}=${(0,i.o)(t)}`])),[]).sort().join("&"))}return t.sort().map((e=>r[e])).filter((e=>e)).join("&")})(e)}\n${n.map((e=>`${e}:${t[e]}`)).join("\n")}\n\n${n.join(";")}\n${r}`}async createStringToSign(e,t,r){const s=new this.sha256;s.update(a(r));const i=await s.digest();return`${w}\n${e}\n${t}\n${(0,n.n)(i)}`}getCanonicalPath({path:e}){if(this.uriEscapePath){const t=[];for(const r of e.split("/"))0!==r?.length&&"."!==r&&(".."===r?t.pop():t.push(r));const r=`${e?.startsWith("/")?"/":""}${t.join("/")}${t.length>0&&e?.endsWith("/")?"/":""}`;return(0,i.o)(r).replace(/%2F/g,"/")}return e}async getSignature(e,t,r,s){const i=await this.createStringToSign(e,t,s),o=new this.sha256(await r);return o.update(a(i)),(0,n.n)(await o.digest())}getSigningKey(e,t,r,s){return(async(e,t,r,s,i)=>{const o=await k(e,t.secretAccessKey,t.accessKeyId),a=`${r}:${s}:${i}:${(0,n.n)(o)}:${t.sessionToken}`;if(a in S)return S[a];for(A.push(a);A.length>50;)delete S[A.shift()];let c=`AWS4${t.secretAccessKey}`;for(const t of[r,s,i,x])c=await k(e,c,t);return S[a]=c})(this.sha256,e,r,t,s||this.service)}validateResolvedCredentials(e){if("object"!=typeof e||"string"!=typeof e.accessKeyId||"string"!=typeof e.secretAccessKey)throw new Error("Resolved credential object is not valid")}}const N=e=>{const t=(r=e,(e=>"number"==typeof e?new Date(1e3*e):"string"==typeof e?Number(e)?new Date(1e3*Number(e)):new Date(e):e)(r).toISOString().replace(/\.\d{3}Z$/,"Z")).replace(/[\-:]/g,"");var r;return{longDate:t,shortDate:t.slice(0,8)}},q=e=>Object.keys(e).sort().join(";")},7523:(e,t,r)=>{"use strict";var n,s,i,o,a;r.d(t,{dB:()=>o,Ue:()=>i,Vf:()=>c}),function(e){e.HEADER="header",e.QUERY="query"}(n||(n={})),function(e){e.HEADER="header",e.QUERY="query"}(s||(s={})),function(e){e.HTTP="http",e.HTTPS="https"}(i||(i={})),function(e){e.MD5="md5",e.CRC32="crc32",e.CRC32C="crc32c",e.SHA1="sha1",e.SHA256="sha256"}(o||(o={})),function(e){e[e.HEADER=0]="HEADER",e[e.TRAILER=1]="TRAILER"}(a||(a={}));const c="__smithy_context";var u,l;!function(e){e.PROFILE="profile",e.SSO_SESSION="sso-session",e.SERVICES="services"}(u||(u={})),function(e){e.HTTP_0_9="http/0.9",e.HTTP_1_0="http/1.0",e.TDS_8_0="tds/8.0"}(l||(l={}))},1302:(e,t,r)=>{"use strict";r.d(t,{E:()=>c,n:()=>l});const n={},s=new Array(64);for(let e=0,t="A".charCodeAt(0),r="Z".charCodeAt(0);e+t<=r;e++){const r=String.fromCharCode(e+t);n[r]=e,s[e]=r}for(let e=0,t="a".charCodeAt(0),r="z".charCodeAt(0);e+t<=r;e++){const r=String.fromCharCode(e+t),i=e+26;n[r]=i,s[i]=r}for(let e=0;e<10;e++){n[e.toString(10)]=e+52;const t=e.toString(10),r=e+52;n[t]=r,s[r]=t}n["+"]=62,s[62]="+",n["/"]=63,s[63]="/";const i=6,o=8,a=63,c=e=>{let t=e.length/4*3;"=="===e.slice(-2)?t-=2:"="===e.slice(-1)&&t--;const r=new ArrayBuffer(t),s=new DataView(r);for(let t=0;t<e.length;t+=4){let r=0,a=0;for(let s=t,o=t+3;s<=o;s++)if("="!==e[s]){if(!(e[s]in n))throw new TypeError(`Invalid character ${e[s]} in base64 string.`);r|=n[e[s]]<<(o-s)*i,a+=i}else r>>=i;const c=t/4*3;r>>=a%o;const u=Math.floor(a/o);for(let e=0;e<u;e++){const t=(u-e-1)*o;s.setUint8(c+e,(r&255<<t)>>t)}}return new Uint8Array(r)};var u=r(2637);function l(e){let t;t="string"==typeof e?(0,u.a)(e):e;const r="object"==typeof t&&"number"==typeof t.length,n="object"==typeof t&&"number"==typeof t.byteOffset&&"number"==typeof t.byteLength;if(!r&&!n)throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");let c="";for(let e=0;e<t.length;e+=3){let r=0,n=0;for(let s=e,i=Math.min(e+3,t.length);s<i;s++)r|=t[s]<<(i-s-1)*o,n+=o;const u=Math.ceil(n/i);r<<=u*i-n;for(let e=1;e<=u;e++){const t=(u-e)*i;c+=s[(r&a<<t)>>t]}c+="==".slice(0,4-u)}return c}},5623:(e,t,r)=>{"use strict";r.d(t,{a:()=>i,n:()=>o});const n={},s={};for(let e=0;e<256;e++){let t=e.toString(16).toLowerCase();1===t.length&&(t=`0${t}`),n[e]=t,s[t]=e}function i(e){if(e.length%2!=0)throw new Error("Hex encoded strings must have an even number length");const t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2){const n=e.slice(r,r+2).toLowerCase();if(!(n in s))throw new Error(`Cannot decode unrecognized sequence ${n} as hexadecimal`);t[r/2]=s[n]}return t}function o(e){let t="";for(let r=0;r<e.byteLength;r++)t+=n[e[r]];return t}},7135:(e,t,r)=>{"use strict";r.d(t,{u:()=>s,t:()=>i});var n=r(7523);const s=e=>e[n.Vf]||(e[n.Vf]={}),i=e=>{if("function"==typeof e)return e;const t=Promise.resolve(e);return()=>t}},2531:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});const n=e=>encodeURIComponent(e).replace(/[!'()*]/g,s),s=e=>`%${e.charCodeAt(0).toString(16).toUpperCase()}`},2637:(e,t,r)=>{"use strict";r.d(t,{a:()=>n});const n=e=>(new TextEncoder).encode(e)},6880:function(e){e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var s=t[n]={i:n,l:!1,exports:{}};return e[n].call(s.exports,s,s.exports,r),s.l=!0,s.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var s in e)r.d(n,s,function(t){return e[t]}.bind(null,s));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=90)}({17:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=r(18),s=function(){function e(){}return e.getFirstMatch=function(e,t){var r=t.match(e);return r&&r.length>0&&r[1]||""},e.getSecondMatch=function(e,t){var r=t.match(e);return r&&r.length>1&&r[2]||""},e.matchAndReturnConst=function(e,t,r){if(e.test(t))return r},e.getWindowsVersionName=function(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}},e.getMacOSVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}},e.getAndroidVersionName=function(e){var t=e.split(".").splice(0,2).map((function(e){return parseInt(e,10)||0}));if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0},e.getVersionPrecision=function(e){return e.split(".").length},e.compareVersions=function(t,r,n){void 0===n&&(n=!1);var s=e.getVersionPrecision(t),i=e.getVersionPrecision(r),o=Math.max(s,i),a=0,c=e.map([t,r],(function(t){var r=o-e.getVersionPrecision(t),n=t+new Array(r+1).join(".0");return e.map(n.split("."),(function(e){return new Array(20-e.length).join("0")+e})).reverse()}));for(n&&(a=o-Math.min(s,i)),o-=1;o>=a;){if(c[0][o]>c[1][o])return 1;if(c[0][o]===c[1][o]){if(o===a)return 0;o-=1}else if(c[0][o]<c[1][o])return-1}},e.map=function(e,t){var r,n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n},e.find=function(e,t){var r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){var s=e[r];if(t(s,r))return s}},e.assign=function(e){for(var t,r,n=e,s=arguments.length,i=new Array(s>1?s-1:0),o=1;o<s;o++)i[o-1]=arguments[o];if(Object.assign)return Object.assign.apply(Object,[e].concat(i));var a=function(){var e=i[t];"object"==typeof e&&null!==e&&Object.keys(e).forEach((function(t){n[t]=e[t]}))};for(t=0,r=i.length;t<r;t+=1)a();return e},e.getBrowserAlias=function(e){return n.BROWSER_ALIASES_MAP[e]},e.getBrowserTypeByAlias=function(e){return n.BROWSER_MAP[e]||""},e}();t.default=s,e.exports=t.default},18:function(e,t,r){"use strict";t.__esModule=!0,t.ENGINE_MAP=t.OS_MAP=t.PLATFORMS_MAP=t.BROWSER_MAP=t.BROWSER_ALIASES_MAP=void 0,t.BROWSER_ALIASES_MAP={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},t.BROWSER_MAP={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},t.PLATFORMS_MAP={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},t.OS_MAP={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},t.ENGINE_MAP={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"}},90:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(91))&&n.__esModule?n:{default:n},i=r(18);function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function(){function e(){}var t,r;return e.getParser=function(e,t){if(void 0===t&&(t=!1),"string"!=typeof e)throw new Error("UserAgent should be a string");return new s.default(e,t)},e.parse=function(e){return new s.default(e).getResult()},t=e,r=[{key:"BROWSER_MAP",get:function(){return i.BROWSER_MAP}},{key:"ENGINE_MAP",get:function(){return i.ENGINE_MAP}},{key:"OS_MAP",get:function(){return i.OS_MAP}},{key:"PLATFORMS_MAP",get:function(){return i.PLATFORMS_MAP}}],null&&o(t.prototype,null),r&&o(t,r),e}();t.default=a,e.exports=t.default},91:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n=c(r(92)),s=c(r(93)),i=c(r(94)),o=c(r(95)),a=c(r(17));function c(e){return e&&e.__esModule?e:{default:e}}var u=function(){function e(e,t){if(void 0===t&&(t=!1),null==e||""===e)throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}var t=e.prototype;return t.getUA=function(){return this._ua},t.test=function(e){return e.test(this._ua)},t.parseBrowser=function(){var e=this;this.parsedResult.browser={};var t=a.default.find(n.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.browser=t.describe(this.getUA())),this.parsedResult.browser},t.getBrowser=function(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()},t.getBrowserName=function(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""},t.getBrowserVersion=function(){return this.getBrowser().version},t.getOS=function(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()},t.parseOS=function(){var e=this;this.parsedResult.os={};var t=a.default.find(s.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.os=t.describe(this.getUA())),this.parsedResult.os},t.getOSName=function(e){var t=this.getOS().name;return e?String(t).toLowerCase()||"":t||""},t.getOSVersion=function(){return this.getOS().version},t.getPlatform=function(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()},t.getPlatformType=function(e){void 0===e&&(e=!1);var t=this.getPlatform().type;return e?String(t).toLowerCase()||"":t||""},t.parsePlatform=function(){var e=this;this.parsedResult.platform={};var t=a.default.find(i.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.platform=t.describe(this.getUA())),this.parsedResult.platform},t.getEngine=function(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()},t.getEngineName=function(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""},t.parseEngine=function(){var e=this;this.parsedResult.engine={};var t=a.default.find(o.default,(function(t){if("function"==typeof t.test)return t.test(e);if(t.test instanceof Array)return t.test.some((function(t){return e.test(t)}));throw new Error("Browser's test function is not valid")}));return t&&(this.parsedResult.engine=t.describe(this.getUA())),this.parsedResult.engine},t.parse=function(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this},t.getResult=function(){return a.default.assign({},this.parsedResult)},t.satisfies=function(e){var t=this,r={},n=0,s={},i=0;if(Object.keys(e).forEach((function(t){var o=e[t];"string"==typeof o?(s[t]=o,i+=1):"object"==typeof o&&(r[t]=o,n+=1)})),n>0){var o=Object.keys(r),c=a.default.find(o,(function(e){return t.isOS(e)}));if(c){var u=this.satisfies(r[c]);if(void 0!==u)return u}var l=a.default.find(o,(function(e){return t.isPlatform(e)}));if(l){var d=this.satisfies(r[l]);if(void 0!==d)return d}}if(i>0){var f=Object.keys(s),p=a.default.find(f,(function(e){return t.isBrowser(e,!0)}));if(void 0!==p)return this.compareVersion(s[p])}},t.isBrowser=function(e,t){void 0===t&&(t=!1);var r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),s=a.default.getBrowserTypeByAlias(n);return t&&s&&(n=s.toLowerCase()),n===r},t.compareVersion=function(e){var t=[0],r=e,n=!1,s=this.getBrowserVersion();if("string"==typeof s)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(a.default.compareVersions(s,r,n))>-1},t.isOS=function(e){return this.getOSName(!0)===String(e).toLowerCase()},t.isPlatform=function(e){return this.getPlatformType(!0)===String(e).toLowerCase()},t.isEngine=function(e){return this.getEngineName(!0)===String(e).toLowerCase()},t.is=function(e,t){return void 0===t&&(t=!1),this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)},t.some=function(e){var t=this;return void 0===e&&(e=[]),e.some((function(e){return t.is(e)}))},e}();t.default=u,e.exports=t.default},92:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=/version\/(\d+(\.?_?\d+)+)/i,o=[{test:[/googlebot/i],describe:function(e){var t={name:"Googlebot"},r=s.default.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/opera/i],describe:function(e){var t={name:"Opera"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe:function(e){var t={name:"Opera"},r=s.default.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe:function(e){var t={name:"Samsung Internet for Android"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe:function(e){var t={name:"NAVER Whale Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe:function(e){var t={name:"MZ Browser"},r=s.default.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/focus/i],describe:function(e){var t={name:"Focus"},r=s.default.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/swing/i],describe:function(e){var t={name:"Swing"},r=s.default.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/coast/i],describe:function(e){var t={name:"Opera Coast"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe:function(e){var t={name:"Opera Touch"},r=s.default.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe:function(e){var t={name:"Yandex Browser"},r=s.default.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe:function(e){var t={name:"UC Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe:function(e){var t={name:"Maxthon"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe:function(e){var t={name:"Epiphany"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe:function(e){var t={name:"Puffin"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe:function(e){var t={name:"Sleipnir"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe:function(e){var t={name:"K-Meleon"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe:function(e){var t={name:"WeChat"},r=s.default.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe:function(e){var t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=s.default.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe:function(e){var t={name:"Internet Explorer"},r=s.default.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe:function(e){var t={name:"Microsoft Edge"},r=s.default.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe:function(e){var t={name:"Microsoft Edge"},r=s.default.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe:function(e){var t={name:"Vivaldi"},r=s.default.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe:function(e){var t={name:"SeaMonkey"},r=s.default.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe:function(e){var t={name:"Sailfish"},r=s.default.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe:function(e){var t={name:"Amazon Silk"},r=s.default.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe:function(e){var t={name:"PhantomJS"},r=s.default.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe:function(e){var t={name:"SlimerJS"},r=s.default.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t={name:"BlackBerry"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t={name:"WebOS Browser"},r=s.default.getFirstMatch(i,e)||s.default.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe:function(e){var t={name:"Bada"},r=s.default.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe:function(e){var t={name:"Tizen"},r=s.default.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe:function(e){var t={name:"QupZilla"},r=s.default.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe:function(e){var t={name:"Firefox"},r=s.default.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe:function(e){var t={name:"Electron"},r=s.default.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe:function(e){var t={name:"Miui"},r=s.default.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe:function(e){var t={name:"Chromium"},r=s.default.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe:function(e){var t={name:"Chrome"},r=s.default.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe:function(e){var t={name:"Google Search"},r=s.default.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t={name:"Android Browser"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe:function(e){var t={name:"PlayStation 4"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe:function(e){var t={name:"Safari"},r=s.default.getFirstMatch(i,e);return r&&(t.version=r),t}},{test:[/.*/i],describe:function(e){var t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:s.default.getFirstMatch(t,e),version:s.default.getSecondMatch(t,e)}}}];t.default=o,e.exports=t.default},93:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:[/Roku\/DVP/],describe:function(e){var t=s.default.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:i.OS_MAP.Roku,version:t}}},{test:[/windows phone/i],describe:function(e){var t=s.default.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.WindowsPhone,version:t}}},{test:[/windows /i],describe:function(e){var t=s.default.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=s.default.getWindowsVersionName(t);return{name:i.OS_MAP.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(e){var t={name:i.OS_MAP.iOS},r=s.default.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe:function(e){var t=s.default.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=s.default.getMacOSVersionName(t),n={name:i.OS_MAP.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe:function(e){var t=s.default.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:i.OS_MAP.iOS,version:t}}},{test:function(e){var t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe:function(e){var t=s.default.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=s.default.getAndroidVersionName(t),n={name:i.OS_MAP.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe:function(e){var t=s.default.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:i.OS_MAP.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe:function(e){var t=s.default.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||s.default.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||s.default.getFirstMatch(/\bbb(\d+)/i,e);return{name:i.OS_MAP.BlackBerry,version:t}}},{test:[/bada/i],describe:function(e){var t=s.default.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.Bada,version:t}}},{test:[/tizen/i],describe:function(e){var t=s.default.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.Tizen,version:t}}},{test:[/linux/i],describe:function(){return{name:i.OS_MAP.Linux}}},{test:[/CrOS/],describe:function(){return{name:i.OS_MAP.ChromeOS}}},{test:[/PlayStation 4/],describe:function(e){var t=s.default.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:i.OS_MAP.PlayStation4,version:t}}}];t.default=o,e.exports=t.default},94:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:[/googlebot/i],describe:function(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe:function(e){var t=s.default.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:i.PLATFORMS_MAP.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe:function(){return{type:i.PLATFORMS_MAP.tablet}}},{test:function(e){var t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe:function(e){var t=s.default.getFirstMatch(/(ipod|iphone)/i,e);return{type:i.PLATFORMS_MAP.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"blackberry"===e.getBrowserName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"BlackBerry"}}},{test:function(e){return"bada"===e.getBrowserName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"windows phone"===e.getBrowserName()},describe:function(){return{type:i.PLATFORMS_MAP.mobile,vendor:"Microsoft"}}},{test:function(e){var t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:function(){return{type:i.PLATFORMS_MAP.tablet}}},{test:function(e){return"android"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.mobile}}},{test:function(e){return"macos"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop,vendor:"Apple"}}},{test:function(e){return"windows"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop}}},{test:function(e){return"linux"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.desktop}}},{test:function(e){return"playstation 4"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.tv}}},{test:function(e){return"roku"===e.getOSName(!0)},describe:function(){return{type:i.PLATFORMS_MAP.tv}}}];t.default=o,e.exports=t.default},95:function(e,t,r){"use strict";t.__esModule=!0,t.default=void 0;var n,s=(n=r(17))&&n.__esModule?n:{default:n},i=r(18),o=[{test:function(e){return"microsoft edge"===e.getBrowserName(!0)},describe:function(e){if(/\sedg\//i.test(e))return{name:i.ENGINE_MAP.Blink};var t=s.default.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:i.ENGINE_MAP.EdgeHTML,version:t}}},{test:[/trident/i],describe:function(e){var t={name:i.ENGINE_MAP.Trident},r=s.default.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){return e.test(/presto/i)},describe:function(e){var t={name:i.ENGINE_MAP.Presto},r=s.default.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:function(e){var t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe:function(e){var t={name:i.ENGINE_MAP.Gecko},r=s.default.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:function(){return{name:i.ENGINE_MAP.Blink}}},{test:[/(apple)?webkit/i],describe:function(e){var t={name:i.ENGINE_MAP.WebKit},r=s.default.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];t.default=o,e.exports=t.default}})},6749:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getApplicativeMonoid=function(e){var t=(0,n.getApplySemigroup)(e);return function(r){return{concat:t(r).concat,empty:e.of(r.empty)}}},t.getApplicativeComposition=function(e,t){var r=(0,i.getFunctorComposition)(e,t).map,o=(0,n.ap)(e,t);return{map:r,of:function(r){return e.of(t.of(r))},ap:function(e,t){return(0,s.pipe)(e,o(t))}}};var n=r(3871),s=r(3643),i=r(1124)},3871:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ap=function(e,t){return function(r){return function(n){return e.ap(e.map(n,(function(e){return function(r){return t.ap(e,r)}})),r)}}},t.apFirst=function(e){return function(t){return function(r){return e.ap(e.map(r,(function(e){return function(){return e}})),t)}}},t.apSecond=function(e){return function(t){return function(r){return e.ap(e.map(r,(function(){return function(e){return e}})),t)}}},t.apS=function(e){return function(t,r){return function(n){return e.ap(e.map(n,(function(e){return function(r){var n;return Object.assign({},e,((n={})[t]=r,n))}})),r)}}},t.getApplySemigroup=function(e){return function(t){return{concat:function(r,n){return e.ap(e.map(r,(function(e){return function(r){return t.concat(e,r)}})),n)}}}},t.sequenceT=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n=t.length,s=function(e){return a.has.call(u,e)||(u[e]=c(o.tuple,e-1,[])),u[e]}(n),i=e.map(t[0],s),l=1;l<n;l++)i=e.ap(i,t[l]);return i}},t.sequenceS=function(e){return function(t){for(var r=Object.keys(t),n=r.length,s=function(e){var t=e.length;switch(t){case 1:return function(t){var r;return(r={})[e[0]]=t,r};case 2:return function(t){return function(r){var n;return(n={})[e[0]]=t,n[e[1]]=r,n}};case 3:return function(t){return function(r){return function(n){var s;return(s={})[e[0]]=t,s[e[1]]=r,s[e[2]]=n,s}}};case 4:return function(t){return function(r){return function(n){return function(s){var i;return(i={})[e[0]]=t,i[e[1]]=r,i[e[2]]=n,i[e[3]]=s,i}}}};case 5:return function(t){return function(r){return function(n){return function(s){return function(i){var o;return(o={})[e[0]]=t,o[e[1]]=r,o[e[2]]=n,o[e[3]]=s,o[e[4]]=i,o}}}}};default:return c((function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];for(var s={},i=0;i<t;i++)s[e[i]]=r[i];return s}),t-1,[])}}(r),i=e.map(t[r[0]],s),o=1;o<n;o++)i=e.ap(i,t[r[o]]);return i}};var o=r(3643),a=i(r(174));function c(e,t,r){return function(n){for(var s=Array(r.length+1),i=0;i<r.length;i++)s[i]=r[i];return s[r.length]=n,0===t?e.apply(null,s):c(e,t-1,s)}}var u={1:function(e){return[e]},2:function(e){return function(t){return[e,t]}},3:function(e){return function(t){return function(r){return[e,t,r]}}},4:function(e){return function(t){return function(r){return function(n){return[e,t,r,n]}}}},5:function(e){return function(t){return function(r){return function(n){return function(s){return[e,t,r,n,s]}}}}}}},7910:(e,t)=>{"use strict";function r(e){return function(t,r){return e.chain(t,(function(t){return e.map(r(t),(function(){return t}))}))}}Object.defineProperty(t,"__esModule",{value:!0}),t.chainFirst=function(e){var t=r(e);return function(e){return function(r){return t(r,e)}}},t.tap=r,t.bind=function(e){return function(t,r){return function(n){return e.chain(n,(function(n){return e.map(r(n),(function(e){var r;return Object.assign({},n,((r={})[t]=e,r))}))}))}}}},7186:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.tailRec=void 0,t.tailRec=function(e,t){for(var r=t(e);"Left"===r._tag;)r=t(r.left);return r.right}},8934:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.match=t.foldW=t.matchW=t.isRight=t.isLeft=t.fromOption=t.fromPredicate=t.FromEither=t.MonadThrow=t.throwError=t.ChainRec=t.Extend=t.extend=t.Alt=t.alt=t.altW=t.Bifunctor=t.mapLeft=t.bimap=t.Traversable=t.sequence=t.traverse=t.Foldable=t.reduceRight=t.foldMap=t.reduce=t.Monad=t.Chain=t.Applicative=t.Apply=t.ap=t.apW=t.Pointed=t.of=t.asUnit=t.as=t.Functor=t.map=t.getAltValidation=t.getApplicativeValidation=t.getWitherable=t.getFilterable=t.getCompactable=t.getSemigroup=t.getEq=t.getShow=t.URI=t.flatMap=t.right=t.left=void 0,t.either=t.stringifyJSON=t.chainFirstW=t.chainFirst=t.chain=t.chainW=t.sequenceArray=t.traverseArray=t.traverseArrayWithIndex=t.traverseReadonlyArrayWithIndex=t.traverseReadonlyNonEmptyArrayWithIndex=t.ApT=t.apSW=t.apS=t.bindW=t.bind=t.let=t.bindTo=t.Do=t.exists=t.toUnion=t.chainNullableK=t.fromNullableK=t.tryCatchK=t.tryCatch=t.fromNullable=t.orElse=t.orElseW=t.swap=t.filterOrElseW=t.filterOrElse=t.flatMapOption=t.flatMapNullable=t.liftOption=t.liftNullable=t.chainOptionKW=t.chainOptionK=t.fromOptionK=t.duplicate=t.flatten=t.flattenW=t.tap=t.apSecondW=t.apSecond=t.apFirstW=t.apFirst=t.flap=t.getOrElse=t.getOrElseW=t.fold=void 0,t.getValidationMonoid=t.getValidationSemigroup=t.getApplyMonoid=t.getApplySemigroup=void 0,t.toError=function(e){try{return e instanceof Error?e:new Error(String(e))}catch(e){return new Error}},t.elem=function e(r){return function(n,s){if(void 0===s){var i=e(r);return function(e){return i(n,e)}}return!(0,t.isLeft)(s)&&r.equals(n,s.right)}},t.parseJSON=function(e,r){return(0,t.tryCatch)((function(){return JSON.parse(e)}),r)},t.getValidation=function(e){var r=(0,t.getApplicativeValidation)(e).ap,n=(0,t.getAltValidation)(e).alt;return{URI:t.URI,_E:void 0,map:g,of:t.of,chain:t.flatMap,bimap:x,mapLeft:S,reduce:v,foldMap:b,reduceRight:w,extend:E,traverse:_,sequence:t.sequence,chainRec:k,throwError:t.throwError,ap:r,alt:n}};var o=r(6749),a=r(3871),c=i(r(7910)),u=r(7186),l=r(6006),d=r(3643),f=r(1124),p=i(r(174)),h=r(4276),m=r(8084);t.left=p.left,t.right=p.right,t.flatMap=(0,d.dual)(2,(function(e,r){return(0,t.isLeft)(e)?e:r(e.right)}));var g=function(e,r){return(0,d.pipe)(e,(0,t.map)(r))},y=function(e,r){return(0,d.pipe)(e,(0,t.ap)(r))},v=function(e,r,n){return(0,d.pipe)(e,(0,t.reduce)(r,n))},b=function(e){return function(r,n){var s=(0,t.foldMap)(e);return(0,d.pipe)(r,s(n))}},w=function(e,r,n){return(0,d.pipe)(e,(0,t.reduceRight)(r,n))},_=function(e){var r=(0,t.traverse)(e);return function(e,t){return(0,d.pipe)(e,r(t))}},x=function(e,r,n){return(0,d.pipe)(e,(0,t.bimap)(r,n))},S=function(e,r){return(0,d.pipe)(e,(0,t.mapLeft)(r))},A=function(e,r){return(0,d.pipe)(e,(0,t.alt)(r))},E=function(e,r){return(0,d.pipe)(e,(0,t.extend)(r))},k=function(e,r){return(0,u.tailRec)(r(e),(function(e){return(0,t.isLeft)(e)?(0,t.right)((0,t.left)(e.left)):(0,t.isLeft)(e.right)?(0,t.left)(r(e.right.left)):(0,t.right)((0,t.right)(e.right.right))}))};t.URI="Either",t.getShow=function(e,r){return{show:function(n){return(0,t.isLeft)(n)?"left(".concat(e.show(n.left),")"):"right(".concat(r.show(n.right),")")}}},t.getEq=function(e,r){return{equals:function(n,s){return n===s||((0,t.isLeft)(n)?(0,t.isLeft)(s)&&e.equals(n.left,s.left):(0,t.isRight)(s)&&r.equals(n.right,s.right))}}},t.getSemigroup=function(e){return{concat:function(r,n){return(0,t.isLeft)(n)?r:(0,t.isLeft)(r)?n:(0,t.right)(e.concat(r.right,n.right))}}},t.getCompactable=function(e){var r=(0,t.left)(e.empty);return{URI:t.URI,_E:void 0,compact:function(e){return(0,t.isLeft)(e)?e:"None"===e.right._tag?r:(0,t.right)(e.right.value)},separate:function(e){return(0,t.isLeft)(e)?(0,h.separated)(e,e):(0,t.isLeft)(e.right)?(0,h.separated)((0,t.right)(e.right.left),r):(0,h.separated)(r,(0,t.right)(e.right.right))}}},t.getFilterable=function(e){var r=(0,t.left)(e.empty),n=(0,t.getCompactable)(e),s=n.compact,i=n.separate;return{URI:t.URI,_E:void 0,map:g,compact:s,separate:i,filter:function(e,n){return(0,t.isLeft)(e)||n(e.right)?e:r},filterMap:function(e,n){if((0,t.isLeft)(e))return e;var s=n(e.right);return"None"===s._tag?r:(0,t.right)(s.value)},partition:function(e,n){return(0,t.isLeft)(e)?(0,h.separated)(e,e):n(e.right)?(0,h.separated)(r,(0,t.right)(e.right)):(0,h.separated)((0,t.right)(e.right),r)},partitionMap:function(e,n){if((0,t.isLeft)(e))return(0,h.separated)(e,e);var s=n(e.right);return(0,t.isLeft)(s)?(0,h.separated)((0,t.right)(s.left),r):(0,h.separated)(r,(0,t.right)(s.right))}}},t.getWitherable=function(e){var r=(0,t.getFilterable)(e),n=(0,t.getCompactable)(e);return{URI:t.URI,_E:void 0,map:g,compact:r.compact,separate:r.separate,filter:r.filter,filterMap:r.filterMap,partition:r.partition,partitionMap:r.partitionMap,traverse:_,sequence:t.sequence,reduce:v,foldMap:b,reduceRight:w,wither:(0,m.witherDefault)(t.Traversable,n),wilt:(0,m.wiltDefault)(t.Traversable,n)}},t.getApplicativeValidation=function(e){return{URI:t.URI,_E:void 0,map:g,ap:function(r,n){return(0,t.isLeft)(r)?(0,t.isLeft)(n)?(0,t.left)(e.concat(r.left,n.left)):r:(0,t.isLeft)(n)?n:(0,t.right)(r.right(n.right))},of:t.of}},t.getAltValidation=function(e){return{URI:t.URI,_E:void 0,map:g,alt:function(r,n){if((0,t.isRight)(r))return r;var s=n();return(0,t.isLeft)(s)?(0,t.left)(e.concat(r.left,s.left)):s}}},t.map=function(e){return function(r){return(0,t.isLeft)(r)?r:(0,t.right)(e(r.right))}},t.Functor={URI:t.URI,map:g},t.as=(0,d.dual)(2,(0,f.as)(t.Functor)),t.asUnit=(0,f.asUnit)(t.Functor),t.of=t.right,t.Pointed={URI:t.URI,of:t.of},t.apW=function(e){return function(r){return(0,t.isLeft)(r)?r:(0,t.isLeft)(e)?e:(0,t.right)(r.right(e.right))}},t.ap=t.apW,t.Apply={URI:t.URI,map:g,ap:y},t.Applicative={URI:t.URI,map:g,ap:y,of:t.of},t.Chain={URI:t.URI,map:g,ap:y,chain:t.flatMap},t.Monad={URI:t.URI,map:g,ap:y,of:t.of,chain:t.flatMap},t.reduce=function(e,r){return function(n){return(0,t.isLeft)(n)?e:r(e,n.right)}},t.foldMap=function(e){return function(r){return function(n){return(0,t.isLeft)(n)?e.empty:r(n.right)}}},t.reduceRight=function(e,r){return function(n){return(0,t.isLeft)(n)?e:r(n.right,e)}},t.Foldable={URI:t.URI,reduce:v,foldMap:b,reduceRight:w},t.traverse=function(e){return function(r){return function(n){return(0,t.isLeft)(n)?e.of((0,t.left)(n.left)):e.map(r(n.right),t.right)}}},t.sequence=function(e){return function(r){return(0,t.isLeft)(r)?e.of((0,t.left)(r.left)):e.map(r.right,t.right)}},t.Traversable={URI:t.URI,map:g,reduce:v,foldMap:b,reduceRight:w,traverse:_,sequence:t.sequence},t.bimap=function(e,r){return function(n){return(0,t.isLeft)(n)?(0,t.left)(e(n.left)):(0,t.right)(r(n.right))}},t.mapLeft=function(e){return function(r){return(0,t.isLeft)(r)?(0,t.left)(e(r.left)):r}},t.Bifunctor={URI:t.URI,bimap:x,mapLeft:S},t.altW=function(e){return function(r){return(0,t.isLeft)(r)?e():r}},t.alt=t.altW,t.Alt={URI:t.URI,map:g,alt:A},t.extend=function(e){return function(r){return(0,t.isLeft)(r)?r:(0,t.right)(e(r))}},t.Extend={URI:t.URI,map:g,extend:E},t.ChainRec={URI:t.URI,map:g,ap:y,chain:t.flatMap,chainRec:k},t.throwError=t.left,t.MonadThrow={URI:t.URI,map:g,ap:y,of:t.of,chain:t.flatMap,throwError:t.throwError},t.FromEither={URI:t.URI,fromEither:d.identity},t.fromPredicate=(0,l.fromPredicate)(t.FromEither),t.fromOption=(0,l.fromOption)(t.FromEither),t.isLeft=p.isLeft,t.isRight=p.isRight,t.matchW=function(e,r){return function(n){return(0,t.isLeft)(n)?e(n.left):r(n.right)}},t.foldW=t.matchW,t.match=t.matchW,t.fold=t.match,t.getOrElseW=function(e){return function(r){return(0,t.isLeft)(r)?e(r.left):r.right}},t.getOrElse=t.getOrElseW,t.flap=(0,f.flap)(t.Functor),t.apFirst=(0,a.apFirst)(t.Apply),t.apFirstW=t.apFirst,t.apSecond=(0,a.apSecond)(t.Apply),t.apSecondW=t.apSecond,t.tap=(0,d.dual)(2,c.tap(t.Chain)),t.flattenW=(0,t.flatMap)(d.identity),t.flatten=t.flattenW,t.duplicate=(0,t.extend)(d.identity),t.fromOptionK=(0,l.fromOptionK)(t.FromEither),t.chainOptionK=(0,l.chainOptionK)(t.FromEither,t.Chain),t.chainOptionKW=t.chainOptionK;var R={fromEither:t.FromEither.fromEither};t.liftNullable=p.liftNullable(R),t.liftOption=p.liftOption(R);var I={flatMap:t.flatMap};t.flatMapNullable=p.flatMapNullable(R,I),t.flatMapOption=p.flatMapOption(R,I),t.filterOrElse=(0,l.filterOrElse)(t.FromEither,t.Chain),t.filterOrElseW=t.filterOrElse,t.swap=function(e){return(0,t.isLeft)(e)?(0,t.right)(e.left):(0,t.left)(e.right)},t.orElseW=function(e){return function(r){return(0,t.isLeft)(r)?e(r.left):r}},t.orElse=t.orElseW,t.fromNullable=function(e){return function(r){return null==r?(0,t.left)(e):(0,t.right)(r)}},t.tryCatch=function(e,r){try{return(0,t.right)(e())}catch(e){return(0,t.left)(r(e))}},t.tryCatchK=function(e,r){return function(){for(var n=[],s=0;s<arguments.length;s++)n[s]=arguments[s];return(0,t.tryCatch)((function(){return e.apply(void 0,n)}),r)}},t.fromNullableK=function(e){var r=(0,t.fromNullable)(e);return function(e){return(0,d.flow)(e,r)}},t.chainNullableK=function(e){var r=(0,t.fromNullableK)(e);return function(e){return(0,t.flatMap)(r(e))}},t.toUnion=(0,t.foldW)(d.identity,d.identity),t.exists=function(e){return function(r){return!(0,t.isLeft)(r)&&e(r.right)}},t.Do=(0,t.of)(p.emptyRecord),t.bindTo=(0,f.bindTo)(t.Functor);var O=(0,f.let)(t.Functor);t.let=O,t.bind=c.bind(t.Chain),t.bindW=t.bind,t.apS=(0,a.apS)(t.Apply),t.apSW=t.apS,t.ApT=(0,t.of)(p.emptyReadonlyArray),t.traverseReadonlyNonEmptyArrayWithIndex=function(e){return function(r){var n=e(0,p.head(r));if((0,t.isLeft)(n))return n;for(var s=[n.right],i=1;i<r.length;i++){var o=e(i,r[i]);if((0,t.isLeft)(o))return o;s.push(o.right)}return(0,t.right)(s)}},t.traverseReadonlyArrayWithIndex=function(e){var r=(0,t.traverseReadonlyNonEmptyArrayWithIndex)(e);return function(e){return p.isNonEmpty(e)?r(e):t.ApT}},t.traverseArrayWithIndex=t.traverseReadonlyArrayWithIndex,t.traverseArray=function(e){return(0,t.traverseReadonlyArrayWithIndex)((function(t,r){return e(r)}))},t.sequenceArray=(0,t.traverseArray)(d.identity),t.chainW=t.flatMap,t.chain=t.flatMap,t.chainFirst=t.tap,t.chainFirstW=t.tap,t.stringifyJSON=function(e,r){return(0,t.tryCatch)((function(){var t=JSON.stringify(e);if("string"!=typeof t)throw new Error("Converting unsupported structure to JSON");return t}),r)},t.either={URI:t.URI,map:g,of:t.of,ap:y,chain:t.flatMap,reduce:v,foldMap:b,reduceRight:w,traverse:_,sequence:t.sequence,bimap:x,mapLeft:S,alt:A,extend:E,chainRec:k,throwError:t.throwError},t.getApplySemigroup=(0,a.getApplySemigroup)(t.Apply),t.getApplyMonoid=(0,o.getApplicativeMonoid)(t.Applicative),t.getValidationSemigroup=function(e,r){return(0,a.getApplySemigroup)((0,t.getApplicativeValidation)(e))(r)},t.getValidationMonoid=function(e,r){return(0,o.getApplicativeMonoid)((0,t.getApplicativeValidation)(e))(r)}},5921:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.eqDate=t.eqNumber=t.eqString=t.eqBoolean=t.eq=t.strictEqual=t.getStructEq=t.getTupleEq=t.Contravariant=t.getMonoid=t.getSemigroup=t.eqStrict=t.URI=t.contramap=t.tuple=t.struct=t.fromEquals=void 0;var n=r(3643);t.fromEquals=function(e){return{equals:function(t,r){return t===r||e(t,r)}}},t.struct=function(e){return(0,t.fromEquals)((function(t,r){for(var n in e)if(!e[n].equals(t[n],r[n]))return!1;return!0}))},t.tuple=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(0,t.fromEquals)((function(t,r){return e.every((function(e,n){return e.equals(t[n],r[n])}))}))},t.contramap=function(e){return function(r){return(0,t.fromEquals)((function(t,n){return r.equals(e(t),e(n))}))}},t.URI="Eq",t.eqStrict={equals:function(e,t){return e===t}};var s={equals:function(){return!0}};t.getSemigroup=function(){return{concat:function(e,r){return(0,t.fromEquals)((function(t,n){return e.equals(t,n)&&r.equals(t,n)}))}}},t.getMonoid=function(){return{concat:(0,t.getSemigroup)().concat,empty:s}},t.Contravariant={URI:t.URI,contramap:function(e,r){return(0,n.pipe)(e,(0,t.contramap)(r))}},t.getTupleEq=t.tuple,t.getStructEq=t.struct,t.strictEqual=t.eqStrict.equals,t.eq=t.Contravariant,t.eqBoolean=t.eqStrict,t.eqString=t.eqStrict,t.eqNumber=t.eqStrict,t.eqDate={equals:function(e,t){return e.valueOf()===t.valueOf()}}},6006:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.fromOption=u,t.fromPredicate=function(e){return function(t,r){return function(n){return e.fromEither(t(n)?c.right(n):c.left(r(n)))}}},t.fromOptionK=l,t.chainOptionK=function(e,t){var r=l(e);return function(e){var n=r(e);return function(e){return function(r){return t.chain(r,n(e))}}}},t.fromEitherK=d,t.chainEitherK=function(e,t){var r=d(e);return function(e){return function(n){return t.chain(n,r(e))}}},t.chainFirstEitherK=function(e,t){var r=f(e,t);return function(e){return function(t){return r(t,e)}}},t.filterOrElse=function(e,t){return function(r,n){return function(s){return t.chain(s,(function(t){return e.fromEither(r(t)?c.right(t):c.left(n(t)))}))}}},t.tapEither=f;var o=r(7910),a=r(3643),c=i(r(174));function u(e){return function(t){return function(r){return e.fromEither(c.isNone(r)?c.left(t()):c.right(r.value))}}}function l(e){var t=u(e);return function(e){var r=t(e);return function(e){return(0,a.flow)(e,r)}}}function d(e){return function(t){return(0,a.flow)(t,e.fromEither)}}function f(e,t){var r=d(e),n=(0,o.tap)(t);return function(e,t){return n(e,r(t))}}},1124:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.map=s,t.flap=function(e){return function(t){return function(r){return e.map(r,(function(e){return e(t)}))}}},t.bindTo=function(e){return function(t){return function(r){return e.map(r,(function(e){var r;return(r={})[t]=e,r}))}}},t.let=function(e){return function(t,r){return function(n){return e.map(n,(function(e){var n;return Object.assign({},e,((n={})[t]=r(e),n))}))}}},t.getFunctorComposition=function(e,t){var r=s(e,t);return{map:function(e,t){return(0,n.pipe)(e,r(t))}}},t.as=i,t.asUnit=function(e){var t=i(e);return function(e){return t(e,void 0)}};var n=r(3643);function s(e,t){return function(r){return function(n){return e.map(n,(function(e){return t.map(e,r)}))}}}function i(e){return function(t,r){return e.map(t,(function(){return r}))}}},9918:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.and=t.or=t.not=t.Contravariant=t.getMonoidAll=t.getSemigroupAll=t.getMonoidAny=t.getSemigroupAny=t.URI=t.contramap=void 0;var n=r(3643);t.contramap=function(e){return function(t){return(0,n.flow)(e,t)}},t.URI="Predicate",t.getSemigroupAny=function(){return{concat:function(e,r){return(0,n.pipe)(e,(0,t.or)(r))}}},t.getMonoidAny=function(){return{concat:(0,t.getSemigroupAny)().concat,empty:n.constFalse}},t.getSemigroupAll=function(){return{concat:function(e,r){return(0,n.pipe)(e,(0,t.and)(r))}}},t.getMonoidAll=function(){return{concat:(0,t.getSemigroupAll)().concat,empty:n.constTrue}},t.Contravariant={URI:t.URI,contramap:function(e,r){return(0,n.pipe)(e,(0,t.contramap)(r))}},t.not=function(e){return function(t){return!e(t)}},t.or=function(e){return function(t){return function(r){return t(r)||e(r)}}},t.and=function(e){return function(t){return function(r){return t(r)&&e(r)}}}},3605:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromArray=t.getDifferenceMagma=t.getIntersectionSemigroup=t.getUnionMonoid=t.getUnionSemigroup=t.URI=t.toReadonlyArray=t.some=t.size=t.isEmpty=t.empty=t.compact=t.toggle=t.remove=t.reduceRight=t.fromReadonlyArray=t.singleton=t.fromSet=void 0,t.toSet=function(e){return new Set(e)},t.map=function(e){var t=m(e);return function(e){return function(r){var n=new Set;return r.forEach((function(r){var s=e(r);t(s,n)||n.add(s)})),n}}},t.chain=function(e){var t=m(e);return function(e){return function(r){var n=new Set;return r.forEach((function(r){e(r).forEach((function(e){t(e,n)||n.add(e)}))})),n}}},t.filter=a,t.partition=function(e){return function(t){for(var r,n=t.values(),s=new Set,i=new Set;!(r=n.next()).done;){var a=r.value;e(a)?s.add(a):i.add(a)}return(0,o.separated)(i,s)}},t.union=c,t.intersection=u,t.partitionMap=function(e,t){return function(r){return function(n){for(var s,i=n.values(),a=new Set,c=new Set,u=m(e),l=m(t);!(s=i.next()).done;){var d=r(s.value);switch(d._tag){case"Left":u(d.left,a)||a.add(d.left);break;case"Right":l(d.right,c)||c.add(d.right)}}return(0,o.separated)(a,c)}}},t.difference=l,t.reduce=function(e){var r=(0,t.toReadonlyArray)(e);return function(e,t){return function(n){return r(n).reduce(t,e)}}},t.foldMap=function(e,r){var n=(0,t.toReadonlyArray)(e);return function(e){return function(t){return n(t).reduce((function(t,n){return r.concat(t,e(n))}),r.empty)}}},t.insert=d,t.separate=function(e,t){return function(r){var n=m(e),s=m(t),i=new Set,a=new Set;return r.forEach((function(e){switch(e._tag){case"Left":n(e.left,i)||i.add(e.left);break;case"Right":s(e.right,a)||a.add(e.right)}})),(0,o.separated)(i,a)}},t.filterMap=f,t.every=p,t.isSubset=h,t.elem=m,t.getShow=function(e){return{show:function(t){var r=[];return t.forEach((function(t){r.push(e.show(t))})),"new Set([".concat(r.sort().join(", "),"])")}}},t.getEq=function(e){var t=h(e);return(0,n.fromEquals)((function(e,r){return t(e,r)&&t(r,e)}))};var n=r(5921),s=r(3643),i=r(9918),o=r(4276);function a(e){return function(t){for(var r,n=t.values(),s=new Set;!(r=n.next()).done;){var i=r.value;e(i)&&s.add(i)}return s}}function c(e){var r=m(e);return function(n,s){if(void 0===s){var i=c(e);return function(e){return i(n,e)}}if((0,t.isEmpty)(n))return s;if((0,t.isEmpty)(s))return n;var o=new Set(n);return s.forEach((function(e){r(e,o)||o.add(e)})),o}}function u(e){var r=m(e);return function(n,s){if(void 0===s){var i=u(e);return function(e){return i(e,n)}}if((0,t.isEmpty)(n)||(0,t.isEmpty)(s))return t.empty;var o=new Set;return n.forEach((function(e){r(e,s)&&o.add(e)})),o}}function l(e){var t=m(e);return function(r,n){if(void 0===n){var s=l(e);return function(e){return s(e,r)}}return a((function(e){return!t(e,n)}))(r)}}function d(e){var t=m(e);return function(e){return function(r){if(t(e)(r))return r;var n=new Set(r);return n.add(e),n}}}function f(e){var t=m(e);return function(e){return function(r){var n=new Set;return r.forEach((function(r){var s=e(r);"Some"!==s._tag||t(s.value,n)||n.add(s.value)})),n}}}function p(e){return(0,i.not)((0,t.some)((0,i.not)(e)))}function h(e){var t=m(e);return function(r,n){if(void 0===n){var s=h(e);return function(e){return s(e,r)}}return p((function(e){return t(e,n)}))(r)}}function m(e){return function(t,r){if(void 0===r){var n=m(e);return function(e){return n(t,e)}}for(var s,i=r.values(),o=!1;!o&&!(s=i.next()).done;)o=e.equals(t,s.value);return o}}t.fromSet=function(e){return new Set(e)},t.singleton=function(e){return new Set([e])},t.fromReadonlyArray=function(e){return function(t){for(var r=t.length,n=new Set,s=m(e),i=0;i<r;i++){var o=t[i];s(o,n)||n.add(o)}return n}},t.reduceRight=function(e){var r=(0,t.toReadonlyArray)(e);return function(e,t){return function(n){return r(n).reduceRight((function(e,r){return t(r,e)}),e)}}},t.remove=function(e){return function(t){return function(r){return a((function(r){return!e.equals(t,r)}))(r)}}},t.toggle=function(e){var r=m(e),n=(0,t.remove)(e),s=d(e);return function(e){return function(t){return(r(e,t)?n:s)(e)(t)}}},t.compact=function(e){return f(e)(s.identity)},t.empty=new Set,t.isEmpty=function(e){return 0===e.size},t.size=function(e){return e.size},t.some=function(e){return function(t){for(var r,n=t.values(),s=!1;!s&&!(r=n.next()).done;)s=e(r.value);return s}},t.toReadonlyArray=function(e){return function(t){var r=[];return t.forEach((function(e){return r.push(e)})),r.sort(e.compare)}},t.URI="ReadonlySet",t.getUnionSemigroup=function(e){return{concat:c(e)}},t.getUnionMonoid=function(e){return{concat:(0,t.getUnionSemigroup)(e).concat,empty:t.empty}},t.getIntersectionSemigroup=function(e){return{concat:u(e)}},t.getDifferenceMagma=function(e){return{concat:l(e)}},t.fromArray=t.fromReadonlyArray},4276:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.right=t.left=t.flap=t.Functor=t.Bifunctor=t.URI=t.bimap=t.mapLeft=t.map=t.separated=void 0;var n=r(3643),s=r(1124);t.separated=function(e,t){return{left:e,right:t}},t.map=function(e){return function(r){return(0,t.separated)((0,t.left)(r),e((0,t.right)(r)))}},t.mapLeft=function(e){return function(r){return(0,t.separated)(e((0,t.left)(r)),(0,t.right)(r))}},t.bimap=function(e,r){return function(n){return(0,t.separated)(e((0,t.left)(n)),r((0,t.right)(n)))}},t.URI="Separated",t.Bifunctor={URI:t.URI,mapLeft:function(e,r){return(0,n.pipe)(e,(0,t.mapLeft)(r))},bimap:function(e,r,s){return(0,n.pipe)(e,(0,t.bimap)(r,s))}},t.Functor={URI:t.URI,map:function(e,r){return(0,n.pipe)(e,(0,t.map)(r))}},t.flap=(0,s.flap)(t.Functor),t.left=function(e){return e.left},t.right=function(e){return e.right}},5363:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.subset=t.toArray=t.elem=t.isSubset=t.every=t.some=t.size=t.isEmpty=t.empty=t.compact=t.fromArray=t.toggle=t.remove=t.singleton=t.reduceRight=t.foldMap=t.reduce=t.getDifferenceMagma=t.getIntersectionSemigroup=t.getUnionMonoid=t.getUnionSemigroup=t.getEq=t.getShow=void 0,t.map=function(e){var r=(0,t.elem)(e);return function(e){return function(t){var n=new Set;return t.forEach((function(t){var s=e(t);r(s,n)||n.add(s)})),n}}},t.chain=function(e){var r=(0,t.elem)(e);return function(e){return function(t){var n=new Set;return t.forEach((function(t){e(t).forEach((function(e){r(e,n)||n.add(e)}))})),n}}},t.filter=u,t.partition=function(e){return function(t){for(var r,n=t.values(),s=new Set,i=new Set;!(r=n.next()).done;){var o=r.value;e(o)?s.add(o):i.add(o)}return(0,c.separated)(i,s)}},t.union=l,t.intersection=d,t.partitionMap=function(e,r){return function(n){return function(s){for(var i,o=s.values(),a=new Set,u=new Set,l=(0,t.elem)(e),d=(0,t.elem)(r);!(i=o.next()).done;){var f=n(i.value);switch(f._tag){case"Left":l(f.left,a)||a.add(f.left);break;case"Right":d(f.right,u)||u.add(f.right)}}return(0,c.separated)(a,u)}}},t.difference=f,t.insert=p,t.separate=function(e,r){return function(n){var s=(0,t.elem)(e),i=(0,t.elem)(r),o=new Set,a=new Set;return n.forEach((function(e){switch(e._tag){case"Left":s(e.left,o)||o.add(e.left);break;case"Right":i(e.right,a)||a.add(e.right)}})),(0,c.separated)(o,a)}},t.filterMap=h;var o=r(3643),a=i(r(3605)),c=r(4276);function u(e){return function(t){for(var r,n=t.values(),s=new Set;!(r=n.next()).done;){var i=r.value;e(i)&&s.add(i)}return s}}function l(e){var r=(0,t.elem)(e);return function(n,s){if(void 0===s){var i=l(e);return function(e){return i(n,e)}}if((0,t.isEmpty)(n))return s;if((0,t.isEmpty)(s))return n;var o=new Set(n);return s.forEach((function(e){r(e,o)||o.add(e)})),o}}function d(e){var r=(0,t.elem)(e);return function(n,s){if(void 0===s){var i=d(e);return function(e){return i(e,n)}}if((0,t.isEmpty)(n)||(0,t.isEmpty)(s))return new Set;var o=new Set;return n.forEach((function(e){r(e,s)&&o.add(e)})),o}}function f(e){var r=(0,t.elem)(e);return function(t,n){if(void 0===n){var s=f(e);return function(e){return s(e,t)}}return u((function(e){return!r(e,n)}))(t)}}function p(e){var r=(0,t.elem)(e);return function(e){return function(t){if(r(e)(t))return t;var n=new Set(t);return n.add(e),n}}}function h(e){var r=(0,t.elem)(e);return function(e){return function(t){var n=new Set;return t.forEach((function(t){var s=e(t);"Some"!==s._tag||r(s.value,n)||n.add(s.value)})),n}}}t.getShow=a.getShow,t.getEq=a.getEq,t.getUnionSemigroup=function(e){return{concat:l(e)}},t.getUnionMonoid=function(e){return{concat:(0,t.getUnionSemigroup)(e).concat,empty:new Set}},t.getIntersectionSemigroup=function(e){return{concat:d(e)}},t.getDifferenceMagma=function(e){return{concat:f(e)}},t.reduce=a.reduce,t.foldMap=a.foldMap,t.reduceRight=a.reduceRight,t.singleton=function(e){return new Set([e])},t.remove=function(e){return function(t){return function(r){return u((function(r){return!e.equals(t,r)}))(r)}}},t.toggle=function(e){var r=(0,t.elem)(e),n=(0,t.remove)(e),s=p(e);return function(e){return function(t){return(r(e,t)?n:s)(e)(t)}}},t.fromArray=function(e){return function(r){for(var n=r.length,s=new Set,i=(0,t.elem)(e),o=0;o<n;o++){var a=r[o];i(a,s)||s.add(a)}return s}},t.compact=function(e){return h(e)(o.identity)},t.empty=new Set,t.isEmpty=function(e){return 0===e.size},t.size=function(e){return e.size},t.some=a.some,t.every=a.every,t.isSubset=a.isSubset,t.elem=a.elem,t.toArray=function(e){return function(t){var r=[];return t.forEach((function(e){return r.push(e)})),r.sort(e.compare)}},t.subset=a.isSubset},8084:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.wiltDefault=function(e,t){return function(r){var n=e.traverse(r);return function(e,s){return r.map(n(e,s),t.separate)}}},t.witherDefault=function(e,t){return function(r){var n=e.traverse(r);return function(e,s){return r.map(n(e,s),t.compact)}}},t.filterE=function(e){return function(t){var r=e.wither(t);return function(e){return function(n){return r(n,(function(r){return t.map(e(r),(function(e){return e?o.some(r):o.none}))}))}}}};var o=i(r(174))},3643:function(e,t){"use strict";var r=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,s=0,i=t.length;s<i;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))};function n(e){return e}function s(e){return function(){return e}}function i(e,t,r,n,s,i,o,a,c){switch(arguments.length){case 1:return e;case 2:return function(){return t(e.apply(this,arguments))};case 3:return function(){return r(t(e.apply(this,arguments)))};case 4:return function(){return n(r(t(e.apply(this,arguments))))};case 5:return function(){return s(n(r(t(e.apply(this,arguments)))))};case 6:return function(){return i(s(n(r(t(e.apply(this,arguments))))))};case 7:return function(){return o(i(s(n(r(t(e.apply(this,arguments)))))))};case 8:return function(){return a(o(i(s(n(r(t(e.apply(this,arguments))))))))};case 9:return function(){return c(a(o(i(s(n(r(t(e.apply(this,arguments)))))))))}}}function o(e){throw new Error("Called `absurd` function which should be uncallable")}Object.defineProperty(t,"__esModule",{value:!0}),t.dual=t.getEndomorphismMonoid=t.SK=t.hole=t.constVoid=t.constUndefined=t.constNull=t.constFalse=t.constTrue=t.unsafeCoerce=t.apply=t.getRing=t.getSemiring=t.getMonoid=t.getSemigroup=t.getBooleanAlgebra=void 0,t.identity=n,t.constant=s,t.flip=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return t.length>1?e(t[1],t[0]):function(r){return e(r)(t[0])}}},t.flow=i,t.tuple=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e},t.increment=function(e){return e+1},t.decrement=function(e){return e-1},t.absurd=o,t.tupled=function(e){return function(t){return e.apply(void 0,t)}},t.untupled=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return e(t)}},t.pipe=function(e,t,r,n,s,i,o,a,c){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return r(t(e));case 4:return n(r(t(e)));case 5:return s(n(r(t(e))));case 6:return i(s(n(r(t(e)))));case 7:return o(i(s(n(r(t(e))))));case 8:return a(o(i(s(n(r(t(e)))))));case 9:return c(a(o(i(s(n(r(t(e))))))));default:for(var u=arguments[0],l=1;l<arguments.length;l++)u=arguments[l](u);return u}},t.not=function(e){return function(t){return!e(t)}},t.getBooleanAlgebra=function(e){return function(){return{meet:function(t,r){return function(n){return e.meet(t(n),r(n))}},join:function(t,r){return function(n){return e.join(t(n),r(n))}},zero:function(){return e.zero},one:function(){return e.one},implies:function(t,r){return function(n){return e.implies(t(n),r(n))}},not:function(t){return function(r){return e.not(t(r))}}}}},t.getSemigroup=function(e){return function(){return{concat:function(t,r){return function(n){return e.concat(t(n),r(n))}}}}},t.getMonoid=function(e){var r=(0,t.getSemigroup)(e);return function(){return{concat:r().concat,empty:function(){return e.empty}}}},t.getSemiring=function(e){return{add:function(t,r){return function(n){return e.add(t(n),r(n))}},zero:function(){return e.zero},mul:function(t,r){return function(n){return e.mul(t(n),r(n))}},one:function(){return e.one}}},t.getRing=function(e){var r=(0,t.getSemiring)(e);return{add:r.add,mul:r.mul,one:r.one,zero:r.zero,sub:function(t,r){return function(n){return e.sub(t(n),r(n))}}}},t.apply=function(e){return function(t){return t(e)}},t.unsafeCoerce=n,t.constTrue=s(!0),t.constFalse=s(!1),t.constNull=s(null),t.constUndefined=s(void 0),t.constVoid=t.constUndefined,t.hole=o,t.SK=function(e,t){return t},t.getEndomorphismMonoid=function(){return{concat:function(e,t){return i(e,t)},empty:n}},t.dual=function(e,t){var n="number"==typeof e?function(t){return t.length>=e}:e;return function(){var e=Array.from(arguments);return n(arguments)?t.apply(this,e):function(n){return t.apply(void 0,r([n],e,!1))}}}},174:function(e,t,r){"use strict";var n=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,s=0,i=t.length;s<i;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.flatMapReader=t.flatMapTask=t.flatMapIO=t.flatMapEither=t.flatMapOption=t.flatMapNullable=t.liftOption=t.liftNullable=t.fromReadonlyNonEmptyArray=t.has=t.emptyRecord=t.emptyReadonlyArray=t.tail=t.head=t.isNonEmpty=t.singleton=t.right=t.left=t.isRight=t.isLeft=t.some=t.none=t.isSome=t.isNone=void 0;var s=r(3643);t.isNone=function(e){return"None"===e._tag},t.isSome=function(e){return"Some"===e._tag},t.none={_tag:"None"},t.some=function(e){return{_tag:"Some",value:e}},t.isLeft=function(e){return"Left"===e._tag},t.isRight=function(e){return"Right"===e._tag},t.left=function(e){return{_tag:"Left",left:e}},t.right=function(e){return{_tag:"Right",right:e}},t.singleton=function(e){return[e]},t.isNonEmpty=function(e){return e.length>0},t.head=function(e){return e[0]},t.tail=function(e){return e.slice(1)},t.emptyReadonlyArray=[],t.emptyRecord={},t.has=Object.prototype.hasOwnProperty,t.fromReadonlyNonEmptyArray=function(e){return n([e[0]],e.slice(1),!0)},t.liftNullable=function(e){return function(r,n){return function(){for(var s=[],i=0;i<arguments.length;i++)s[i]=arguments[i];var o=r.apply(void 0,s);return e.fromEither(null==o?(0,t.left)(n.apply(void 0,s)):(0,t.right)(o))}}},t.liftOption=function(e){return function(r,n){return function(){for(var s=[],i=0;i<arguments.length;i++)s[i]=arguments[i];var o=r.apply(void 0,s);return e.fromEither((0,t.isNone)(o)?(0,t.left)(n.apply(void 0,s)):(0,t.right)(o.value))}}},t.flatMapNullable=function(e,r){return(0,s.dual)(3,(function(n,s,i){return r.flatMap(n,(0,t.liftNullable)(e)(s,i))}))},t.flatMapOption=function(e,r){return(0,s.dual)(3,(function(n,s,i){return r.flatMap(n,(0,t.liftOption)(e)(s,i))}))},t.flatMapEither=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromEither(n(t))}))}))},t.flatMapIO=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromIO(n(t))}))}))},t.flatMapTask=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromTask(n(t))}))}))},t.flatMapReader=function(e,t){return(0,s.dual)(2,(function(r,n){return t.flatMap(r,(function(t){return e.fromReader(n(t))}))}))}},6075:e=>{var t;self,t=()=>{return e={7629:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(9474),o=r(1687),a=r(8652),c=r(8160),u=r(3292),l=r(6354),d=r(8901),f=r(9708),p=r(6914),h=r(2294),m=r(6133),g=r(1152),y=r(8863),v=r(2036),b={Base:class{constructor(e){this.type=e,this.$_root=null,this._definition={},this._reset()}_reset(){this._ids=new h.Ids,this._preferences=null,this._refs=new m.Manager,this._cache=null,this._valids=null,this._invalids=null,this._flags={},this._rules=[],this._singleRules=new Map,this.$_terms={},this.$_temp={ruleset:null,whens:{}}}describe(){return n("function"==typeof f.describe,"Manifest functionality disabled"),f.describe(this)}allow(...e){return c.verifyFlat(e,"allow"),this._values(e,"_valids")}alter(e){n(e&&"object"==typeof e&&!Array.isArray(e),"Invalid targets argument"),n(!this._inRuleset(),"Cannot set alterations inside a ruleset");const t=this.clone();t.$_terms.alterations=t.$_terms.alterations||[];for(const r in e){const s=e[r];n("function"==typeof s,"Alteration adjuster for",r,"must be a function"),t.$_terms.alterations.push({target:r,adjuster:s})}return t.$_temp.ruleset=!1,t}artifact(e){return n(void 0!==e,"Artifact cannot be undefined"),n(!this._cache,"Cannot set an artifact with a rule cache"),this.$_setFlag("artifact",e)}cast(e){return n(!1===e||"string"==typeof e,"Invalid to value"),n(!1===e||this._definition.cast[e],"Type",this.type,"does not support casting to",e),this.$_setFlag("cast",!1===e?void 0:e)}default(e,t){return this._default("default",e,t)}description(e){return n(e&&"string"==typeof e,"Description must be a non-empty string"),this.$_setFlag("description",e)}empty(e){const t=this.clone();return void 0!==e&&(e=t.$_compile(e,{override:!1})),t.$_setFlag("empty",e,{clone:!1})}error(e){return n(e,"Missing error"),n(e instanceof Error||"function"==typeof e,"Must provide a valid Error object or a function"),this.$_setFlag("error",e)}example(e,t={}){return n(void 0!==e,"Missing example"),c.assertOptions(t,["override"]),this._inner("examples",e,{single:!0,override:t.override})}external(e,t){return"object"==typeof e&&(n(!t,"Cannot combine options with description"),t=e.description,e=e.method),n("function"==typeof e,"Method must be a function"),n(void 0===t||t&&"string"==typeof t,"Description must be a non-empty string"),this._inner("externals",{method:e,description:t},{single:!0})}failover(e,t){return this._default("failover",e,t)}forbidden(){return this.presence("forbidden")}id(e){return e?(n("string"==typeof e,"id must be a non-empty string"),n(/^[^\.]+$/.test(e),"id cannot contain period character"),this.$_setFlag("id",e)):this.$_setFlag("id",void 0)}invalid(...e){return this._values(e,"_invalids")}label(e){return n(e&&"string"==typeof e,"Label name must be a non-empty string"),this.$_setFlag("label",e)}meta(e){return n(void 0!==e,"Meta cannot be undefined"),this._inner("metas",e,{single:!0})}note(...e){n(e.length,"Missing notes");for(const t of e)n(t&&"string"==typeof t,"Notes must be non-empty strings");return this._inner("notes",e)}only(e=!0){return n("boolean"==typeof e,"Invalid mode:",e),this.$_setFlag("only",e)}optional(){return this.presence("optional")}prefs(e){n(e,"Missing preferences"),n(void 0===e.context,"Cannot override context"),n(void 0===e.externals,"Cannot override externals"),n(void 0===e.warnings,"Cannot override warnings"),n(void 0===e.debug,"Cannot override debug"),c.checkPreferences(e);const t=this.clone();return t._preferences=c.preferences(t._preferences,e),t}presence(e){return n(["optional","required","forbidden"].includes(e),"Unknown presence mode",e),this.$_setFlag("presence",e)}raw(e=!0){return this.$_setFlag("result",e?"raw":void 0)}result(e){return n(["raw","strip"].includes(e),"Unknown result mode",e),this.$_setFlag("result",e)}required(){return this.presence("required")}strict(e){const t=this.clone(),r=void 0!==e&&!e;return t._preferences=c.preferences(t._preferences,{convert:r}),t}strip(e=!0){return this.$_setFlag("result",e?"strip":void 0)}tag(...e){n(e.length,"Missing tags");for(const t of e)n(t&&"string"==typeof t,"Tags must be non-empty strings");return this._inner("tags",e)}unit(e){return n(e&&"string"==typeof e,"Unit name must be a non-empty string"),this.$_setFlag("unit",e)}valid(...e){c.verifyFlat(e,"valid");const t=this.allow(...e);return t.$_setFlag("only",!!t._valids,{clone:!1}),t}when(e,t){const r=this.clone();r.$_terms.whens||(r.$_terms.whens=[]);const s=u.when(r,e,t);if(!["any","link"].includes(r.type)){const e=s.is?[s]:s.switch;for(const t of e)n(!t.then||"any"===t.then.type||t.then.type===r.type,"Cannot combine",r.type,"with",t.then&&t.then.type),n(!t.otherwise||"any"===t.otherwise.type||t.otherwise.type===r.type,"Cannot combine",r.type,"with",t.otherwise&&t.otherwise.type)}return r.$_terms.whens.push(s),r.$_mutateRebuild()}cache(e){n(!this._inRuleset(),"Cannot set caching inside a ruleset"),n(!this._cache,"Cannot override schema cache"),n(void 0===this._flags.artifact,"Cannot cache a rule with an artifact");const t=this.clone();return t._cache=e||a.provider.provision(),t.$_temp.ruleset=!1,t}clone(){const e=Object.create(Object.getPrototypeOf(this));return this._assign(e)}concat(e){n(c.isSchema(e),"Invalid schema object"),n("any"===this.type||"any"===e.type||e.type===this.type,"Cannot merge type",this.type,"with another type:",e.type),n(!this._inRuleset(),"Cannot concatenate onto a schema with open ruleset"),n(!e._inRuleset(),"Cannot concatenate a schema with open ruleset");let t=this.clone();if("any"===this.type&&"any"!==e.type){const r=e.clone();for(const e of Object.keys(t))"type"!==e&&(r[e]=t[e]);t=r}t._ids.concat(e._ids),t._refs.register(e,m.toSibling),t._preferences=t._preferences?c.preferences(t._preferences,e._preferences):e._preferences,t._valids=v.merge(t._valids,e._valids,e._invalids),t._invalids=v.merge(t._invalids,e._invalids,e._valids);for(const r of e._singleRules.keys())t._singleRules.has(r)&&(t._rules=t._rules.filter((e=>e.keep||e.name!==r)),t._singleRules.delete(r));for(const r of e._rules)e._definition.rules[r.method].multi||t._singleRules.set(r.name,r),t._rules.push(r);if(t._flags.empty&&e._flags.empty){t._flags.empty=t._flags.empty.concat(e._flags.empty);const r=Object.assign({},e._flags);delete r.empty,o(t._flags,r)}else if(e._flags.empty){t._flags.empty=e._flags.empty;const r=Object.assign({},e._flags);delete r.empty,o(t._flags,r)}else o(t._flags,e._flags);for(const r in e.$_terms){const n=e.$_terms[r];n?t.$_terms[r]?t.$_terms[r]=t.$_terms[r].concat(n):t.$_terms[r]=n.slice():t.$_terms[r]||(t.$_terms[r]=n)}return this.$_root._tracer&&this.$_root._tracer._combine(t,[this,e]),t.$_mutateRebuild()}extend(e){return n(!e.base,"Cannot extend type with another base"),d.type(this,e)}extract(e){return e=Array.isArray(e)?e:e.split("."),this._ids.reach(e)}fork(e,t){n(!this._inRuleset(),"Cannot fork inside a ruleset");let r=this;for(let n of[].concat(e))n=Array.isArray(n)?n:n.split("."),r=r._ids.fork(n,t,r);return r.$_temp.ruleset=!1,r}rule(e){const t=this._definition;c.assertOptions(e,Object.keys(t.modifiers)),n(!1!==this.$_temp.ruleset,"Cannot apply rules to empty ruleset or the last rule added does not support rule properties");const r=null===this.$_temp.ruleset?this._rules.length-1:this.$_temp.ruleset;n(r>=0&&r<this._rules.length,"Cannot apply rules to empty ruleset");const i=this.clone();for(let o=r;o<i._rules.length;++o){const r=i._rules[o],a=s(r);for(const s in e)t.modifiers[s](a,e[s]),n(a.name===r.name,"Cannot change rule name");i._rules[o]=a,i._singleRules.get(a.name)===r&&i._singleRules.set(a.name,a)}return i.$_temp.ruleset=!1,i.$_mutateRebuild()}get ruleset(){n(!this._inRuleset(),"Cannot start a new ruleset without closing the previous one");const e=this.clone();return e.$_temp.ruleset=e._rules.length,e}get $(){return this.ruleset}tailor(e){e=[].concat(e),n(!this._inRuleset(),"Cannot tailor inside a ruleset");let t=this;if(this.$_terms.alterations)for(const{target:r,adjuster:s}of this.$_terms.alterations)e.includes(r)&&(t=s(t),n(c.isSchema(t),"Alteration adjuster for",r,"failed to return a schema object"));return t=t.$_modify({each:t=>t.tailor(e),ref:!1}),t.$_temp.ruleset=!1,t.$_mutateRebuild()}tracer(){return g.location?g.location(this):this}validate(e,t){return y.entry(e,this,t)}validateAsync(e,t){return y.entryAsync(e,this,t)}$_addRule(e){"string"==typeof e&&(e={name:e}),n(e&&"object"==typeof e,"Invalid options"),n(e.name&&"string"==typeof e.name,"Invalid rule name");for(const t in e)n("_"!==t[0],"Cannot set private rule properties");const t=Object.assign({},e);t._resolve=[],t.method=t.method||t.name;const r=this._definition.rules[t.method],s=t.args;n(r,"Unknown rule",t.method);const i=this.clone();if(s){n(1===Object.keys(s).length||Object.keys(s).length===this._definition.rules[t.name].args.length,"Invalid rule definition for",this.type,t.name);for(const e in s){let o=s[e];if(r.argsByName){const a=r.argsByName.get(e);if(a.ref&&c.isResolvable(o))t._resolve.push(e),i.$_mutateRegister(o);else if(a.normalize&&(o=a.normalize(o),s[e]=o),a.assert){const t=c.validateArg(o,e,a);n(!t,t,"or reference")}}void 0!==o?s[e]=o:delete s[e]}}return r.multi||(i._ruleRemove(t.name,{clone:!1}),i._singleRules.set(t.name,t)),!1===i.$_temp.ruleset&&(i.$_temp.ruleset=null),r.priority?i._rules.unshift(t):i._rules.push(t),i}$_compile(e,t){return u.schema(this.$_root,e,t)}$_createError(e,t,r,n,s,i={}){const o=!1!==i.flags?this._flags:{},a=i.messages?p.merge(this._definition.messages,i.messages):this._definition.messages;return new l.Report(e,t,r,o,a,n,s)}$_getFlag(e){return this._flags[e]}$_getRule(e){return this._singleRules.get(e)}$_mapLabels(e){return e=Array.isArray(e)?e:e.split("."),this._ids.labels(e)}$_match(e,t,r,n){(r=Object.assign({},r)).abortEarly=!0,r._externals=!1,t.snapshot();const s=!y.validate(e,this,t,r,n).errors;return t.restore(),s}$_modify(e){return c.assertOptions(e,["each","once","ref","schema"]),h.schema(this,e)||this}$_mutateRebuild(){return n(!this._inRuleset(),"Cannot add this rule inside a ruleset"),this._refs.reset(),this._ids.reset(),this.$_modify({each:(e,{source:t,name:r,path:n,key:s})=>{const i=this._definition[t][r]&&this._definition[t][r].register;!1!==i&&this.$_mutateRegister(e,{family:i,key:s})}}),this._definition.rebuild&&this._definition.rebuild(this),this.$_temp.ruleset=!1,this}$_mutateRegister(e,{family:t,key:r}={}){this._refs.register(e,t),this._ids.register(e,{key:r})}$_property(e){return this._definition.properties[e]}$_reach(e){return this._ids.reach(e)}$_rootReferences(){return this._refs.roots()}$_setFlag(e,t,r={}){n("_"===e[0]||!this._inRuleset(),"Cannot set flag inside a ruleset");const s=this._definition.flags[e]||{};if(i(t,s.default)&&(t=void 0),i(t,this._flags[e]))return this;const o=!1!==r.clone?this.clone():this;return void 0!==t?(o._flags[e]=t,o.$_mutateRegister(t)):delete o._flags[e],"_"!==e[0]&&(o.$_temp.ruleset=!1),o}$_parent(e,...t){return this[e][c.symbols.parent].call(this,...t)}$_validate(e,t,r){return y.validate(e,this,t,r)}_assign(e){e.type=this.type,e.$_root=this.$_root,e.$_temp=Object.assign({},this.$_temp),e.$_temp.whens={},e._ids=this._ids.clone(),e._preferences=this._preferences,e._valids=this._valids&&this._valids.clone(),e._invalids=this._invalids&&this._invalids.clone(),e._rules=this._rules.slice(),e._singleRules=s(this._singleRules,{shallow:!0}),e._refs=this._refs.clone(),e._flags=Object.assign({},this._flags),e._cache=null,e.$_terms={};for(const t in this.$_terms)e.$_terms[t]=this.$_terms[t]?this.$_terms[t].slice():null;e.$_super={};for(const t in this.$_super)e.$_super[t]=this._super[t].bind(e);return e}_bare(){const e=this.clone();e._reset();const t=e._definition.terms;for(const r in t){const n=t[r];e.$_terms[r]=n.init}return e.$_mutateRebuild()}_default(e,t,r={}){return c.assertOptions(r,"literal"),n(void 0!==t,"Missing",e,"value"),n("function"==typeof t||!r.literal,"Only function value supports literal option"),"function"==typeof t&&r.literal&&(t={[c.symbols.literal]:!0,literal:t}),this.$_setFlag(e,t)}_generate(e,t,r){if(!this.$_terms.whens)return{schema:this};const n=[],s=[];for(let i=0;i<this.$_terms.whens.length;++i){const o=this.$_terms.whens[i];if(o.concat){n.push(o.concat),s.push(`${i}.concat`);continue}const a=o.ref?o.ref.resolve(e,t,r):e,c=o.is?[o]:o.switch,u=s.length;for(let u=0;u<c.length;++u){const{is:l,then:d,otherwise:f}=c[u],p=`${i}${o.switch?"."+u:""}`;if(l.$_match(a,t.nest(l,`${p}.is`),r)){if(d){const i=t.localize([...t.path,`${p}.then`],t.ancestors,t.schemas),{schema:o,id:a}=d._generate(e,i,r);n.push(o),s.push(`${p}.then${a?`(${a})`:""}`);break}}else if(f){const i=t.localize([...t.path,`${p}.otherwise`],t.ancestors,t.schemas),{schema:o,id:a}=f._generate(e,i,r);n.push(o),s.push(`${p}.otherwise${a?`(${a})`:""}`);break}}if(o.break&&s.length>u)break}const i=s.join(", ");if(t.mainstay.tracer.debug(t,"rule","when",i),!i)return{schema:this};if(!t.mainstay.tracer.active&&this.$_temp.whens[i])return{schema:this.$_temp.whens[i],id:i};let o=this;this._definition.generate&&(o=this._definition.generate(this,e,t,r));for(const e of n)o=o.concat(e);return this.$_root._tracer&&this.$_root._tracer._combine(o,[this,...n]),this.$_temp.whens[i]=o,{schema:o,id:i}}_inner(e,t,r={}){n(!this._inRuleset(),`Cannot set ${e} inside a ruleset`);const s=this.clone();return s.$_terms[e]&&!r.override||(s.$_terms[e]=[]),r.single?s.$_terms[e].push(t):s.$_terms[e].push(...t),s.$_temp.ruleset=!1,s}_inRuleset(){return null!==this.$_temp.ruleset&&!1!==this.$_temp.ruleset}_ruleRemove(e,t={}){if(!this._singleRules.has(e))return this;const r=!1!==t.clone?this.clone():this;r._singleRules.delete(e);const n=[];for(let t=0;t<r._rules.length;++t){const s=r._rules[t];s.name!==e||s.keep?n.push(s):r._inRuleset()&&t<r.$_temp.ruleset&&--r.$_temp.ruleset}return r._rules=n,r}_values(e,t){c.verifyFlat(e,t.slice(1,-1));const r=this.clone(),s=e[0]===c.symbols.override;if(s&&(e=e.slice(1)),!r[t]&&e.length?r[t]=new v:s&&(r[t]=e.length?new v:null,r.$_mutateRebuild()),!r[t])return r;s&&r[t].override();for(const s of e){n(void 0!==s,"Cannot call allow/valid/invalid with undefined"),n(s!==c.symbols.override,"Override must be the first value");const e="_invalids"===t?"_valids":"_invalids";r[e]&&(r[e].remove(s),r[e].length||(n("_valids"===t||!r._flags.only,"Setting invalid value",s,"leaves schema rejecting all values due to previous valid rule"),r[e]=null)),r[t].add(s,r._refs)}return r}}};b.Base.prototype[c.symbols.any]={version:c.version,compile:u.compile,root:"$_root"},b.Base.prototype.isImmutable=!0,b.Base.prototype.deny=b.Base.prototype.invalid,b.Base.prototype.disallow=b.Base.prototype.invalid,b.Base.prototype.equal=b.Base.prototype.valid,b.Base.prototype.exist=b.Base.prototype.required,b.Base.prototype.not=b.Base.prototype.invalid,b.Base.prototype.options=b.Base.prototype.prefs,b.Base.prototype.preferences=b.Base.prototype.prefs,e.exports=new b.Base},8652:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(8160),o={max:1e3,supported:new Set(["undefined","boolean","number","string"])};t.provider={provision:e=>new o.Cache(e)},o.Cache=class{constructor(e={}){i.assertOptions(e,["max"]),n(void 0===e.max||e.max&&e.max>0&&isFinite(e.max),"Invalid max cache size"),this._max=e.max||o.max,this._map=new Map,this._list=new o.List}get length(){return this._map.size}set(e,t){if(null!==e&&!o.supported.has(typeof e))return;let r=this._map.get(e);if(r)return r.value=t,void this._list.first(r);r=this._list.unshift({key:e,value:t}),this._map.set(e,r),this._compact()}get(e){const t=this._map.get(e);if(t)return this._list.first(t),s(t.value)}_compact(){if(this._map.size>this._max){const e=this._list.pop();this._map.delete(e.key)}}},o.List=class{constructor(){this.tail=null,this.head=null}unshift(e){return e.next=null,e.prev=this.head,this.head&&(this.head.next=e),this.head=e,this.tail||(this.tail=e),e}first(e){e!==this.head&&(this._remove(e),this.unshift(e))}pop(){return this._remove(this.tail)}_remove(e){const{next:t,prev:r}=e;return t.prev=r,r&&(r.next=t),e===this.tail&&(this.tail=t),e.prev=null,e.next=null,e}}},8160:(e,t,r)=>{"use strict";const n=r(375),s=r(7916),i=r(5934);let o,a;const c={isoDate:/^(?:[-+]\d{2})?(?:\d{4}(?!\d{2}\b))(?:(-?)(?:(?:0[1-9]|1[0-2])(?:\1(?:[12]\d|0[1-9]|3[01]))?|W(?:[0-4]\d|5[0-2])(?:-?[1-7])?|(?:00[1-9]|0[1-9]\d|[12]\d{2}|3(?:[0-5]\d|6[1-6])))(?![T]$|[T][\d]+Z$)(?:[T\s](?:(?:(?:[01]\d|2[0-3])(?:(:?)[0-5]\d)?|24\:?00)(?:[.,]\d+(?!:))?)(?:\2[0-5]\d(?:[.,]\d+)?)?(?:[Z]|(?:[+-])(?:[01]\d|2[0-3])(?::?[0-5]\d)?)?)?)?$/};t.version=i.version,t.defaults={abortEarly:!0,allowUnknown:!1,artifacts:!1,cache:!0,context:null,convert:!0,dateFormat:"iso",errors:{escapeHtml:!1,label:"path",language:null,render:!0,stack:!1,wrap:{label:'"',array:"[]"}},externals:!0,messages:{},nonEnumerables:!1,noDefaults:!1,presence:"optional",skipFunctions:!1,stripUnknown:!1,warnings:!1},t.symbols={any:Symbol.for("@hapi/joi/schema"),arraySingle:Symbol("arraySingle"),deepDefault:Symbol("deepDefault"),errors:Symbol("errors"),literal:Symbol("literal"),override:Symbol("override"),parent:Symbol("parent"),prefs:Symbol("prefs"),ref:Symbol("ref"),template:Symbol("template"),values:Symbol("values")},t.assertOptions=function(e,t,r="Options"){n(e&&"object"==typeof e&&!Array.isArray(e),"Options must be of type object");const s=Object.keys(e).filter((e=>!t.includes(e)));n(0===s.length,`${r} contain unknown keys: ${s}`)},t.checkPreferences=function(e){a=a||r(3378);const t=a.preferences.validate(e);if(t.error)throw new s([t.error.details[0].message])},t.compare=function(e,t,r){switch(r){case"=":return e===t;case">":return e>t;case"<":return e<t;case">=":return e>=t;case"<=":return e<=t}},t.default=function(e,t){return void 0===e?t:e},t.isIsoDate=function(e){return c.isoDate.test(e)},t.isNumber=function(e){return"number"==typeof e&&!isNaN(e)},t.isResolvable=function(e){return!!e&&(e[t.symbols.ref]||e[t.symbols.template])},t.isSchema=function(e,r={}){const s=e&&e[t.symbols.any];return!!s&&(n(r.legacy||s.version===t.version,"Cannot mix different versions of joi schemas"),!0)},t.isValues=function(e){return e[t.symbols.values]},t.limit=function(e){return Number.isSafeInteger(e)&&e>=0},t.preferences=function(e,n){o=o||r(6914),e=e||{},n=n||{};const s=Object.assign({},e,n);return n.errors&&e.errors&&(s.errors=Object.assign({},e.errors,n.errors),s.errors.wrap=Object.assign({},e.errors.wrap,n.errors.wrap)),n.messages&&(s.messages=o.compile(n.messages,e.messages)),delete s[t.symbols.prefs],s},t.tryWithPath=function(e,t,r={}){try{return e()}catch(e){throw void 0!==e.path?e.path=t+"."+e.path:e.path=t,r.append&&(e.message=`${e.message} (${e.path})`),e}},t.validateArg=function(e,r,{assert:n,message:s}){if(t.isSchema(n)){const t=n.validate(e);if(!t.error)return;return t.error.message}if(!n(e))return r?`${r} ${s}`:s},t.verifyFlat=function(e,t){for(const r of e)n(!Array.isArray(r),"Method no longer accepts array arguments:",t)}},3292:(e,t,r)=>{"use strict";const n=r(375),s=r(8160),i=r(6133),o={};t.schema=function(e,t,r={}){s.assertOptions(r,["appendPath","override"]);try{return o.schema(e,t,r)}catch(e){throw r.appendPath&&void 0!==e.path&&(e.message=`${e.message} (${e.path})`),e}},o.schema=function(e,t,r){n(void 0!==t,"Invalid undefined schema"),Array.isArray(t)&&(n(t.length,"Invalid empty array schema"),1===t.length&&(t=t[0]));const i=(t,...n)=>!1!==r.override?t.valid(e.override,...n):t.valid(...n);if(o.simple(t))return i(e,t);if("function"==typeof t)return e.custom(t);if(n("object"==typeof t,"Invalid schema content:",typeof t),s.isResolvable(t))return i(e,t);if(s.isSchema(t))return t;if(Array.isArray(t)){for(const r of t)if(!o.simple(r))return e.alternatives().try(...t);return i(e,...t)}return t instanceof RegExp?e.string().regex(t):t instanceof Date?i(e.date(),t):(n(Object.getPrototypeOf(t)===Object.getPrototypeOf({}),"Schema can only contain plain objects"),e.object().keys(t))},t.ref=function(e,t){return i.isRef(e)?e:i.create(e,t)},t.compile=function(e,r,i={}){s.assertOptions(i,["legacy"]);const a=r&&r[s.symbols.any];if(a)return n(i.legacy||a.version===s.version,"Cannot mix different versions of joi schemas:",a.version,s.version),r;if("object"!=typeof r||!i.legacy)return t.schema(e,r,{appendPath:!0});const c=o.walk(r);return c?c.compile(c.root,r):t.schema(e,r,{appendPath:!0})},o.walk=function(e){if("object"!=typeof e)return null;if(Array.isArray(e)){for(const t of e){const e=o.walk(t);if(e)return e}return null}const t=e[s.symbols.any];if(t)return{root:e[t.root],compile:t.compile};n(Object.getPrototypeOf(e)===Object.getPrototypeOf({}),"Schema can only contain plain objects");for(const t in e){const r=o.walk(e[t]);if(r)return r}return null},o.simple=function(e){return null===e||["boolean","string","number"].includes(typeof e)},t.when=function(e,r,a){if(void 0===a&&(n(r&&"object"==typeof r,"Missing options"),a=r,r=i.create(".")),Array.isArray(a)&&(a={switch:a}),s.assertOptions(a,["is","not","then","otherwise","switch","break"]),s.isSchema(r))return n(void 0===a.is,'"is" can not be used with a schema condition'),n(void 0===a.not,'"not" can not be used with a schema condition'),n(void 0===a.switch,'"switch" can not be used with a schema condition'),o.condition(e,{is:r,then:a.then,otherwise:a.otherwise,break:a.break});if(n(i.isRef(r)||"string"==typeof r,"Invalid condition:",r),n(void 0===a.not||void 0===a.is,'Cannot combine "is" with "not"'),void 0===a.switch){let c=a;void 0!==a.not&&(c={is:a.not,then:a.otherwise,otherwise:a.then,break:a.break});let u=void 0!==c.is?e.$_compile(c.is):e.$_root.invalid(null,!1,0,"").required();return n(void 0!==c.then||void 0!==c.otherwise,'options must have at least one of "then", "otherwise", or "switch"'),n(void 0===c.break||void 0===c.then||void 0===c.otherwise,"Cannot specify then, otherwise, and break all together"),void 0===a.is||i.isRef(a.is)||s.isSchema(a.is)||(u=u.required()),o.condition(e,{ref:t.ref(r),is:u,then:c.then,otherwise:c.otherwise,break:c.break})}n(Array.isArray(a.switch),'"switch" must be an array'),n(void 0===a.is,'Cannot combine "switch" with "is"'),n(void 0===a.not,'Cannot combine "switch" with "not"'),n(void 0===a.then,'Cannot combine "switch" with "then"');const c={ref:t.ref(r),switch:[],break:a.break};for(let t=0;t<a.switch.length;++t){const r=a.switch[t],o=t===a.switch.length-1;s.assertOptions(r,o?["is","then","otherwise"]:["is","then"]),n(void 0!==r.is,'Switch statement missing "is"'),n(void 0!==r.then,'Switch statement missing "then"');const u={is:e.$_compile(r.is),then:e.$_compile(r.then)};if(i.isRef(r.is)||s.isSchema(r.is)||(u.is=u.is.required()),o){n(void 0===a.otherwise||void 0===r.otherwise,'Cannot specify "otherwise" inside and outside a "switch"');const t=void 0!==a.otherwise?a.otherwise:r.otherwise;void 0!==t&&(n(void 0===c.break,"Cannot specify both otherwise and break"),u.otherwise=e.$_compile(t))}c.switch.push(u)}return c},o.condition=function(e,t){for(const r of["then","otherwise"])void 0===t[r]?delete t[r]:t[r]=e.$_compile(t[r]);return t}},6354:(e,t,r)=>{"use strict";const n=r(5688),s=r(8160),i=r(3328);t.Report=class{constructor(e,r,n,s,i,o,a){if(this.code=e,this.flags=s,this.messages=i,this.path=o.path,this.prefs=a,this.state=o,this.value=r,this.message=null,this.template=null,this.local=n||{},this.local.label=t.label(this.flags,this.state,this.prefs,this.messages),void 0===this.value||this.local.hasOwnProperty("value")||(this.local.value=this.value),this.path.length){const e=this.path[this.path.length-1];"object"!=typeof e&&(this.local.key=e)}}_setTemplate(e){if(this.template=e,!this.flags.label&&0===this.path.length){const e=this._template(this.template,"root");e&&(this.local.label=e)}}toString(){if(this.message)return this.message;const e=this.code;if(!this.prefs.errors.render)return this.code;const t=this._template(this.template)||this._template(this.prefs.messages)||this._template(this.messages);return void 0===t?`Error code "${e}" is not defined, your custom type is missing the correct messages definition`:(this.message=t.render(this.value,this.state,this.prefs,this.local,{errors:this.prefs.errors,messages:[this.prefs.messages,this.messages]}),this.prefs.errors.label||(this.message=this.message.replace(/^"" /,"").trim()),this.message)}_template(e,r){return t.template(this.value,e,r||this.code,this.state,this.prefs)}},t.path=function(e){let t="";for(const r of e)"object"!=typeof r&&("string"==typeof r?(t&&(t+="."),t+=r):t+=`[${r}]`);return t},t.template=function(e,t,r,n,o){if(!t)return;if(i.isTemplate(t))return"root"!==r?t:null;let a=o.errors.language;if(s.isResolvable(a)&&(a=a.resolve(e,n,o)),a&&t[a]){if(void 0!==t[a][r])return t[a][r];if(void 0!==t[a]["*"])return t[a]["*"]}return t[r]?t[r]:t["*"]},t.label=function(e,r,n,s){if(!n.errors.label)return"";if(e.label)return e.label;let i=r.path;return"key"===n.errors.label&&r.path.length>1&&(i=r.path.slice(-1)),t.path(i)||t.template(null,n.messages,"root",r,n)||s&&t.template(null,s,"root",r,n)||"value"},t.process=function(e,r,n){if(!e)return null;const{override:s,message:i,details:o}=t.details(e);if(s)return s;if(n.errors.stack)return new t.ValidationError(i,o,r);const a=Error.stackTraceLimit;Error.stackTraceLimit=0;const c=new t.ValidationError(i,o,r);return Error.stackTraceLimit=a,c},t.details=function(e,t={}){let r=[];const n=[];for(const s of e){if(s instanceof Error){if(!1!==t.override)return{override:s};const e=s.toString();r.push(e),n.push({message:e,type:"override",context:{error:s}});continue}const e=s.toString();r.push(e),n.push({message:e,path:s.path.filter((e=>"object"!=typeof e)),type:s.code,context:s.local})}return r.length>1&&(r=[...new Set(r)]),{message:r.join(". "),details:n}},t.ValidationError=class extends Error{constructor(e,t,r){super(e),this._original=r,this.details=t}static isError(e){return e instanceof t.ValidationError}},t.ValidationError.prototype.isJoi=!0,t.ValidationError.prototype.name="ValidationError",t.ValidationError.prototype.annotate=n.error},8901:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(8160),o=r(6914),a={};t.type=function(e,t){const r=Object.getPrototypeOf(e),c=s(r),u=e._assign(Object.create(c)),l=Object.assign({},t);delete l.base,c._definition=l;const d=r._definition||{};l.messages=o.merge(d.messages,l.messages),l.properties=Object.assign({},d.properties,l.properties),u.type=l.type,l.flags=Object.assign({},d.flags,l.flags);const f=Object.assign({},d.terms);if(l.terms)for(const e in l.terms){const t=l.terms[e];n(void 0===u.$_terms[e],"Invalid term override for",l.type,e),u.$_terms[e]=t.init,f[e]=t}l.terms=f,l.args||(l.args=d.args),l.prepare=a.prepare(l.prepare,d.prepare),l.coerce&&("function"==typeof l.coerce&&(l.coerce={method:l.coerce}),l.coerce.from&&!Array.isArray(l.coerce.from)&&(l.coerce={method:l.coerce.method,from:[].concat(l.coerce.from)})),l.coerce=a.coerce(l.coerce,d.coerce),l.validate=a.validate(l.validate,d.validate);const p=Object.assign({},d.rules);if(l.rules)for(const e in l.rules){const t=l.rules[e];n("object"==typeof t,"Invalid rule definition for",l.type,e);let r=t.method;if(void 0===r&&(r=function(){return this.$_addRule(e)}),r&&(n(!c[e],"Rule conflict in",l.type,e),c[e]=r),n(!p[e],"Rule conflict in",l.type,e),p[e]=t,t.alias){const e=[].concat(t.alias);for(const r of e)c[r]=t.method}t.args&&(t.argsByName=new Map,t.args=t.args.map((e=>("string"==typeof e&&(e={name:e}),n(!t.argsByName.has(e.name),"Duplicated argument name",e.name),i.isSchema(e.assert)&&(e.assert=e.assert.strict().label(e.name)),t.argsByName.set(e.name,e),e))))}l.rules=p;const h=Object.assign({},d.modifiers);if(l.modifiers)for(const e in l.modifiers){n(!c[e],"Rule conflict in",l.type,e);const t=l.modifiers[e];n("function"==typeof t,"Invalid modifier definition for",l.type,e);const r=function(t){return this.rule({[e]:t})};c[e]=r,h[e]=t}if(l.modifiers=h,l.overrides){c._super=r,u.$_super={};for(const e in l.overrides)n(r[e],"Cannot override missing",e),l.overrides[e][i.symbols.parent]=r[e],u.$_super[e]=r[e].bind(u);Object.assign(c,l.overrides)}l.cast=Object.assign({},d.cast,l.cast);const m=Object.assign({},d.manifest,l.manifest);return m.build=a.build(l.manifest&&l.manifest.build,d.manifest&&d.manifest.build),l.manifest=m,l.rebuild=a.rebuild(l.rebuild,d.rebuild),u},a.build=function(e,t){return e&&t?function(r,n){return t(e(r,n),n)}:e||t},a.coerce=function(e,t){return e&&t?{from:e.from&&t.from?[...new Set([...e.from,...t.from])]:null,method(r,n){let s;if((!t.from||t.from.includes(typeof r))&&(s=t.method(r,n),s)){if(s.errors||void 0===s.value)return s;r=s.value}if(!e.from||e.from.includes(typeof r)){const t=e.method(r,n);if(t)return t}return s}}:e||t},a.prepare=function(e,t){return e&&t?function(r,n){const s=e(r,n);if(s){if(s.errors||void 0===s.value)return s;r=s.value}return t(r,n)||s}:e||t},a.rebuild=function(e,t){return e&&t?function(r){t(r),e(r)}:e||t},a.validate=function(e,t){return e&&t?function(r,n){const s=t(r,n);if(s){if(s.errors&&(!Array.isArray(s.errors)||s.errors.length))return s;r=s.value}return e(r,n)||s}:e||t}},5107:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(8652),o=r(8160),a=r(3292),c=r(6354),u=r(8901),l=r(9708),d=r(6133),f=r(3328),p=r(1152);let h;const m={types:{alternatives:r(4946),any:r(8068),array:r(546),boolean:r(4937),date:r(7500),function:r(390),link:r(8785),number:r(3832),object:r(8966),string:r(7417),symbol:r(8826)},aliases:{alt:"alternatives",bool:"boolean",func:"function"},root:function(){const e={_types:new Set(Object.keys(m.types))};for(const t of e._types)e[t]=function(...e){return n(!e.length||["alternatives","link","object"].includes(t),"The",t,"type does not allow arguments"),m.generate(this,m.types[t],e)};for(const t of["allow","custom","disallow","equal","exist","forbidden","invalid","not","only","optional","options","prefs","preferences","required","strip","valid","when"])e[t]=function(...e){return this.any()[t](...e)};Object.assign(e,m.methods);for(const t in m.aliases){const r=m.aliases[t];e[t]=e[r]}return e.x=e.expression,p.setup&&p.setup(e),e}};m.methods={ValidationError:c.ValidationError,version:o.version,cache:i.provider,assert(e,t,...r){m.assert(e,t,!0,r)},attempt:(e,t,...r)=>m.assert(e,t,!1,r),build(e){return n("function"==typeof l.build,"Manifest functionality disabled"),l.build(this,e)},checkPreferences(e){o.checkPreferences(e)},compile(e,t){return a.compile(this,e,t)},defaults(e){n("function"==typeof e,"modifier must be a function");const t=Object.assign({},this);for(const r of t._types){const s=e(t[r]());n(o.isSchema(s),"modifier must return a valid schema object"),t[r]=function(...e){return m.generate(this,s,e)}}return t},expression:(...e)=>new f(...e),extend(...e){o.verifyFlat(e,"extend"),h=h||r(3378),n(e.length,"You need to provide at least one extension"),this.assert(e,h.extensions);const t=Object.assign({},this);t._types=new Set(t._types);for(let r of e){"function"==typeof r&&(r=r(t)),this.assert(r,h.extension);const e=m.expandExtension(r,t);for(const r of e){n(void 0===t[r.type]||t._types.has(r.type),"Cannot override name",r.type);const e=r.base||this.any(),s=u.type(e,r);t._types.add(r.type),t[r.type]=function(...e){return m.generate(this,s,e)}}}return t},isError:c.ValidationError.isError,isExpression:f.isTemplate,isRef:d.isRef,isSchema:o.isSchema,in:(...e)=>d.in(...e),override:o.symbols.override,ref:(...e)=>d.create(...e),types(){const e={};for(const t of this._types)e[t]=this[t]();for(const t in m.aliases)e[t]=this[t]();return e}},m.assert=function(e,t,r,n){const i=n[0]instanceof Error||"string"==typeof n[0]?n[0]:null,a=null!==i?n[1]:n[0],u=t.validate(e,o.preferences({errors:{stack:!0}},a||{}));let l=u.error;if(!l)return u.value;if(i instanceof Error)throw i;const d=r&&"function"==typeof l.annotate?l.annotate():l.message;throw l instanceof c.ValidationError==0&&(l=s(l)),l.message=i?`${i} ${d}`:d,l},m.generate=function(e,t,r){return n(e,"Must be invoked on a Joi instance."),t.$_root=e,t._definition.args&&r.length?t._definition.args(t,...r):t},m.expandExtension=function(e,t){if("string"==typeof e.type)return[e];const r=[];for(const n of t._types)if(e.type.test(n)){const s=Object.assign({},e);s.type=n,s.base=t[n](),r.push(s)}return r},e.exports=m.root()},6914:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(3328);t.compile=function(e,t){if("string"==typeof e)return n(!t,"Cannot set single message string"),new i(e);if(i.isTemplate(e))return n(!t,"Cannot set single message template"),e;n("object"==typeof e&&!Array.isArray(e),"Invalid message options"),t=t?s(t):{};for(let r in e){const s=e[r];if("root"===r||i.isTemplate(s)){t[r]=s;continue}if("string"==typeof s){t[r]=new i(s);continue}n("object"==typeof s&&!Array.isArray(s),"Invalid message for",r);const o=r;for(r in t[o]=t[o]||{},s){const e=s[r];"root"===r||i.isTemplate(e)?t[o][r]=e:(n("string"==typeof e,"Invalid message for",r,"in",o),t[o][r]=new i(e))}}return t},t.decompile=function(e){const t={};for(let r in e){const n=e[r];if("root"===r){t.root=n;continue}if(i.isTemplate(n)){t[r]=n.describe({compact:!0});continue}const s=r;for(r in t[s]={},n){const e=n[r];"root"!==r?t[s][r]=e.describe({compact:!0}):t[s].root=e}}return t},t.merge=function(e,r){if(!e)return t.compile(r);if(!r)return e;if("string"==typeof r)return new i(r);if(i.isTemplate(r))return r;const o=s(e);for(let e in r){const t=r[e];if("root"===e||i.isTemplate(t)){o[e]=t;continue}if("string"==typeof t){o[e]=new i(t);continue}n("object"==typeof t&&!Array.isArray(t),"Invalid message for",e);const s=e;for(e in o[s]=o[s]||{},t){const r=t[e];"root"===e||i.isTemplate(r)?o[s][e]=r:(n("string"==typeof r,"Invalid message for",e,"in",s),o[s][e]=new i(r))}}return o}},2294:(e,t,r)=>{"use strict";const n=r(375),s=r(8160),i=r(6133),o={};t.Ids=o.Ids=class{constructor(){this._byId=new Map,this._byKey=new Map,this._schemaChain=!1}clone(){const e=new o.Ids;return e._byId=new Map(this._byId),e._byKey=new Map(this._byKey),e._schemaChain=this._schemaChain,e}concat(e){e._schemaChain&&(this._schemaChain=!0);for(const[t,r]of e._byId.entries())n(!this._byKey.has(t),"Schema id conflicts with existing key:",t),this._byId.set(t,r);for(const[t,r]of e._byKey.entries())n(!this._byId.has(t),"Schema key conflicts with existing id:",t),this._byKey.set(t,r)}fork(e,t,r){const i=this._collect(e);i.push({schema:r});const a=i.shift();let c={id:a.id,schema:t(a.schema)};n(s.isSchema(c.schema),"adjuster function failed to return a joi schema type");for(const e of i)c={id:e.id,schema:o.fork(e.schema,c.id,c.schema)};return c.schema}labels(e,t=[]){const r=e[0],n=this._get(r);if(!n)return[...t,...e].join(".");const s=e.slice(1);return t=[...t,n.schema._flags.label||r],s.length?n.schema._ids.labels(s,t):t.join(".")}reach(e,t=[]){const r=e[0],s=this._get(r);n(s,"Schema does not contain path",[...t,...e].join("."));const i=e.slice(1);return i.length?s.schema._ids.reach(i,[...t,r]):s.schema}register(e,{key:t}={}){if(!e||!s.isSchema(e))return;(e.$_property("schemaChain")||e._ids._schemaChain)&&(this._schemaChain=!0);const r=e._flags.id;if(r){const t=this._byId.get(r);n(!t||t.schema===e,"Cannot add different schemas with the same id:",r),n(!this._byKey.has(r),"Schema id conflicts with existing key:",r),this._byId.set(r,{schema:e,id:r})}t&&(n(!this._byKey.has(t),"Schema already contains key:",t),n(!this._byId.has(t),"Schema key conflicts with existing id:",t),this._byKey.set(t,{schema:e,id:t}))}reset(){this._byId=new Map,this._byKey=new Map,this._schemaChain=!1}_collect(e,t=[],r=[]){const s=e[0],i=this._get(s);n(i,"Schema does not contain path",[...t,...e].join(".")),r=[i,...r];const o=e.slice(1);return o.length?i.schema._ids._collect(o,[...t,s],r):r}_get(e){return this._byId.get(e)||this._byKey.get(e)}},o.fork=function(e,r,n){const s=t.schema(e,{each:(e,{key:t})=>{if(r===(e._flags.id||t))return n},ref:!1});return s?s.$_mutateRebuild():e},t.schema=function(e,t){let r;for(const n in e._flags){if("_"===n[0])continue;const s=o.scan(e._flags[n],{source:"flags",name:n},t);void 0!==s&&(r=r||e.clone(),r._flags[n]=s)}for(let n=0;n<e._rules.length;++n){const s=e._rules[n],i=o.scan(s.args,{source:"rules",name:s.name},t);if(void 0!==i){r=r||e.clone();const t=Object.assign({},s);t.args=i,r._rules[n]=t,r._singleRules.get(s.name)===s&&r._singleRules.set(s.name,t)}}for(const n in e.$_terms){if("_"===n[0])continue;const s=o.scan(e.$_terms[n],{source:"terms",name:n},t);void 0!==s&&(r=r||e.clone(),r.$_terms[n]=s)}return r},o.scan=function(e,t,r,n,a){const c=n||[];if(null===e||"object"!=typeof e)return;let u;if(Array.isArray(e)){for(let n=0;n<e.length;++n){const s="terms"===t.source&&"keys"===t.name&&e[n].key,i=o.scan(e[n],t,r,[n,...c],s);void 0!==i&&(u=u||e.slice(),u[n]=i)}return u}if(!1!==r.schema&&s.isSchema(e)||!1!==r.ref&&i.isRef(e)){const n=r.each(e,{...t,path:c,key:a});if(n===e)return;return n}for(const n in e){if("_"===n[0])continue;const s=o.scan(e[n],t,r,[n,...c],a);void 0!==s&&(u=u||Object.assign({},e),u[n]=s)}return u}},6133:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(9621),o=r(8160);let a;const c={symbol:Symbol("ref"),defaults:{adjust:null,in:!1,iterables:null,map:null,separator:".",type:"value"}};t.create=function(e,t={}){n("string"==typeof e,"Invalid reference key:",e),o.assertOptions(t,["adjust","ancestor","in","iterables","map","prefix","render","separator"]),n(!t.prefix||"object"==typeof t.prefix,"options.prefix must be of type object");const r=Object.assign({},c.defaults,t);delete r.prefix;const s=r.separator,i=c.context(e,s,t.prefix);if(r.type=i.type,e=i.key,"value"===r.type)if(i.root&&(n(!s||e[0]!==s,"Cannot specify relative path with root prefix"),r.ancestor="root",e||(e=null)),s&&s===e)e=null,r.ancestor=0;else if(void 0!==r.ancestor)n(!s||!e||e[0]!==s,"Cannot combine prefix with ancestor option");else{const[t,n]=c.ancestor(e,s);n&&""===(e=e.slice(n))&&(e=null),r.ancestor=t}return r.path=s?null===e?[]:e.split(s):[e],new c.Ref(r)},t.in=function(e,r={}){return t.create(e,{...r,in:!0})},t.isRef=function(e){return!!e&&!!e[o.symbols.ref]},c.Ref=class{constructor(e){n("object"==typeof e,"Invalid reference construction"),o.assertOptions(e,["adjust","ancestor","in","iterables","map","path","render","separator","type","depth","key","root","display"]),n([!1,void 0].includes(e.separator)||"string"==typeof e.separator&&1===e.separator.length,"Invalid separator"),n(!e.adjust||"function"==typeof e.adjust,"options.adjust must be a function"),n(!e.map||Array.isArray(e.map),"options.map must be an array"),n(!e.map||!e.adjust,"Cannot set both map and adjust options"),Object.assign(this,c.defaults,e),n("value"===this.type||void 0===this.ancestor,"Non-value references cannot reference ancestors"),Array.isArray(this.map)&&(this.map=new Map(this.map)),this.depth=this.path.length,this.key=this.path.length?this.path.join(this.separator):null,this.root=this.path[0],this.updateDisplay()}resolve(e,t,r,s,i={}){return n(!this.in||i.in,"Invalid in() reference usage"),"global"===this.type?this._resolve(r.context,t,i):"local"===this.type?this._resolve(s,t,i):this.ancestor?"root"===this.ancestor?this._resolve(t.ancestors[t.ancestors.length-1],t,i):(n(this.ancestor<=t.ancestors.length,"Invalid reference exceeds the schema root:",this.display),this._resolve(t.ancestors[this.ancestor-1],t,i)):this._resolve(e,t,i)}_resolve(e,t,r){let n;if("value"===this.type&&t.mainstay.shadow&&!1!==r.shadow&&(n=t.mainstay.shadow.get(this.absolute(t))),void 0===n&&(n=i(e,this.path,{iterables:this.iterables,functions:!0})),this.adjust&&(n=this.adjust(n)),this.map){const e=this.map.get(n);void 0!==e&&(n=e)}return t.mainstay&&t.mainstay.tracer.resolve(t,this,n),n}toString(){return this.display}absolute(e){return[...e.path.slice(0,-this.ancestor),...this.path]}clone(){return new c.Ref(this)}describe(){const e={path:this.path};"value"!==this.type&&(e.type=this.type),"."!==this.separator&&(e.separator=this.separator),"value"===this.type&&1!==this.ancestor&&(e.ancestor=this.ancestor),this.map&&(e.map=[...this.map]);for(const t of["adjust","iterables","render"])null!==this[t]&&void 0!==this[t]&&(e[t]=this[t]);return!1!==this.in&&(e.in=!0),{ref:e}}updateDisplay(){const e=null!==this.key?this.key:"";if("value"!==this.type)return void(this.display=`ref:${this.type}:${e}`);if(!this.separator)return void(this.display=`ref:${e}`);if(!this.ancestor)return void(this.display=`ref:${this.separator}${e}`);if("root"===this.ancestor)return void(this.display=`ref:root:${e}`);if(1===this.ancestor)return void(this.display=`ref:${e||".."}`);const t=new Array(this.ancestor+1).fill(this.separator).join("");this.display=`ref:${t}${e||""}`}},c.Ref.prototype[o.symbols.ref]=!0,t.build=function(e){return"value"===(e=Object.assign({},c.defaults,e)).type&&void 0===e.ancestor&&(e.ancestor=1),new c.Ref(e)},c.context=function(e,t,r={}){if(e=e.trim(),r){const n=void 0===r.global?"$":r.global;if(n!==t&&e.startsWith(n))return{key:e.slice(n.length),type:"global"};const s=void 0===r.local?"#":r.local;if(s!==t&&e.startsWith(s))return{key:e.slice(s.length),type:"local"};const i=void 0===r.root?"/":r.root;if(i!==t&&e.startsWith(i))return{key:e.slice(i.length),type:"value",root:!0}}return{key:e,type:"value"}},c.ancestor=function(e,t){if(!t)return[1,0];if(e[0]!==t)return[1,0];if(e[1]!==t)return[0,1];let r=2;for(;e[r]===t;)++r;return[r-1,r]},t.toSibling=0,t.toParent=1,t.Manager=class{constructor(){this.refs=[]}register(e,n){if(e)if(n=void 0===n?t.toParent:n,Array.isArray(e))for(const t of e)this.register(t,n);else if(o.isSchema(e))for(const t of e._refs.refs)t.ancestor-n>=0&&this.refs.push({ancestor:t.ancestor-n,root:t.root});else t.isRef(e)&&"value"===e.type&&e.ancestor-n>=0&&this.refs.push({ancestor:e.ancestor-n,root:e.root}),a=a||r(3328),a.isTemplate(e)&&this.register(e.refs(),n)}get length(){return this.refs.length}clone(){const e=new t.Manager;return e.refs=s(this.refs),e}reset(){this.refs=[]}roots(){return this.refs.filter((e=>!e.ancestor)).map((e=>e.root))}}},3378:(e,t,r)=>{"use strict";const n=r(5107),s={};s.wrap=n.string().min(1).max(2).allow(!1),t.preferences=n.object({allowUnknown:n.boolean(),abortEarly:n.boolean(),artifacts:n.boolean(),cache:n.boolean(),context:n.object(),convert:n.boolean(),dateFormat:n.valid("date","iso","string","time","utc"),debug:n.boolean(),errors:{escapeHtml:n.boolean(),label:n.valid("path","key",!1),language:[n.string(),n.object().ref()],render:n.boolean(),stack:n.boolean(),wrap:{label:s.wrap,array:s.wrap,string:s.wrap}},externals:n.boolean(),messages:n.object(),noDefaults:n.boolean(),nonEnumerables:n.boolean(),presence:n.valid("required","optional","forbidden"),skipFunctions:n.boolean(),stripUnknown:n.object({arrays:n.boolean(),objects:n.boolean()}).or("arrays","objects").allow(!0,!1),warnings:n.boolean()}).strict(),s.nameRx=/^[a-zA-Z0-9]\w*$/,s.rule=n.object({alias:n.array().items(n.string().pattern(s.nameRx)).single(),args:n.array().items(n.string(),n.object({name:n.string().pattern(s.nameRx).required(),ref:n.boolean(),assert:n.alternatives([n.function(),n.object().schema()]).conditional("ref",{is:!0,then:n.required()}),normalize:n.function(),message:n.string().when("assert",{is:n.function(),then:n.required()})})),convert:n.boolean(),manifest:n.boolean(),method:n.function().allow(!1),multi:n.boolean(),validate:n.function()}),t.extension=n.object({type:n.alternatives([n.string(),n.object().regex()]).required(),args:n.function(),cast:n.object().pattern(s.nameRx,n.object({from:n.function().maxArity(1).required(),to:n.function().minArity(1).maxArity(2).required()})),base:n.object().schema().when("type",{is:n.object().regex(),then:n.forbidden()}),coerce:[n.function().maxArity(3),n.object({method:n.function().maxArity(3).required(),from:n.array().items(n.string()).single()})],flags:n.object().pattern(s.nameRx,n.object({setter:n.string(),default:n.any()})),manifest:{build:n.function().arity(2)},messages:[n.object(),n.string()],modifiers:n.object().pattern(s.nameRx,n.function().minArity(1).maxArity(2)),overrides:n.object().pattern(s.nameRx,n.function()),prepare:n.function().maxArity(3),rebuild:n.function().arity(1),rules:n.object().pattern(s.nameRx,s.rule),terms:n.object().pattern(s.nameRx,n.object({init:n.array().allow(null).required(),manifest:n.object().pattern(/.+/,[n.valid("schema","single"),n.object({mapped:n.object({from:n.string().required(),to:n.string().required()}).required()})])})),validate:n.function().maxArity(3)}).strict(),t.extensions=n.array().items(n.object(),n.function().arity(1)).strict(),s.desc={buffer:n.object({buffer:n.string()}),func:n.object({function:n.function().required(),options:{literal:!0}}),override:n.object({override:!0}),ref:n.object({ref:n.object({type:n.valid("value","global","local"),path:n.array().required(),separator:n.string().length(1).allow(!1),ancestor:n.number().min(0).integer().allow("root"),map:n.array().items(n.array().length(2)).min(1),adjust:n.function(),iterables:n.boolean(),in:n.boolean(),render:n.boolean()}).required()}),regex:n.object({regex:n.string().min(3)}),special:n.object({special:n.valid("deep").required()}),template:n.object({template:n.string().required(),options:n.object()}),value:n.object({value:n.alternatives([n.object(),n.array()]).required()})},s.desc.entity=n.alternatives([n.array().items(n.link("...")),n.boolean(),n.function(),n.number(),n.string(),s.desc.buffer,s.desc.func,s.desc.ref,s.desc.regex,s.desc.special,s.desc.template,s.desc.value,n.link("/")]),s.desc.values=n.array().items(null,n.boolean(),n.function(),n.number().allow(1/0,-1/0),n.string().allow(""),n.symbol(),s.desc.buffer,s.desc.func,s.desc.override,s.desc.ref,s.desc.regex,s.desc.template,s.desc.value),s.desc.messages=n.object().pattern(/.+/,[n.string(),s.desc.template,n.object().pattern(/.+/,[n.string(),s.desc.template])]),t.description=n.object({type:n.string().required(),flags:n.object({cast:n.string(),default:n.any(),description:n.string(),empty:n.link("/"),failover:s.desc.entity,id:n.string(),label:n.string(),only:!0,presence:["optional","required","forbidden"],result:["raw","strip"],strip:n.boolean(),unit:n.string()}).unknown(),preferences:{allowUnknown:n.boolean(),abortEarly:n.boolean(),artifacts:n.boolean(),cache:n.boolean(),convert:n.boolean(),dateFormat:["date","iso","string","time","utc"],errors:{escapeHtml:n.boolean(),label:["path","key"],language:[n.string(),s.desc.ref],wrap:{label:s.wrap,array:s.wrap}},externals:n.boolean(),messages:s.desc.messages,noDefaults:n.boolean(),nonEnumerables:n.boolean(),presence:["required","optional","forbidden"],skipFunctions:n.boolean(),stripUnknown:n.object({arrays:n.boolean(),objects:n.boolean()}).or("arrays","objects").allow(!0,!1),warnings:n.boolean()},allow:s.desc.values,invalid:s.desc.values,rules:n.array().min(1).items({name:n.string().required(),args:n.object().min(1),keep:n.boolean(),message:[n.string(),s.desc.messages],warn:n.boolean()}),keys:n.object().pattern(/.*/,n.link("/")),link:s.desc.ref}).pattern(/^[a-z]\w*$/,n.any())},493:(e,t,r)=>{"use strict";const n=r(8571),s=r(9621),i=r(8160),o={value:Symbol("value")};e.exports=o.State=class{constructor(e,t,r){this.path=e,this.ancestors=t,this.mainstay=r.mainstay,this.schemas=r.schemas,this.debug=null}localize(e,t=null,r=null){const n=new o.State(e,t,this);return r&&n.schemas&&(n.schemas=[o.schemas(r),...n.schemas]),n}nest(e,t){const r=new o.State(this.path,this.ancestors,this);return r.schemas=r.schemas&&[o.schemas(e),...r.schemas],r.debug=t,r}shadow(e,t){this.mainstay.shadow=this.mainstay.shadow||new o.Shadow,this.mainstay.shadow.set(this.path,e,t)}snapshot(){this.mainstay.shadow&&(this._snapshot=n(this.mainstay.shadow.node(this.path))),this.mainstay.snapshot()}restore(){this.mainstay.shadow&&(this.mainstay.shadow.override(this.path,this._snapshot),this._snapshot=void 0),this.mainstay.restore()}commit(){this.mainstay.shadow&&(this.mainstay.shadow.override(this.path,this._snapshot),this._snapshot=void 0),this.mainstay.commit()}},o.schemas=function(e){return i.isSchema(e)?{schema:e}:e},o.Shadow=class{constructor(){this._values=null}set(e,t,r){if(!e.length)return;if("strip"===r&&"number"==typeof e[e.length-1])return;this._values=this._values||new Map;let n=this._values;for(let t=0;t<e.length;++t){const r=e[t];let s=n.get(r);s||(s=new Map,n.set(r,s)),n=s}n[o.value]=t}get(e){const t=this.node(e);if(t)return t[o.value]}node(e){if(this._values)return s(this._values,e,{iterables:!0})}override(e,t){if(!this._values)return;const r=e.slice(0,-1),n=e[e.length-1],i=s(this._values,r,{iterables:!0});t?i.set(n,t):i&&i.delete(n)}}},3328:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(5277),o=r(1447),a=r(8160),c=r(6354),u=r(6133),l={symbol:Symbol("template"),opens:new Array(1e3).join("\0"),closes:new Array(1e3).join(""),dateFormat:{date:Date.prototype.toDateString,iso:Date.prototype.toISOString,string:Date.prototype.toString,time:Date.prototype.toTimeString,utc:Date.prototype.toUTCString}};e.exports=l.Template=class{constructor(e,t){if(n("string"==typeof e,"Template source must be a string"),n(!e.includes("\0")&&!e.includes(""),"Template source cannot contain reserved control characters"),this.source=e,this.rendered=e,this._template=null,t){const{functions:e,...r}=t;this._settings=Object.keys(r).length?s(r):void 0,this._functions=e,this._functions&&(n(Object.keys(this._functions).every((e=>"string"==typeof e)),"Functions keys must be strings"),n(Object.values(this._functions).every((e=>"function"==typeof e)),"Functions values must be functions"))}else this._settings=void 0,this._functions=void 0;this._parse()}_parse(){if(!this.source.includes("{"))return;const e=l.encode(this.source),t=l.split(e);let r=!1;const n=[],s=t.shift();s&&n.push(s);for(const e of t){const t="{"!==e[0],s=t?"}":"}}",i=e.indexOf(s);if(-1===i||"{"===e[1]){n.push(`{${l.decode(e)}`);continue}let o=e.slice(t?0:1,i);const a=":"===o[0];a&&(o=o.slice(1));const c=this._ref(l.decode(o),{raw:t,wrapped:a});n.push(c),"string"!=typeof c&&(r=!0);const u=e.slice(i+s.length);u&&n.push(l.decode(u))}r?this._template=n:this.rendered=n.join("")}static date(e,t){return l.dateFormat[t.dateFormat].call(e)}describe(e={}){if(!this._settings&&e.compact)return this.source;const t={template:this.source};return this._settings&&(t.options=this._settings),this._functions&&(t.functions=this._functions),t}static build(e){return new l.Template(e.template,e.options||e.functions?{...e.options,functions:e.functions}:void 0)}isDynamic(){return!!this._template}static isTemplate(e){return!!e&&!!e[a.symbols.template]}refs(){if(!this._template)return;const e=[];for(const t of this._template)"string"!=typeof t&&e.push(...t.refs);return e}resolve(e,t,r,n){return this._template&&1===this._template.length?this._part(this._template[0],e,t,r,n,{}):this.render(e,t,r,n)}_part(e,...t){return e.ref?e.ref.resolve(...t):e.formula.evaluate(t)}render(e,t,r,n,s={}){if(!this.isDynamic())return this.rendered;const o=[];for(const a of this._template)if("string"==typeof a)o.push(a);else{const c=this._part(a,e,t,r,n,s),u=l.stringify(c,e,t,r,n,s);if(void 0!==u){const e=a.raw||!1===(s.errors&&s.errors.escapeHtml)?u:i(u);o.push(l.wrap(e,a.wrapped&&r.errors.wrap.label))}}return o.join("")}_ref(e,{raw:t,wrapped:r}){const n=[],s=e=>{const t=u.create(e,this._settings);return n.push(t),e=>{const r=t.resolve(...e);return void 0!==r?r:null}};try{const t=this._functions?{...l.functions,...this._functions}:l.functions;var i=new o.Parser(e,{reference:s,functions:t,constants:l.constants})}catch(t){throw t.message=`Invalid template variable "${e}" fails due to: ${t.message}`,t}if(i.single){if("reference"===i.single.type){const e=n[0];return{ref:e,raw:t,refs:n,wrapped:r||"local"===e.type&&"label"===e.key}}return l.stringify(i.single.value)}return{formula:i,raw:t,refs:n}}toString(){return this.source}},l.Template.prototype[a.symbols.template]=!0,l.Template.prototype.isImmutable=!0,l.encode=function(e){return e.replace(/\\(\{+)/g,((e,t)=>l.opens.slice(0,t.length))).replace(/\\(\}+)/g,((e,t)=>l.closes.slice(0,t.length)))},l.decode=function(e){return e.replace(/\u0000/g,"{").replace(/\u0001/g,"}")},l.split=function(e){const t=[];let r="";for(let n=0;n<e.length;++n){const s=e[n];if("{"===s){let s="";for(;n+1<e.length&&"{"===e[n+1];)s+="{",++n;t.push(r),r=s}else r+=s}return t.push(r),t},l.wrap=function(e,t){return t?1===t.length?`${t}${e}${t}`:`${t[0]}${e}${t[1]}`:e},l.stringify=function(e,t,r,n,s,i={}){const o=typeof e,a=n&&n.errors&&n.errors.wrap||{};let c=!1;if(u.isRef(e)&&e.render&&(c=e.in,e=e.resolve(t,r,n,s,{in:e.in,...i})),null===e)return"null";if("string"===o)return l.wrap(e,i.arrayItems&&a.string);if("number"===o||"function"===o||"symbol"===o)return e.toString();if("object"!==o)return JSON.stringify(e);if(e instanceof Date)return l.Template.date(e,n);if(e instanceof Map){const t=[];for(const[r,n]of e.entries())t.push(`${r.toString()} -> ${n.toString()}`);e=t}if(!Array.isArray(e))return e.toString();const d=[];for(const o of e)d.push(l.stringify(o,t,r,n,s,{arrayItems:!0,...i}));return l.wrap(d.join(", "),!c&&a.array)},l.constants={true:!0,false:!1,null:null,second:1e3,minute:6e4,hour:36e5,day:864e5},l.functions={if:(e,t,r)=>e?t:r,length:e=>"string"==typeof e?e.length:e&&"object"==typeof e?Array.isArray(e)?e.length:Object.keys(e).length:null,msg(e){const[t,r,n,s,i]=this,o=i.messages;if(!o)return"";const a=c.template(t,o[0],e,r,n)||c.template(t,o[1],e,r,n);return a?a.render(t,r,n,s,i):""},number:e=>"number"==typeof e?e:"string"==typeof e?parseFloat(e):"boolean"==typeof e?e?1:0:e instanceof Date?e.getTime():null}},4946:(e,t,r)=>{"use strict";const n=r(375),s=r(1687),i=r(8068),o=r(8160),a=r(3292),c=r(6354),u=r(6133),l={};e.exports=i.extend({type:"alternatives",flags:{match:{default:"any"}},terms:{matches:{init:[],register:u.toSibling}},args:(e,...t)=>1===t.length&&Array.isArray(t[0])?e.try(...t[0]):e.try(...t),validate(e,t){const{schema:r,error:n,state:i,prefs:o}=t;if(r._flags.match){const t=[],a=[];for(let n=0;n<r.$_terms.matches.length;++n){const s=r.$_terms.matches[n],c=i.nest(s.schema,`match.${n}`);c.snapshot();const u=s.schema.$_validate(e,c,o);u.errors?(a.push(u.errors),c.restore()):(t.push(u.value),c.commit())}if(0===t.length)return{errors:n("alternatives.any",{details:a.map((e=>c.details(e,{override:!1})))})};if("one"===r._flags.match)return 1===t.length?{value:t[0]}:{errors:n("alternatives.one")};if(t.length!==r.$_terms.matches.length)return{errors:n("alternatives.all",{details:a.map((e=>c.details(e,{override:!1})))})};const u=e=>e.$_terms.matches.some((e=>"object"===e.schema.type||"alternatives"===e.schema.type&&u(e.schema)));return u(r)?{value:t.reduce(((e,t)=>s(e,t,{mergeArrays:!1})))}:{value:t[t.length-1]}}const a=[];for(let t=0;t<r.$_terms.matches.length;++t){const n=r.$_terms.matches[t];if(n.schema){const r=i.nest(n.schema,`match.${t}`);r.snapshot();const s=n.schema.$_validate(e,r,o);if(!s.errors)return r.commit(),s;r.restore(),a.push({schema:n.schema,reports:s.errors});continue}const s=n.ref?n.ref.resolve(e,i,o):e,c=n.is?[n]:n.switch;for(let r=0;r<c.length;++r){const a=c[r],{is:u,then:l,otherwise:d}=a,f=`match.${t}${n.switch?"."+r:""}`;if(u.$_match(s,i.nest(u,`${f}.is`),o)){if(l)return l.$_validate(e,i.nest(l,`${f}.then`),o)}else if(d)return d.$_validate(e,i.nest(d,`${f}.otherwise`),o)}}return l.errors(a,t)},rules:{conditional:{method(e,t){n(!this._flags._endedSwitch,"Unreachable condition"),n(!this._flags.match,"Cannot combine match mode",this._flags.match,"with conditional rule"),n(void 0===t.break,"Cannot use break option with alternatives conditional");const r=this.clone(),s=a.when(r,e,t),i=s.is?[s]:s.switch;for(const e of i)if(e.then&&e.otherwise){r.$_setFlag("_endedSwitch",!0,{clone:!1});break}return r.$_terms.matches.push(s),r.$_mutateRebuild()}},match:{method(e){if(n(["any","one","all"].includes(e),"Invalid alternatives match mode",e),"any"!==e)for(const t of this.$_terms.matches)n(t.schema,"Cannot combine match mode",e,"with conditional rules");return this.$_setFlag("match",e)}},try:{method(...e){n(e.length,"Missing alternative schemas"),o.verifyFlat(e,"try"),n(!this._flags._endedSwitch,"Unreachable condition");const t=this.clone();for(const r of e)t.$_terms.matches.push({schema:t.$_compile(r)});return t.$_mutateRebuild()}}},overrides:{label(e){return this.$_parent("label",e).$_modify({each:(t,r)=>"is"!==r.path[0]&&"string"!=typeof t._flags.label?t.label(e):void 0,ref:!1})}},rebuild(e){e.$_modify({each:t=>{o.isSchema(t)&&"array"===t.type&&e.$_setFlag("_arrayItems",!0,{clone:!1})}})},manifest:{build(e,t){if(t.matches)for(const r of t.matches){const{schema:t,ref:n,is:s,not:i,then:o,otherwise:a}=r;e=t?e.try(t):n?e.conditional(n,{is:s,then:o,not:i,otherwise:a,switch:r.switch}):e.conditional(s,{then:o,otherwise:a})}return e}},messages:{"alternatives.all":"{{#label}} does not match all of the required types","alternatives.any":"{{#label}} does not match any of the allowed types","alternatives.match":"{{#label}} does not match any of the allowed types","alternatives.one":"{{#label}} matches more than one allowed type","alternatives.types":"{{#label}} must be one of {{#types}}"}}),l.errors=function(e,{error:t,state:r}){if(!e.length)return{errors:t("alternatives.any")};if(1===e.length)return{errors:e[0].reports};const n=new Set,s=[];for(const{reports:i,schema:o}of e){if(i.length>1)return l.unmatched(e,t);const a=i[0];if(a instanceof c.Report==0)return l.unmatched(e,t);if(a.state.path.length!==r.path.length){s.push({type:o.type,report:a});continue}if("any.only"===a.code){for(const e of a.local.valids)n.add(e);continue}const[u,d]=a.code.split(".");"base"!==d?s.push({type:o.type,report:a}):"object.base"===a.code?n.add(a.local.type):n.add(u)}return s.length?1===s.length?{errors:s[0].report}:l.unmatched(e,t):{errors:t("alternatives.types",{types:[...n]})}},l.unmatched=function(e,t){const r=[];for(const t of e)r.push(...t.reports);return{errors:t("alternatives.match",c.details(r,{override:!1}))}}},8068:(e,t,r)=>{"use strict";const n=r(375),s=r(7629),i=r(8160),o=r(6914);e.exports=s.extend({type:"any",flags:{only:{default:!1}},terms:{alterations:{init:null},examples:{init:null},externals:{init:null},metas:{init:[]},notes:{init:[]},shared:{init:null},tags:{init:[]},whens:{init:null}},rules:{custom:{method(e,t){return n("function"==typeof e,"Method must be a function"),n(void 0===t||t&&"string"==typeof t,"Description must be a non-empty string"),this.$_addRule({name:"custom",args:{method:e,description:t}})},validate(e,t,{method:r}){try{return r(e,t)}catch(e){return t.error("any.custom",{error:e})}},args:["method","description"],multi:!0},messages:{method(e){return this.prefs({messages:e})}},shared:{method(e){n(i.isSchema(e)&&e._flags.id,"Schema must be a schema with an id");const t=this.clone();return t.$_terms.shared=t.$_terms.shared||[],t.$_terms.shared.push(e),t.$_mutateRegister(e),t}},warning:{method(e,t){return n(e&&"string"==typeof e,"Invalid warning code"),this.$_addRule({name:"warning",args:{code:e,local:t},warn:!0})},validate:(e,t,{code:r,local:n})=>t.error(r,n),args:["code","local"],multi:!0}},modifiers:{keep(e,t=!0){e.keep=t},message(e,t){e.message=o.compile(t)},warn(e,t=!0){e.warn=t}},manifest:{build(e,t){for(const r in t){const n=t[r];if(["examples","externals","metas","notes","tags"].includes(r))for(const t of n)e=e[r.slice(0,-1)](t);else if("alterations"!==r)if("whens"!==r){if("shared"===r)for(const t of n)e=e.shared(t)}else for(const t of n){const{ref:r,is:n,not:s,then:i,otherwise:o,concat:a}=t;e=a?e.concat(a):r?e.when(r,{is:n,not:s,then:i,otherwise:o,switch:t.switch,break:t.break}):e.when(n,{then:i,otherwise:o,break:t.break})}else{const t={};for(const{target:e,adjuster:r}of n)t[e]=r;e=e.alter(t)}}return e}},messages:{"any.custom":"{{#label}} failed custom validation because {{#error.message}}","any.default":"{{#label}} threw an error when running default method","any.failover":"{{#label}} threw an error when running failover method","any.invalid":"{{#label}} contains an invalid value","any.only":'{{#label}} must be {if(#valids.length == 1, "", "one of ")}{{#valids}}',"any.ref":"{{#label}} {{#arg}} references {{:#ref}} which {{#reason}}","any.required":"{{#label}} is required","any.unknown":"{{#label}} is not allowed"}})},546:(e,t,r)=>{"use strict";const n=r(375),s=r(9474),i=r(9621),o=r(8068),a=r(8160),c=r(3292),u={};e.exports=o.extend({type:"array",flags:{single:{default:!1},sparse:{default:!1}},terms:{items:{init:[],manifest:"schema"},ordered:{init:[],manifest:"schema"},_exclusions:{init:[]},_inclusions:{init:[]},_requireds:{init:[]}},coerce:{from:"object",method(e,{schema:t,state:r,prefs:n}){if(!Array.isArray(e))return;const s=t.$_getRule("sort");return s?u.sort(t,e,s.args.options,r,n):void 0}},validate(e,{schema:t,error:r}){if(!Array.isArray(e)){if(t._flags.single){const t=[e];return t[a.symbols.arraySingle]=!0,{value:t}}return{errors:r("array.base")}}if(t.$_getRule("items")||t.$_terms.externals)return{value:e.slice()}},rules:{has:{method(e){e=this.$_compile(e,{appendPath:!0});const t=this.$_addRule({name:"has",args:{schema:e}});return t.$_mutateRegister(e),t},validate(e,{state:t,prefs:r,error:n},{schema:s}){const i=[e,...t.ancestors];for(let n=0;n<e.length;++n){const o=t.localize([...t.path,n],i,s);if(s.$_match(e[n],o,r))return e}const o=s._flags.label;return o?n("array.hasKnown",{patternLabel:o}):n("array.hasUnknown",null)},multi:!0},items:{method(...e){a.verifyFlat(e,"items");const t=this.$_addRule("items");for(let r=0;r<e.length;++r){const n=a.tryWithPath((()=>this.$_compile(e[r])),r,{append:!0});t.$_terms.items.push(n)}return t.$_mutateRebuild()},validate(e,{schema:t,error:r,state:n,prefs:s,errorsArray:i}){const o=t.$_terms._requireds.slice(),c=t.$_terms.ordered.slice(),l=[...t.$_terms._inclusions,...o],d=!e[a.symbols.arraySingle];delete e[a.symbols.arraySingle];const f=i();let p=e.length;for(let i=0;i<p;++i){const a=e[i];let h=!1,m=!1;const g=d?i:new Number(i),y=[...n.path,g];if(!t._flags.sparse&&void 0===a){if(f.push(r("array.sparse",{key:g,path:y,pos:i,value:void 0},n.localize(y))),s.abortEarly)return f;c.shift();continue}const v=[e,...n.ancestors];for(const e of t.$_terms._exclusions)if(e.$_match(a,n.localize(y,v,e),s,{presence:"ignore"})){if(f.push(r("array.excludes",{pos:i,value:a},n.localize(y))),s.abortEarly)return f;h=!0,c.shift();break}if(h)continue;if(t.$_terms.ordered.length){if(c.length){const o=c.shift(),l=o.$_validate(a,n.localize(y,v,o),s);if(l.errors){if(f.push(...l.errors),s.abortEarly)return f}else if("strip"===o._flags.result)u.fastSplice(e,i),--i,--p;else{if(!t._flags.sparse&&void 0===l.value){if(f.push(r("array.sparse",{key:g,path:y,pos:i,value:void 0},n.localize(y))),s.abortEarly)return f;continue}e[i]=l.value}continue}if(!t.$_terms.items.length){if(f.push(r("array.orderedLength",{pos:i,limit:t.$_terms.ordered.length})),s.abortEarly)return f;break}}const b=[];let w=o.length;for(let c=0;c<w;++c){const l=n.localize(y,v,o[c]);l.snapshot();const d=o[c].$_validate(a,l,s);if(b[c]=d,!d.errors){if(l.commit(),e[i]=d.value,m=!0,u.fastSplice(o,c),--c,--w,!t._flags.sparse&&void 0===d.value&&(f.push(r("array.sparse",{key:g,path:y,pos:i,value:void 0},n.localize(y))),s.abortEarly))return f;break}l.restore()}if(m)continue;const _=s.stripUnknown&&!!s.stripUnknown.arrays||!1;w=l.length;for(const c of l){let l;const d=o.indexOf(c);if(-1!==d)l=b[d];else{const o=n.localize(y,v,c);if(o.snapshot(),l=c.$_validate(a,o,s),!l.errors){o.commit(),"strip"===c._flags.result?(u.fastSplice(e,i),--i,--p):t._flags.sparse||void 0!==l.value?e[i]=l.value:(f.push(r("array.sparse",{key:g,path:y,pos:i,value:void 0},n.localize(y))),h=!0),m=!0;break}o.restore()}if(1===w){if(_){u.fastSplice(e,i),--i,--p,m=!0;break}if(f.push(...l.errors),s.abortEarly)return f;h=!0;break}}if(!h&&(t.$_terms._inclusions.length||t.$_terms._requireds.length)&&!m){if(_){u.fastSplice(e,i),--i,--p;continue}if(f.push(r("array.includes",{pos:i,value:a},n.localize(y))),s.abortEarly)return f}}return o.length&&u.fillMissedErrors(t,f,o,e,n,s),c.length&&(u.fillOrderedErrors(t,f,c,e,n,s),f.length||u.fillDefault(c,e,n,s)),f.length?f:e},priority:!0,manifest:!1},length:{method(e){return this.$_addRule({name:"length",args:{limit:e},operator:"="})},validate:(e,t,{limit:r},{name:n,operator:s,args:i})=>a.compare(e.length,r,s)?e:t.error("array."+n,{limit:i.limit,value:e}),args:[{name:"limit",ref:!0,assert:a.limit,message:"must be a positive integer"}]},max:{method(e){return this.$_addRule({name:"max",method:"length",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"length",args:{limit:e},operator:">="})}},ordered:{method(...e){a.verifyFlat(e,"ordered");const t=this.$_addRule("items");for(let r=0;r<e.length;++r){const n=a.tryWithPath((()=>this.$_compile(e[r])),r,{append:!0});u.validateSingle(n,t),t.$_mutateRegister(n),t.$_terms.ordered.push(n)}return t.$_mutateRebuild()}},single:{method(e){const t=void 0===e||!!e;return n(!t||!this._flags._arrayItems,"Cannot specify single rule when array has array items"),this.$_setFlag("single",t)}},sort:{method(e={}){a.assertOptions(e,["by","order"]);const t={order:e.order||"ascending"};return e.by&&(t.by=c.ref(e.by,{ancestor:0}),n(!t.by.ancestor,"Cannot sort by ancestor")),this.$_addRule({name:"sort",args:{options:t}})},validate(e,{error:t,state:r,prefs:n,schema:s},{options:i}){const{value:o,errors:a}=u.sort(s,e,i,r,n);if(a)return a;for(let r=0;r<e.length;++r)if(e[r]!==o[r])return t("array.sort",{order:i.order,by:i.by?i.by.key:"value"});return e},convert:!0},sparse:{method(e){const t=void 0===e||!!e;return this._flags.sparse===t?this:(t?this.clone():this.$_addRule("items")).$_setFlag("sparse",t,{clone:!1})}},unique:{method(e,t={}){n(!e||"function"==typeof e||"string"==typeof e,"comparator must be a function or a string"),a.assertOptions(t,["ignoreUndefined","separator"]);const r={name:"unique",args:{options:t,comparator:e}};if(e)if("string"==typeof e){const n=a.default(t.separator,".");r.path=n?e.split(n):[e]}else r.comparator=e;return this.$_addRule(r)},validate(e,{state:t,error:r,schema:o},{comparator:a,options:c},{comparator:u,path:l}){const d={string:Object.create(null),number:Object.create(null),undefined:Object.create(null),boolean:Object.create(null),bigint:Object.create(null),object:new Map,function:new Map,custom:new Map},f=u||s,p=c.ignoreUndefined;for(let s=0;s<e.length;++s){const o=l?i(e[s],l):e[s],c=u?d.custom:d[typeof o];if(n(c,"Failed to find unique map container for type",typeof o),c instanceof Map){const n=c.entries();let i;for(;!(i=n.next()).done;)if(f(i.value[0],o)){const n=t.localize([...t.path,s],[e,...t.ancestors]),o={pos:s,value:e[s],dupePos:i.value[1],dupeValue:e[i.value[1]]};return l&&(o.path=a),r("array.unique",o,n)}c.set(o,s)}else{if((!p||void 0!==o)&&void 0!==c[o]){const n={pos:s,value:e[s],dupePos:c[o],dupeValue:e[c[o]]};return l&&(n.path=a),r("array.unique",n,t.localize([...t.path,s],[e,...t.ancestors]))}c[o]=s}}return e},args:["comparator","options"],multi:!0}},cast:{set:{from:Array.isArray,to:(e,t)=>new Set(e)}},rebuild(e){e.$_terms._inclusions=[],e.$_terms._exclusions=[],e.$_terms._requireds=[];for(const t of e.$_terms.items)u.validateSingle(t,e),"required"===t._flags.presence?e.$_terms._requireds.push(t):"forbidden"===t._flags.presence?e.$_terms._exclusions.push(t):e.$_terms._inclusions.push(t);for(const t of e.$_terms.ordered)u.validateSingle(t,e)},manifest:{build:(e,t)=>(t.items&&(e=e.items(...t.items)),t.ordered&&(e=e.ordered(...t.ordered)),e)},messages:{"array.base":"{{#label}} must be an array","array.excludes":"{{#label}} contains an excluded value","array.hasKnown":"{{#label}} does not contain at least one required match for type {:#patternLabel}","array.hasUnknown":"{{#label}} does not contain at least one required match","array.includes":"{{#label}} does not match any of the allowed types","array.includesRequiredBoth":"{{#label}} does not contain {{#knownMisses}} and {{#unknownMisses}} other required value(s)","array.includesRequiredKnowns":"{{#label}} does not contain {{#knownMisses}}","array.includesRequiredUnknowns":"{{#label}} does not contain {{#unknownMisses}} required value(s)","array.length":"{{#label}} must contain {{#limit}} items","array.max":"{{#label}} must contain less than or equal to {{#limit}} items","array.min":"{{#label}} must contain at least {{#limit}} items","array.orderedLength":"{{#label}} must contain at most {{#limit}} items","array.sort":"{{#label}} must be sorted in {#order} order by {{#by}}","array.sort.mismatching":"{{#label}} cannot be sorted due to mismatching types","array.sort.unsupported":"{{#label}} cannot be sorted due to unsupported type {#type}","array.sparse":"{{#label}} must not be a sparse array item","array.unique":"{{#label}} contains a duplicate value"}}),u.fillMissedErrors=function(e,t,r,n,s,i){const o=[];let a=0;for(const e of r){const t=e._flags.label;t?o.push(t):++a}o.length?a?t.push(e.$_createError("array.includesRequiredBoth",n,{knownMisses:o,unknownMisses:a},s,i)):t.push(e.$_createError("array.includesRequiredKnowns",n,{knownMisses:o},s,i)):t.push(e.$_createError("array.includesRequiredUnknowns",n,{unknownMisses:a},s,i))},u.fillOrderedErrors=function(e,t,r,n,s,i){const o=[];for(const e of r)"required"===e._flags.presence&&o.push(e);o.length&&u.fillMissedErrors(e,t,o,n,s,i)},u.fillDefault=function(e,t,r,n){const s=[];let i=!0;for(let o=e.length-1;o>=0;--o){const a=e[o],c=[t,...r.ancestors],u=a.$_validate(void 0,r.localize(r.path,c,a),n).value;if(i){if(void 0===u)continue;i=!1}s.unshift(u)}s.length&&t.push(...s)},u.fastSplice=function(e,t){let r=t;for(;r<e.length;)e[r++]=e[r];--e.length},u.validateSingle=function(e,t){("array"===e.type||e._flags._arrayItems)&&(n(!t._flags.single,"Cannot specify array item with single rule enabled"),t.$_setFlag("_arrayItems",!0,{clone:!1}))},u.sort=function(e,t,r,n,s){const i="ascending"===r.order?1:-1,o=-1*i,a=i,c=(c,l)=>{let d=u.compare(c,l,o,a);if(null!==d)return d;if(r.by&&(c=r.by.resolve(c,n,s),l=r.by.resolve(l,n,s)),d=u.compare(c,l,o,a),null!==d)return d;const f=typeof c;if(f!==typeof l)throw e.$_createError("array.sort.mismatching",t,null,n,s);if("number"!==f&&"string"!==f)throw e.$_createError("array.sort.unsupported",t,{type:f},n,s);return"number"===f?(c-l)*i:c<l?o:a};try{return{value:t.slice().sort(c)}}catch(e){return{errors:e}}},u.compare=function(e,t,r,n){return e===t?0:void 0===e?1:void 0===t?-1:null===e?n:null===t?r:null}},4937:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),i=r(8160),o=r(2036),a={isBool:function(e){return"boolean"==typeof e}};e.exports=s.extend({type:"boolean",flags:{sensitive:{default:!1}},terms:{falsy:{init:null,manifest:"values"},truthy:{init:null,manifest:"values"}},coerce(e,{schema:t}){if("boolean"!=typeof e){if("string"==typeof e){const r=t._flags.sensitive?e:e.toLowerCase();e="true"===r||"false"!==r&&e}return"boolean"!=typeof e&&(e=t.$_terms.truthy&&t.$_terms.truthy.has(e,null,null,!t._flags.sensitive)||(!t.$_terms.falsy||!t.$_terms.falsy.has(e,null,null,!t._flags.sensitive))&&e),{value:e}}},validate(e,{error:t}){if("boolean"!=typeof e)return{value:e,errors:t("boolean.base")}},rules:{truthy:{method(...e){i.verifyFlat(e,"truthy");const t=this.clone();t.$_terms.truthy=t.$_terms.truthy||new o;for(let r=0;r<e.length;++r){const s=e[r];n(void 0!==s,"Cannot call truthy with undefined"),t.$_terms.truthy.add(s)}return t}},falsy:{method(...e){i.verifyFlat(e,"falsy");const t=this.clone();t.$_terms.falsy=t.$_terms.falsy||new o;for(let r=0;r<e.length;++r){const s=e[r];n(void 0!==s,"Cannot call falsy with undefined"),t.$_terms.falsy.add(s)}return t}},sensitive:{method(e=!0){return this.$_setFlag("sensitive",e)}}},cast:{number:{from:a.isBool,to:(e,t)=>e?1:0},string:{from:a.isBool,to:(e,t)=>e?"true":"false"}},manifest:{build:(e,t)=>(t.truthy&&(e=e.truthy(...t.truthy)),t.falsy&&(e=e.falsy(...t.falsy)),e)},messages:{"boolean.base":"{{#label}} must be a boolean"}})},7500:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),i=r(8160),o=r(3328),a={isDate:function(e){return e instanceof Date}};e.exports=s.extend({type:"date",coerce:{from:["number","string"],method:(e,{schema:t})=>({value:a.parse(e,t._flags.format)||e})},validate(e,{schema:t,error:r,prefs:n}){if(e instanceof Date&&!isNaN(e.getTime()))return;const s=t._flags.format;return n.convert&&s&&"string"==typeof e?{value:e,errors:r("date.format",{format:s})}:{value:e,errors:r("date.base")}},rules:{compare:{method:!1,validate(e,t,{date:r},{name:n,operator:s,args:o}){const a="now"===r?Date.now():r.getTime();return i.compare(e.getTime(),a,s)?e:t.error("date."+n,{limit:o.date,value:e})},args:[{name:"date",ref:!0,normalize:e=>"now"===e?e:a.parse(e),assert:e=>null!==e,message:"must have a valid date format"}]},format:{method(e){return n(["iso","javascript","unix"].includes(e),"Unknown date format",e),this.$_setFlag("format",e)}},greater:{method(e){return this.$_addRule({name:"greater",method:"compare",args:{date:e},operator:">"})}},iso:{method(){return this.format("iso")}},less:{method(e){return this.$_addRule({name:"less",method:"compare",args:{date:e},operator:"<"})}},max:{method(e){return this.$_addRule({name:"max",method:"compare",args:{date:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"compare",args:{date:e},operator:">="})}},timestamp:{method(e="javascript"){return n(["javascript","unix"].includes(e),'"type" must be one of "javascript, unix"'),this.format(e)}}},cast:{number:{from:a.isDate,to:(e,t)=>e.getTime()},string:{from:a.isDate,to:(e,{prefs:t})=>o.date(e,t)}},messages:{"date.base":"{{#label}} must be a valid date","date.format":'{{#label}} must be in {msg("date.format." + #format) || #format} format',"date.greater":"{{#label}} must be greater than {{:#limit}}","date.less":"{{#label}} must be less than {{:#limit}}","date.max":"{{#label}} must be less than or equal to {{:#limit}}","date.min":"{{#label}} must be greater than or equal to {{:#limit}}","date.format.iso":"ISO 8601 date","date.format.javascript":"timestamp or number of milliseconds","date.format.unix":"timestamp or number of seconds"}}),a.parse=function(e,t){if(e instanceof Date)return e;if("string"!=typeof e&&(isNaN(e)||!isFinite(e)))return null;if(/^\s*$/.test(e))return null;if("iso"===t)return i.isIsoDate(e)?a.date(e.toString()):null;const r=e;if("string"==typeof e&&/^[+-]?\d+(\.\d+)?$/.test(e)&&(e=parseFloat(e)),t){if("javascript"===t)return a.date(1*e);if("unix"===t)return a.date(1e3*e);if("string"==typeof r)return null}return a.date(e)},a.date=function(e){const t=new Date(e);return isNaN(t.getTime())?null:t}},390:(e,t,r)=>{"use strict";const n=r(375),s=r(7824);e.exports=s.extend({type:"function",properties:{typeof:"function"},rules:{arity:{method(e){return n(Number.isSafeInteger(e)&&e>=0,"n must be a positive integer"),this.$_addRule({name:"arity",args:{n:e}})},validate:(e,t,{n:r})=>e.length===r?e:t.error("function.arity",{n:r})},class:{method(){return this.$_addRule("class")},validate:(e,t)=>/^\s*class\s/.test(e.toString())?e:t.error("function.class",{value:e})},minArity:{method(e){return n(Number.isSafeInteger(e)&&e>0,"n must be a strict positive integer"),this.$_addRule({name:"minArity",args:{n:e}})},validate:(e,t,{n:r})=>e.length>=r?e:t.error("function.minArity",{n:r})},maxArity:{method(e){return n(Number.isSafeInteger(e)&&e>=0,"n must be a positive integer"),this.$_addRule({name:"maxArity",args:{n:e}})},validate:(e,t,{n:r})=>e.length<=r?e:t.error("function.maxArity",{n:r})}},messages:{"function.arity":"{{#label}} must have an arity of {{#n}}","function.class":"{{#label}} must be a class","function.maxArity":"{{#label}} must have an arity lesser or equal to {{#n}}","function.minArity":"{{#label}} must have an arity greater or equal to {{#n}}"}})},7824:(e,t,r)=>{"use strict";const n=r(978),s=r(375),i=r(8571),o=r(3652),a=r(8068),c=r(8160),u=r(3292),l=r(6354),d=r(6133),f=r(3328),p={renameDefaults:{alias:!1,multiple:!1,override:!1}};e.exports=a.extend({type:"_keys",properties:{typeof:"object"},flags:{unknown:{default:void 0}},terms:{dependencies:{init:null},keys:{init:null,manifest:{mapped:{from:"schema",to:"key"}}},patterns:{init:null},renames:{init:null}},args:(e,t)=>e.keys(t),validate(e,{schema:t,error:r,state:n,prefs:s}){if(!e||typeof e!==t.$_property("typeof")||Array.isArray(e))return{value:e,errors:r("object.base",{type:t.$_property("typeof")})};if(!(t.$_terms.renames||t.$_terms.dependencies||t.$_terms.keys||t.$_terms.patterns||t.$_terms.externals))return;e=p.clone(e,s);const i=[];if(t.$_terms.renames&&!p.rename(t,e,n,s,i))return{value:e,errors:i};if(!t.$_terms.keys&&!t.$_terms.patterns&&!t.$_terms.dependencies)return{value:e,errors:i};const o=new Set(Object.keys(e));if(t.$_terms.keys){const r=[e,...n.ancestors];for(const a of t.$_terms.keys){const t=a.key,c=e[t];o.delete(t);const u=n.localize([...n.path,t],r,a),l=a.schema.$_validate(c,u,s);if(l.errors){if(s.abortEarly)return{value:e,errors:l.errors};void 0!==l.value&&(e[t]=l.value),i.push(...l.errors)}else"strip"===a.schema._flags.result||void 0===l.value&&void 0!==c?delete e[t]:void 0!==l.value&&(e[t]=l.value)}}if(o.size||t._flags._hasPatternMatch){const r=p.unknown(t,e,o,i,n,s);if(r)return r}if(t.$_terms.dependencies)for(const r of t.$_terms.dependencies){if(null!==r.key&&!1===p.isPresent(r.options)(r.key.resolve(e,n,s,null,{shadow:!1})))continue;const o=p.dependencies[r.rel](t,r,e,n,s);if(o){const r=t.$_createError(o.code,e,o.context,n,s);if(s.abortEarly)return{value:e,errors:r};i.push(r)}}return{value:e,errors:i}},rules:{and:{method(...e){return c.verifyFlat(e,"and"),p.dependency(this,"and",null,e)}},append:{method(e){return null==e||0===Object.keys(e).length?this:this.keys(e)}},assert:{method(e,t,r){f.isTemplate(e)||(e=u.ref(e)),s(void 0===r||"string"==typeof r,"Message must be a string"),t=this.$_compile(t,{appendPath:!0});const n=this.$_addRule({name:"assert",args:{subject:e,schema:t,message:r}});return n.$_mutateRegister(e),n.$_mutateRegister(t),n},validate(e,{error:t,prefs:r,state:n},{subject:s,schema:i,message:o}){const a=s.resolve(e,n,r),c=d.isRef(s)?s.absolute(n):[];return i.$_match(a,n.localize(c,[e,...n.ancestors],i),r)?e:t("object.assert",{subject:s,message:o})},args:["subject","schema","message"],multi:!0},instance:{method(e,t){return s("function"==typeof e,"constructor must be a function"),t=t||e.name,this.$_addRule({name:"instance",args:{constructor:e,name:t}})},validate:(e,t,{constructor:r,name:n})=>e instanceof r?e:t.error("object.instance",{type:n,value:e}),args:["constructor","name"]},keys:{method(e){s(void 0===e||"object"==typeof e,"Object schema must be a valid object"),s(!c.isSchema(e),"Object schema cannot be a joi schema");const t=this.clone();if(e)if(Object.keys(e).length){t.$_terms.keys=t.$_terms.keys?t.$_terms.keys.filter((t=>!e.hasOwnProperty(t.key))):new p.Keys;for(const r in e)c.tryWithPath((()=>t.$_terms.keys.push({key:r,schema:this.$_compile(e[r])})),r)}else t.$_terms.keys=new p.Keys;else t.$_terms.keys=null;return t.$_mutateRebuild()}},length:{method(e){return this.$_addRule({name:"length",args:{limit:e},operator:"="})},validate:(e,t,{limit:r},{name:n,operator:s,args:i})=>c.compare(Object.keys(e).length,r,s)?e:t.error("object."+n,{limit:i.limit,value:e}),args:[{name:"limit",ref:!0,assert:c.limit,message:"must be a positive integer"}]},max:{method(e){return this.$_addRule({name:"max",method:"length",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"length",args:{limit:e},operator:">="})}},nand:{method(...e){return c.verifyFlat(e,"nand"),p.dependency(this,"nand",null,e)}},or:{method(...e){return c.verifyFlat(e,"or"),p.dependency(this,"or",null,e)}},oxor:{method(...e){return p.dependency(this,"oxor",null,e)}},pattern:{method(e,t,r={}){const n=e instanceof RegExp;n||(e=this.$_compile(e,{appendPath:!0})),s(void 0!==t,"Invalid rule"),c.assertOptions(r,["fallthrough","matches"]),n&&s(!e.flags.includes("g")&&!e.flags.includes("y"),"pattern should not use global or sticky mode"),t=this.$_compile(t,{appendPath:!0});const i=this.clone();i.$_terms.patterns=i.$_terms.patterns||[];const o={[n?"regex":"schema"]:e,rule:t};return r.matches&&(o.matches=this.$_compile(r.matches),"array"!==o.matches.type&&(o.matches=o.matches.$_root.array().items(o.matches)),i.$_mutateRegister(o.matches),i.$_setFlag("_hasPatternMatch",!0,{clone:!1})),r.fallthrough&&(o.fallthrough=!0),i.$_terms.patterns.push(o),i.$_mutateRegister(t),i}},ref:{method(){return this.$_addRule("ref")},validate:(e,t)=>d.isRef(e)?e:t.error("object.refType",{value:e})},regex:{method(){return this.$_addRule("regex")},validate:(e,t)=>e instanceof RegExp?e:t.error("object.regex",{value:e})},rename:{method(e,t,r={}){s("string"==typeof e||e instanceof RegExp,"Rename missing the from argument"),s("string"==typeof t||t instanceof f,"Invalid rename to argument"),s(t!==e,"Cannot rename key to same name:",e),c.assertOptions(r,["alias","ignoreUndefined","override","multiple"]);const i=this.clone();i.$_terms.renames=i.$_terms.renames||[];for(const t of i.$_terms.renames)s(t.from!==e,"Cannot rename the same key multiple times");return t instanceof f&&i.$_mutateRegister(t),i.$_terms.renames.push({from:e,to:t,options:n(p.renameDefaults,r)}),i}},schema:{method(e="any"){return this.$_addRule({name:"schema",args:{type:e}})},validate:(e,t,{type:r})=>!c.isSchema(e)||"any"!==r&&e.type!==r?t.error("object.schema",{type:r}):e},unknown:{method(e){return this.$_setFlag("unknown",!1!==e)}},with:{method(e,t,r={}){return p.dependency(this,"with",e,t,r)}},without:{method(e,t,r={}){return p.dependency(this,"without",e,t,r)}},xor:{method(...e){return c.verifyFlat(e,"xor"),p.dependency(this,"xor",null,e)}}},overrides:{default(e,t){return void 0===e&&(e=c.symbols.deepDefault),this.$_parent("default",e,t)}},rebuild(e){if(e.$_terms.keys){const t=new o.Sorter;for(const r of e.$_terms.keys)c.tryWithPath((()=>t.add(r,{after:r.schema.$_rootReferences(),group:r.key})),r.key);e.$_terms.keys=new p.Keys(...t.nodes)}},manifest:{build(e,t){if(t.keys&&(e=e.keys(t.keys)),t.dependencies)for(const{rel:r,key:n=null,peers:s,options:i}of t.dependencies)e=p.dependency(e,r,n,s,i);if(t.patterns)for(const{regex:r,schema:n,rule:s,fallthrough:i,matches:o}of t.patterns)e=e.pattern(r||n,s,{fallthrough:i,matches:o});if(t.renames)for(const{from:r,to:n,options:s}of t.renames)e=e.rename(r,n,s);return e}},messages:{"object.and":"{{#label}} contains {{#presentWithLabels}} without its required peers {{#missingWithLabels}}","object.assert":'{{#label}} is invalid because {if(#subject.key, `"` + #subject.key + `" failed to ` + (#message || "pass the assertion test"), #message || "the assertion failed")}',"object.base":"{{#label}} must be of type {{#type}}","object.instance":"{{#label}} must be an instance of {{:#type}}","object.length":'{{#label}} must have {{#limit}} key{if(#limit == 1, "", "s")}',"object.max":'{{#label}} must have less than or equal to {{#limit}} key{if(#limit == 1, "", "s")}',"object.min":'{{#label}} must have at least {{#limit}} key{if(#limit == 1, "", "s")}',"object.missing":"{{#label}} must contain at least one of {{#peersWithLabels}}","object.nand":"{{:#mainWithLabel}} must not exist simultaneously with {{#peersWithLabels}}","object.oxor":"{{#label}} contains a conflict between optional exclusive peers {{#peersWithLabels}}","object.pattern.match":"{{#label}} keys failed to match pattern requirements","object.refType":"{{#label}} must be a Joi reference","object.regex":"{{#label}} must be a RegExp object","object.rename.multiple":"{{#label}} cannot rename {{:#from}} because multiple renames are disabled and another key was already renamed to {{:#to}}","object.rename.override":"{{#label}} cannot rename {{:#from}} because override is disabled and target {{:#to}} exists","object.schema":"{{#label}} must be a Joi schema of {{#type}} type","object.unknown":"{{#label}} is not allowed","object.with":"{{:#mainWithLabel}} missing required peer {{:#peerWithLabel}}","object.without":"{{:#mainWithLabel}} conflict with forbidden peer {{:#peerWithLabel}}","object.xor":"{{#label}} contains a conflict between exclusive peers {{#peersWithLabels}}"}}),p.clone=function(e,t){if("object"==typeof e){if(t.nonEnumerables)return i(e,{shallow:!0});const r=Object.create(Object.getPrototypeOf(e));return Object.assign(r,e),r}const r=function(...t){return e.apply(this,t)};return r.prototype=i(e.prototype),Object.defineProperty(r,"name",{value:e.name,writable:!1}),Object.defineProperty(r,"length",{value:e.length,writable:!1}),Object.assign(r,e),r},p.dependency=function(e,t,r,n,i){s(null===r||"string"==typeof r,t,"key must be a strings"),i||(i=n.length>1&&"object"==typeof n[n.length-1]?n.pop():{}),c.assertOptions(i,["separator","isPresent"]),n=[].concat(n);const o=c.default(i.separator,"."),a=[];for(const e of n)s("string"==typeof e,t,"peers must be strings"),a.push(u.ref(e,{separator:o,ancestor:0,prefix:!1}));null!==r&&(r=u.ref(r,{separator:o,ancestor:0,prefix:!1}));const l=e.clone();return l.$_terms.dependencies=l.$_terms.dependencies||[],l.$_terms.dependencies.push(new p.Dependency(t,r,a,n,i)),l},p.dependencies={and(e,t,r,n,s){const i=[],o=[],a=t.peers.length,c=p.isPresent(t.options);for(const e of t.peers)!1===c(e.resolve(r,n,s,null,{shadow:!1}))?i.push(e.key):o.push(e.key);if(i.length!==a&&o.length!==a)return{code:"object.and",context:{present:o,presentWithLabels:p.keysToLabels(e,o),missing:i,missingWithLabels:p.keysToLabels(e,i)}}},nand(e,t,r,n,s){const i=[],o=p.isPresent(t.options);for(const e of t.peers)o(e.resolve(r,n,s,null,{shadow:!1}))&&i.push(e.key);if(i.length!==t.peers.length)return;const a=t.paths[0],c=t.paths.slice(1);return{code:"object.nand",context:{main:a,mainWithLabel:p.keysToLabels(e,a),peers:c,peersWithLabels:p.keysToLabels(e,c)}}},or(e,t,r,n,s){const i=p.isPresent(t.options);for(const e of t.peers)if(i(e.resolve(r,n,s,null,{shadow:!1})))return;return{code:"object.missing",context:{peers:t.paths,peersWithLabels:p.keysToLabels(e,t.paths)}}},oxor(e,t,r,n,s){const i=[],o=p.isPresent(t.options);for(const e of t.peers)o(e.resolve(r,n,s,null,{shadow:!1}))&&i.push(e.key);if(!i.length||1===i.length)return;const a={peers:t.paths,peersWithLabels:p.keysToLabels(e,t.paths)};return a.present=i,a.presentWithLabels=p.keysToLabels(e,i),{code:"object.oxor",context:a}},with(e,t,r,n,s){const i=p.isPresent(t.options);for(const o of t.peers)if(!1===i(o.resolve(r,n,s,null,{shadow:!1})))return{code:"object.with",context:{main:t.key.key,mainWithLabel:p.keysToLabels(e,t.key.key),peer:o.key,peerWithLabel:p.keysToLabels(e,o.key)}}},without(e,t,r,n,s){const i=p.isPresent(t.options);for(const o of t.peers)if(i(o.resolve(r,n,s,null,{shadow:!1})))return{code:"object.without",context:{main:t.key.key,mainWithLabel:p.keysToLabels(e,t.key.key),peer:o.key,peerWithLabel:p.keysToLabels(e,o.key)}}},xor(e,t,r,n,s){const i=[],o=p.isPresent(t.options);for(const e of t.peers)o(e.resolve(r,n,s,null,{shadow:!1}))&&i.push(e.key);if(1===i.length)return;const a={peers:t.paths,peersWithLabels:p.keysToLabels(e,t.paths)};return 0===i.length?{code:"object.missing",context:a}:(a.present=i,a.presentWithLabels=p.keysToLabels(e,i),{code:"object.xor",context:a})}},p.keysToLabels=function(e,t){return Array.isArray(t)?t.map((t=>e.$_mapLabels(t))):e.$_mapLabels(t)},p.isPresent=function(e){return"function"==typeof e.isPresent?e.isPresent:e=>void 0!==e},p.rename=function(e,t,r,n,s){const i={};for(const o of e.$_terms.renames){const a=[],c="string"!=typeof o.from;if(c)for(const e in t){if(void 0===t[e]&&o.options.ignoreUndefined)continue;if(e===o.to)continue;const r=o.from.exec(e);r&&a.push({from:e,to:o.to,match:r})}else!Object.prototype.hasOwnProperty.call(t,o.from)||void 0===t[o.from]&&o.options.ignoreUndefined||a.push(o);for(const u of a){const a=u.from;let l=u.to;if(l instanceof f&&(l=l.render(t,r,n,u.match)),a!==l){if(!o.options.multiple&&i[l]&&(s.push(e.$_createError("object.rename.multiple",t,{from:a,to:l,pattern:c},r,n)),n.abortEarly))return!1;if(Object.prototype.hasOwnProperty.call(t,l)&&!o.options.override&&!i[l]&&(s.push(e.$_createError("object.rename.override",t,{from:a,to:l,pattern:c},r,n)),n.abortEarly))return!1;void 0===t[a]?delete t[l]:t[l]=t[a],i[l]=!0,o.options.alias||delete t[a]}}}return!0},p.unknown=function(e,t,r,n,s,i){if(e.$_terms.patterns){let o=!1;const a=e.$_terms.patterns.map((e=>{if(e.matches)return o=!0,[]})),c=[t,...s.ancestors];for(const o of r){const u=t[o],l=[...s.path,o];for(let d=0;d<e.$_terms.patterns.length;++d){const f=e.$_terms.patterns[d];if(f.regex){const e=f.regex.test(o);if(s.mainstay.tracer.debug(s,"rule",`pattern.${d}`,e?"pass":"error"),!e)continue}else if(!f.schema.$_match(o,s.nest(f.schema,`pattern.${d}`),i))continue;r.delete(o);const p=s.localize(l,c,{schema:f.rule,key:o}),h=f.rule.$_validate(u,p,i);if(h.errors){if(i.abortEarly)return{value:t,errors:h.errors};n.push(...h.errors)}if(f.matches&&a[d].push(o),t[o]=h.value,!f.fallthrough)break}}if(o)for(let r=0;r<a.length;++r){const o=a[r];if(!o)continue;const u=e.$_terms.patterns[r].matches,d=s.localize(s.path,c,u),f=u.$_validate(o,d,i);if(f.errors){const r=l.details(f.errors,{override:!1});r.matches=o;const a=e.$_createError("object.pattern.match",t,r,s,i);if(i.abortEarly)return{value:t,errors:a};n.push(a)}}}if(r.size&&(e.$_terms.keys||e.$_terms.patterns)){if(i.stripUnknown&&void 0===e._flags.unknown||i.skipFunctions){const e=!(!i.stripUnknown||!0!==i.stripUnknown&&!i.stripUnknown.objects);for(const n of r)e?(delete t[n],r.delete(n)):"function"==typeof t[n]&&r.delete(n)}if(!c.default(e._flags.unknown,i.allowUnknown))for(const o of r){const r=s.localize([...s.path,o],[]),a=e.$_createError("object.unknown",t[o],{child:o},r,i,{flags:!1});if(i.abortEarly)return{value:t,errors:a};n.push(a)}}},p.Dependency=class{constructor(e,t,r,n,s){this.rel=e,this.key=t,this.peers=r,this.paths=n,this.options=s}describe(){const e={rel:this.rel,peers:this.paths};return null!==this.key&&(e.key=this.key.key),"."!==this.peers[0].separator&&(e.options={...e.options,separator:this.peers[0].separator}),this.options.isPresent&&(e.options={...e.options,isPresent:this.options.isPresent}),e}},p.Keys=class extends Array{concat(e){const t=this.slice(),r=new Map;for(let e=0;e<t.length;++e)r.set(t[e].key,e);for(const n of e){const e=n.key,s=r.get(e);void 0!==s?t[s]={key:e,schema:t[s].schema.concat(n.schema)}:t.push(n)}return t}}},8785:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),i=r(8160),o=r(3292),a=r(6354),c={};e.exports=s.extend({type:"link",properties:{schemaChain:!0},terms:{link:{init:null,manifest:"single",register:!1}},args:(e,t)=>e.ref(t),validate(e,{schema:t,state:r,prefs:s}){n(t.$_terms.link,"Uninitialized link schema");const i=c.generate(t,e,r,s),o=t.$_terms.link[0].ref;return i.$_validate(e,r.nest(i,`link:${o.display}:${i.type}`),s)},generate:(e,t,r,n)=>c.generate(e,t,r,n),rules:{ref:{method(e){n(!this.$_terms.link,"Cannot reinitialize schema"),e=o.ref(e),n("value"===e.type||"local"===e.type,"Invalid reference type:",e.type),n("local"===e.type||"root"===e.ancestor||e.ancestor>0,"Link cannot reference itself");const t=this.clone();return t.$_terms.link=[{ref:e}],t}},relative:{method(e=!0){return this.$_setFlag("relative",e)}}},overrides:{concat(e){n(this.$_terms.link,"Uninitialized link schema"),n(i.isSchema(e),"Invalid schema object"),n("link"!==e.type,"Cannot merge type link with another link");const t=this.clone();return t.$_terms.whens||(t.$_terms.whens=[]),t.$_terms.whens.push({concat:e}),t.$_mutateRebuild()}},manifest:{build:(e,t)=>(n(t.link,"Invalid link description missing link"),e.ref(t.link))}}),c.generate=function(e,t,r,n){let s=r.mainstay.links.get(e);if(s)return s._generate(t,r,n).schema;const i=e.$_terms.link[0].ref,{perspective:o,path:a}=c.perspective(i,r);c.assert(o,"which is outside of schema boundaries",i,e,r,n);try{s=a.length?o.$_reach(a):o}catch(t){c.assert(!1,"to non-existing schema",i,e,r,n)}return c.assert("link"!==s.type,"which is another link",i,e,r,n),e._flags.relative||r.mainstay.links.set(e,s),s._generate(t,r,n).schema},c.perspective=function(e,t){if("local"===e.type){for(const{schema:r,key:n}of t.schemas){if((r._flags.id||n)===e.path[0])return{perspective:r,path:e.path.slice(1)};if(r.$_terms.shared)for(const t of r.$_terms.shared)if(t._flags.id===e.path[0])return{perspective:t,path:e.path.slice(1)}}return{perspective:null,path:null}}return"root"===e.ancestor?{perspective:t.schemas[t.schemas.length-1].schema,path:e.path}:{perspective:t.schemas[e.ancestor]&&t.schemas[e.ancestor].schema,path:e.path}},c.assert=function(e,t,r,s,i,o){e||n(!1,`"${a.label(s._flags,i,o)}" contains link reference "${r.display}" ${t}`)}},3832:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),i=r(8160),o={numberRx:/^\s*[+-]?(?:(?:\d+(?:\.\d*)?)|(?:\.\d+))(?:e([+-]?\d+))?\s*$/i,precisionRx:/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/,exponentialPartRegex:/[eE][+-]?\d+$/,leadingSignAndZerosRegex:/^[+-]?(0*)?/,dotRegex:/\./,trailingZerosRegex:/0+$/,decimalPlaces(e){const t=e.toString(),r=t.indexOf("."),n=t.indexOf("e");return(r<0?0:(n<0?t.length:n)-r-1)+(n<0?0:Math.max(0,-parseInt(t.slice(n+1))))}};e.exports=s.extend({type:"number",flags:{unsafe:{default:!1}},coerce:{from:"string",method(e,{schema:t,error:r}){if(!e.match(o.numberRx))return;e=e.trim();const n={value:parseFloat(e)};if(0===n.value&&(n.value=0),!t._flags.unsafe)if(e.match(/e/i)){if(o.extractSignificantDigits(e)!==o.extractSignificantDigits(String(n.value)))return n.errors=r("number.unsafe"),n}else{const t=n.value.toString();if(t.match(/e/i))return n;if(t!==o.normalizeDecimal(e))return n.errors=r("number.unsafe"),n}return n}},validate(e,{schema:t,error:r,prefs:n}){if(e===1/0||e===-1/0)return{value:e,errors:r("number.infinity")};if(!i.isNumber(e))return{value:e,errors:r("number.base")};const s={value:e};if(n.convert){const e=t.$_getRule("precision");if(e){const t=Math.pow(10,e.args.limit);s.value=Math.round(s.value*t)/t}}return 0===s.value&&(s.value=0),!t._flags.unsafe&&(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&(s.errors=r("number.unsafe")),s},rules:{compare:{method:!1,validate:(e,t,{limit:r},{name:n,operator:s,args:o})=>i.compare(e,r,s)?e:t.error("number."+n,{limit:o.limit,value:e}),args:[{name:"limit",ref:!0,assert:i.isNumber,message:"must be a number"}]},greater:{method(e){return this.$_addRule({name:"greater",method:"compare",args:{limit:e},operator:">"})}},integer:{method(){return this.$_addRule("integer")},validate:(e,t)=>Math.trunc(e)-e==0?e:t.error("number.integer")},less:{method(e){return this.$_addRule({name:"less",method:"compare",args:{limit:e},operator:"<"})}},max:{method(e){return this.$_addRule({name:"max",method:"compare",args:{limit:e},operator:"<="})}},min:{method(e){return this.$_addRule({name:"min",method:"compare",args:{limit:e},operator:">="})}},multiple:{method(e){const t="number"==typeof e?o.decimalPlaces(e):null,r=Math.pow(10,t);return this.$_addRule({name:"multiple",args:{base:e,baseDecimalPlace:t,pfactor:r}})},validate:(e,t,{base:r,baseDecimalPlace:n,pfactor:s},i)=>o.decimalPlaces(e)>n?t.error("number.multiple",{multiple:i.args.base,value:e}):Math.round(s*e)%Math.round(s*r)==0?e:t.error("number.multiple",{multiple:i.args.base,value:e}),args:[{name:"base",ref:!0,assert:e=>"number"==typeof e&&isFinite(e)&&e>0,message:"must be a positive number"},"baseDecimalPlace","pfactor"],multi:!0},negative:{method(){return this.sign("negative")}},port:{method(){return this.$_addRule("port")},validate:(e,t)=>Number.isSafeInteger(e)&&e>=0&&e<=65535?e:t.error("number.port")},positive:{method(){return this.sign("positive")}},precision:{method(e){return n(Number.isSafeInteger(e),"limit must be an integer"),this.$_addRule({name:"precision",args:{limit:e}})},validate(e,t,{limit:r}){const n=e.toString().match(o.precisionRx);return Math.max((n[1]?n[1].length:0)-(n[2]?parseInt(n[2],10):0),0)<=r?e:t.error("number.precision",{limit:r,value:e})},convert:!0},sign:{method(e){return n(["negative","positive"].includes(e),"Invalid sign",e),this.$_addRule({name:"sign",args:{sign:e}})},validate:(e,t,{sign:r})=>"negative"===r&&e<0||"positive"===r&&e>0?e:t.error(`number.${r}`)},unsafe:{method(e=!0){return n("boolean"==typeof e,"enabled must be a boolean"),this.$_setFlag("unsafe",e)}}},cast:{string:{from:e=>"number"==typeof e,to:(e,t)=>e.toString()}},messages:{"number.base":"{{#label}} must be a number","number.greater":"{{#label}} must be greater than {{#limit}}","number.infinity":"{{#label}} cannot be infinity","number.integer":"{{#label}} must be an integer","number.less":"{{#label}} must be less than {{#limit}}","number.max":"{{#label}} must be less than or equal to {{#limit}}","number.min":"{{#label}} must be greater than or equal to {{#limit}}","number.multiple":"{{#label}} must be a multiple of {{#multiple}}","number.negative":"{{#label}} must be a negative number","number.port":"{{#label}} must be a valid port","number.positive":"{{#label}} must be a positive number","number.precision":"{{#label}} must have no more than {{#limit}} decimal places","number.unsafe":"{{#label}} must be a safe number"}}),o.extractSignificantDigits=function(e){return e.replace(o.exponentialPartRegex,"").replace(o.dotRegex,"").replace(o.trailingZerosRegex,"").replace(o.leadingSignAndZerosRegex,"")},o.normalizeDecimal=function(e){return(e=e.replace(/^\+/,"").replace(/\.0*$/,"").replace(/^(-?)\.([^\.]*)$/,"$10.$2").replace(/^(-?)0+([0-9])/,"$1$2")).includes(".")&&e.endsWith("0")&&(e=e.replace(/0+$/,"")),"-0"===e?"0":e}},8966:(e,t,r)=>{"use strict";const n=r(7824);e.exports=n.extend({type:"object",cast:{map:{from:e=>e&&"object"==typeof e,to:(e,t)=>new Map(Object.entries(e))}}})},7417:(e,t,r)=>{"use strict";const n=r(375),s=r(5380),i=r(1745),o=r(9959),a=r(6064),c=r(9926),u=r(5752),l=r(8068),d=r(8160),f={tlds:c instanceof Set&&{tlds:{allow:c,deny:null}},base64Regex:{true:{true:/^(?:[\w\-]{2}[\w\-]{2})*(?:[\w\-]{2}==|[\w\-]{3}=)?$/,false:/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$/},false:{true:/^(?:[\w\-]{2}[\w\-]{2})*(?:[\w\-]{2}(==)?|[\w\-]{3}=?)?$/,false:/^(?:[A-Za-z0-9+\/]{2}[A-Za-z0-9+\/]{2})*(?:[A-Za-z0-9+\/]{2}(==)?|[A-Za-z0-9+\/]{3}=?)?$/}},dataUriRegex:/^data:[\w+.-]+\/[\w+.-]+;((charset=[\w-]+|base64),)?(.*)$/,hexRegex:{withPrefix:/^0x[0-9a-f]+$/i,withOptionalPrefix:/^(?:0x)?[0-9a-f]+$/i,withoutPrefix:/^[0-9a-f]+$/i},ipRegex:o.regex({cidr:"forbidden"}).regex,isoDurationRegex:/^P(?!$)(\d+Y)?(\d+M)?(\d+W)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?$/,guidBrackets:{"{":"}","[":"]","(":")","":""},guidVersions:{uuidv1:"1",uuidv2:"2",uuidv3:"3",uuidv4:"4",uuidv5:"5",uuidv6:"6",uuidv7:"7",uuidv8:"8"},guidSeparators:new Set([void 0,!0,!1,"-",":"]),normalizationForms:["NFC","NFD","NFKC","NFKD"]};e.exports=l.extend({type:"string",flags:{insensitive:{default:!1},truncate:{default:!1}},terms:{replacements:{init:null}},coerce:{from:"string",method(e,{schema:t,state:r,prefs:n}){const s=t.$_getRule("normalize");s&&(e=e.normalize(s.args.form));const i=t.$_getRule("case");i&&(e="upper"===i.args.direction?e.toLocaleUpperCase():e.toLocaleLowerCase());const o=t.$_getRule("trim");if(o&&o.args.enabled&&(e=e.trim()),t.$_terms.replacements)for(const r of t.$_terms.replacements)e=e.replace(r.pattern,r.replacement);const a=t.$_getRule("hex");if(a&&a.args.options.byteAligned&&e.length%2!=0&&(e=`0${e}`),t.$_getRule("isoDate")){const t=f.isoDate(e);t&&(e=t)}if(t._flags.truncate){const s=t.$_getRule("max");if(s){let i=s.args.limit;if(d.isResolvable(i)&&(i=i.resolve(e,r,n),!d.limit(i)))return{value:e,errors:t.$_createError("any.ref",i,{ref:s.args.limit,arg:"limit",reason:"must be a positive integer"},r,n)};e=e.slice(0,i)}}return{value:e}}},validate(e,{schema:t,error:r}){if("string"!=typeof e)return{value:e,errors:r("string.base")};if(""===e){const n=t.$_getRule("min");if(n&&0===n.args.limit)return;return{value:e,errors:r("string.empty")}}},rules:{alphanum:{method(){return this.$_addRule("alphanum")},validate:(e,t)=>/^[a-zA-Z0-9]+$/.test(e)?e:t.error("string.alphanum")},base64:{method(e={}){return d.assertOptions(e,["paddingRequired","urlSafe"]),e={urlSafe:!1,paddingRequired:!0,...e},n("boolean"==typeof e.paddingRequired,"paddingRequired must be boolean"),n("boolean"==typeof e.urlSafe,"urlSafe must be boolean"),this.$_addRule({name:"base64",args:{options:e}})},validate:(e,t,{options:r})=>f.base64Regex[r.paddingRequired][r.urlSafe].test(e)?e:t.error("string.base64")},case:{method(e){return n(["lower","upper"].includes(e),"Invalid case:",e),this.$_addRule({name:"case",args:{direction:e}})},validate:(e,t,{direction:r})=>"lower"===r&&e===e.toLocaleLowerCase()||"upper"===r&&e===e.toLocaleUpperCase()?e:t.error(`string.${r}case`),convert:!0},creditCard:{method(){return this.$_addRule("creditCard")},validate(e,t){let r=e.length,n=0,s=1;for(;r--;){const t=e.charAt(r)*s;n+=t-9*(t>9),s^=3}return n>0&&n%10==0?e:t.error("string.creditCard")}},dataUri:{method(e={}){return d.assertOptions(e,["paddingRequired"]),e={paddingRequired:!0,...e},n("boolean"==typeof e.paddingRequired,"paddingRequired must be boolean"),this.$_addRule({name:"dataUri",args:{options:e}})},validate(e,t,{options:r}){const n=e.match(f.dataUriRegex);if(n){if(!n[2])return e;if("base64"!==n[2])return e;if(f.base64Regex[r.paddingRequired].false.test(n[3]))return e}return t.error("string.dataUri")}},domain:{method(e){e&&d.assertOptions(e,["allowFullyQualified","allowUnicode","maxDomainSegments","minDomainSegments","tlds"]);const t=f.addressOptions(e);return this.$_addRule({name:"domain",args:{options:e},address:t})},validate:(e,t,r,{address:n})=>s.isValid(e,n)?e:t.error("string.domain")},email:{method(e={}){d.assertOptions(e,["allowFullyQualified","allowUnicode","ignoreLength","maxDomainSegments","minDomainSegments","multiple","separator","tlds"]),n(void 0===e.multiple||"boolean"==typeof e.multiple,"multiple option must be an boolean");const t=f.addressOptions(e),r=new RegExp(`\\s*[${e.separator?a(e.separator):","}]\\s*`);return this.$_addRule({name:"email",args:{options:e},regex:r,address:t})},validate(e,t,{options:r},{regex:n,address:s}){const o=r.multiple?e.split(n):[e],a=[];for(const e of o)i.isValid(e,s)||a.push(e);return a.length?t.error("string.email",{value:e,invalids:a}):e}},guid:{alias:"uuid",method(e={}){d.assertOptions(e,["version","separator"]);let t="";if(e.version){const r=[].concat(e.version);n(r.length>=1,"version must have at least 1 valid version specified");const s=new Set;for(let e=0;e<r.length;++e){const i=r[e];n("string"==typeof i,"version at position "+e+" must be a string");const o=f.guidVersions[i.toLowerCase()];n(o,"version at position "+e+" must be one of "+Object.keys(f.guidVersions).join(", ")),n(!s.has(o),"version at position "+e+" must not be a duplicate"),t+=o,s.add(o)}}n(f.guidSeparators.has(e.separator),'separator must be one of true, false, "-", or ":"');const r=void 0===e.separator?"[:-]?":!0===e.separator?"[:-]":!1===e.separator?"[]?":`\\${e.separator}`,s=new RegExp(`^([\\[{\\(]?)[0-9A-F]{8}(${r})[0-9A-F]{4}\\2?[${t||"0-9A-F"}][0-9A-F]{3}\\2?[${t?"89AB":"0-9A-F"}][0-9A-F]{3}\\2?[0-9A-F]{12}([\\]}\\)]?)$`,"i");return this.$_addRule({name:"guid",args:{options:e},regex:s})},validate(e,t,r,{regex:n}){const s=n.exec(e);return s?f.guidBrackets[s[1]]!==s[s.length-1]?t.error("string.guid"):e:t.error("string.guid")}},hex:{method(e={}){return d.assertOptions(e,["byteAligned","prefix"]),e={byteAligned:!1,prefix:!1,...e},n("boolean"==typeof e.byteAligned,"byteAligned must be boolean"),n("boolean"==typeof e.prefix||"optional"===e.prefix,'prefix must be boolean or "optional"'),this.$_addRule({name:"hex",args:{options:e}})},validate:(e,t,{options:r})=>("optional"===r.prefix?f.hexRegex.withOptionalPrefix:!0===r.prefix?f.hexRegex.withPrefix:f.hexRegex.withoutPrefix).test(e)?r.byteAligned&&e.length%2!=0?t.error("string.hexAlign"):e:t.error("string.hex")},hostname:{method(){return this.$_addRule("hostname")},validate:(e,t)=>s.isValid(e,{minDomainSegments:1})||f.ipRegex.test(e)?e:t.error("string.hostname")},insensitive:{method(){return this.$_setFlag("insensitive",!0)}},ip:{method(e={}){d.assertOptions(e,["cidr","version"]);const{cidr:t,versions:r,regex:n}=o.regex(e),s=e.version?r:void 0;return this.$_addRule({name:"ip",args:{options:{cidr:t,version:s}},regex:n})},validate:(e,t,{options:r},{regex:n})=>n.test(e)?e:r.version?t.error("string.ipVersion",{value:e,cidr:r.cidr,version:r.version}):t.error("string.ip",{value:e,cidr:r.cidr})},isoDate:{method(){return this.$_addRule("isoDate")},validate:(e,{error:t})=>f.isoDate(e)?e:t("string.isoDate")},isoDuration:{method(){return this.$_addRule("isoDuration")},validate:(e,t)=>f.isoDurationRegex.test(e)?e:t.error("string.isoDuration")},length:{method(e,t){return f.length(this,"length",e,"=",t)},validate(e,t,{limit:r,encoding:n},{name:s,operator:i,args:o}){const a=!n&&e.length;return d.compare(a,r,i)?e:t.error("string."+s,{limit:o.limit,value:e,encoding:n})},args:[{name:"limit",ref:!0,assert:d.limit,message:"must be a positive integer"},"encoding"]},lowercase:{method(){return this.case("lower")}},max:{method(e,t){return f.length(this,"max",e,"<=",t)},args:["limit","encoding"]},min:{method(e,t){return f.length(this,"min",e,">=",t)},args:["limit","encoding"]},normalize:{method(e="NFC"){return n(f.normalizationForms.includes(e),"normalization form must be one of "+f.normalizationForms.join(", ")),this.$_addRule({name:"normalize",args:{form:e}})},validate:(e,{error:t},{form:r})=>e===e.normalize(r)?e:t("string.normalize",{value:e,form:r}),convert:!0},pattern:{alias:"regex",method(e,t={}){n(e instanceof RegExp,"regex must be a RegExp"),n(!e.flags.includes("g")&&!e.flags.includes("y"),"regex should not use global or sticky mode"),"string"==typeof t&&(t={name:t}),d.assertOptions(t,["invert","name"]);const r=["string.pattern",t.invert?".invert":"",t.name?".name":".base"].join("");return this.$_addRule({name:"pattern",args:{regex:e,options:t},errorCode:r})},validate:(e,t,{regex:r,options:n},{errorCode:s})=>r.test(e)^n.invert?e:t.error(s,{name:n.name,regex:r,value:e}),args:["regex","options"],multi:!0},replace:{method(e,t){"string"==typeof e&&(e=new RegExp(a(e),"g")),n(e instanceof RegExp,"pattern must be a RegExp"),n("string"==typeof t,"replacement must be a String");const r=this.clone();return r.$_terms.replacements||(r.$_terms.replacements=[]),r.$_terms.replacements.push({pattern:e,replacement:t}),r}},token:{method(){return this.$_addRule("token")},validate:(e,t)=>/^\w+$/.test(e)?e:t.error("string.token")},trim:{method(e=!0){return n("boolean"==typeof e,"enabled must be a boolean"),this.$_addRule({name:"trim",args:{enabled:e}})},validate:(e,t,{enabled:r})=>r&&e!==e.trim()?t.error("string.trim"):e,convert:!0},truncate:{method(e=!0){return n("boolean"==typeof e,"enabled must be a boolean"),this.$_setFlag("truncate",e)}},uppercase:{method(){return this.case("upper")}},uri:{method(e={}){d.assertOptions(e,["allowRelative","allowQuerySquareBrackets","domain","relativeOnly","scheme","encodeUri"]),e.domain&&d.assertOptions(e.domain,["allowFullyQualified","allowUnicode","maxDomainSegments","minDomainSegments","tlds"]);const{regex:t,scheme:r}=u.regex(e),n=e.domain?f.addressOptions(e.domain):null;return this.$_addRule({name:"uri",args:{options:e},regex:t,domain:n,scheme:r})},validate(e,t,{options:r},{regex:n,domain:i,scheme:o}){if(["http:/","https:/"].includes(e))return t.error("string.uri");let a=n.exec(e);if(!a&&t.prefs.convert&&r.encodeUri){const t=encodeURI(e);a=n.exec(t),a&&(e=t)}if(a){const n=a[1]||a[2];return!i||r.allowRelative&&!n||s.isValid(n,i)?e:t.error("string.domain",{value:n})}return r.relativeOnly?t.error("string.uriRelativeOnly"):r.scheme?t.error("string.uriCustomScheme",{scheme:o,value:e}):t.error("string.uri")}}},manifest:{build(e,t){if(t.replacements)for(const{pattern:r,replacement:n}of t.replacements)e=e.replace(r,n);return e}},messages:{"string.alphanum":"{{#label}} must only contain alpha-numeric characters","string.base":"{{#label}} must be a string","string.base64":"{{#label}} must be a valid base64 string","string.creditCard":"{{#label}} must be a credit card","string.dataUri":"{{#label}} must be a valid dataUri string","string.domain":"{{#label}} must contain a valid domain name","string.email":"{{#label}} must be a valid email","string.empty":"{{#label}} is not allowed to be empty","string.guid":"{{#label}} must be a valid GUID","string.hex":"{{#label}} must only contain hexadecimal characters","string.hexAlign":"{{#label}} hex decoded representation must be byte aligned","string.hostname":"{{#label}} must be a valid hostname","string.ip":"{{#label}} must be a valid ip address with a {{#cidr}} CIDR","string.ipVersion":"{{#label}} must be a valid ip address of one of the following versions {{#version}} with a {{#cidr}} CIDR","string.isoDate":"{{#label}} must be in iso format","string.isoDuration":"{{#label}} must be a valid ISO 8601 duration","string.length":"{{#label}} length must be {{#limit}} characters long","string.lowercase":"{{#label}} must only contain lowercase characters","string.max":"{{#label}} length must be less than or equal to {{#limit}} characters long","string.min":"{{#label}} length must be at least {{#limit}} characters long","string.normalize":"{{#label}} must be unicode normalized in the {{#form}} form","string.token":"{{#label}} must only contain alpha-numeric and underscore characters","string.pattern.base":"{{#label}} with value {:[.]} fails to match the required pattern: {{#regex}}","string.pattern.name":"{{#label}} with value {:[.]} fails to match the {{#name}} pattern","string.pattern.invert.base":"{{#label}} with value {:[.]} matches the inverted pattern: {{#regex}}","string.pattern.invert.name":"{{#label}} with value {:[.]} matches the inverted {{#name}} pattern","string.trim":"{{#label}} must not have leading or trailing whitespace","string.uri":"{{#label}} must be a valid uri","string.uriCustomScheme":"{{#label}} must be a valid uri with a scheme matching the {{#scheme}} pattern","string.uriRelativeOnly":"{{#label}} must be a valid relative uri","string.uppercase":"{{#label}} must only contain uppercase characters"}}),f.addressOptions=function(e){if(!e)return f.tlds||e;if(n(void 0===e.minDomainSegments||Number.isSafeInteger(e.minDomainSegments)&&e.minDomainSegments>0,"minDomainSegments must be a positive integer"),n(void 0===e.maxDomainSegments||Number.isSafeInteger(e.maxDomainSegments)&&e.maxDomainSegments>0,"maxDomainSegments must be a positive integer"),!1===e.tlds)return e;if(!0===e.tlds||void 0===e.tlds)return n(f.tlds,"Built-in TLD list disabled"),Object.assign({},e,f.tlds);n("object"==typeof e.tlds,"tlds must be true, false, or an object");const t=e.tlds.deny;if(t)return Array.isArray(t)&&(e=Object.assign({},e,{tlds:{deny:new Set(t)}})),n(e.tlds.deny instanceof Set,"tlds.deny must be an array, Set, or boolean"),n(!e.tlds.allow,"Cannot specify both tlds.allow and tlds.deny lists"),f.validateTlds(e.tlds.deny,"tlds.deny"),e;const r=e.tlds.allow;return r?!0===r?(n(f.tlds,"Built-in TLD list disabled"),Object.assign({},e,f.tlds)):(Array.isArray(r)&&(e=Object.assign({},e,{tlds:{allow:new Set(r)}})),n(e.tlds.allow instanceof Set,"tlds.allow must be an array, Set, or boolean"),f.validateTlds(e.tlds.allow,"tlds.allow"),e):e},f.validateTlds=function(e,t){for(const r of e)n(s.isValid(r,{minDomainSegments:1,maxDomainSegments:1}),`${t} must contain valid top level domain names`)},f.isoDate=function(e){if(!d.isIsoDate(e))return null;/.*T.*[+-]\d\d$/.test(e)&&(e+="00");const t=new Date(e);return isNaN(t.getTime())?null:t.toISOString()},f.length=function(e,t,r,s,i){return n(!i||!1,"Invalid encoding:",i),e.$_addRule({name:t,method:"length",args:{limit:r,encoding:i},operator:s})}},8826:(e,t,r)=>{"use strict";const n=r(375),s=r(8068),i={};i.Map=class extends Map{slice(){return new i.Map(this)}},e.exports=s.extend({type:"symbol",terms:{map:{init:new i.Map}},coerce:{method(e,{schema:t,error:r}){const n=t.$_terms.map.get(e);return n&&(e=n),t._flags.only&&"symbol"!=typeof e?{value:e,errors:r("symbol.map",{map:t.$_terms.map})}:{value:e}}},validate(e,{error:t}){if("symbol"!=typeof e)return{value:e,errors:t("symbol.base")}},rules:{map:{method(e){e&&!e[Symbol.iterator]&&"object"==typeof e&&(e=Object.entries(e)),n(e&&e[Symbol.iterator],"Iterable must be an iterable or object");const t=this.clone(),r=[];for(const s of e){n(s&&s[Symbol.iterator],"Entry must be an iterable");const[e,i]=s;n("object"!=typeof e&&"function"!=typeof e&&"symbol"!=typeof e,"Key must not be of type object, function, or Symbol"),n("symbol"==typeof i,"Value must be a Symbol"),t.$_terms.map.set(e,i),r.push(i)}return t.valid(...r)}}},manifest:{build:(e,t)=>(t.map&&(e=e.map(t.map)),e)},messages:{"symbol.base":"{{#label}} must be a symbol","symbol.map":"{{#label}} must be one of {{#map}}"}})},8863:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(738),o=r(9621),a=r(8160),c=r(6354),u=r(493),l={result:Symbol("result")};t.entry=function(e,t,r){let s=a.defaults;r&&(n(void 0===r.warnings,"Cannot override warnings preference in synchronous validation"),n(void 0===r.artifacts,"Cannot override artifacts preference in synchronous validation"),s=a.preferences(a.defaults,r));const i=l.entry(e,t,s);n(!i.mainstay.externals.length,"Schema with external rules must use validateAsync()");const o={value:i.value};return i.error&&(o.error=i.error),i.mainstay.warnings.length&&(o.warning=c.details(i.mainstay.warnings)),i.mainstay.debug&&(o.debug=i.mainstay.debug),i.mainstay.artifacts&&(o.artifacts=i.mainstay.artifacts),o},t.entryAsync=async function(e,t,r){let n=a.defaults;r&&(n=a.preferences(a.defaults,r));const s=l.entry(e,t,n),i=s.mainstay;if(s.error)throw i.debug&&(s.error.debug=i.debug),s.error;if(i.externals.length){let t=s.value;const u=[];for(const s of i.externals){const d=s.state.path,f="link"===s.schema.type?i.links.get(s.schema):null;let p,h,m=t;const g=d.length?[t]:[],y=d.length?o(e,d):e;if(d.length){p=d[d.length-1];let e=t;for(const t of d.slice(0,-1))e=e[t],g.unshift(e);h=g[0],m=h[p]}try{const e=(e,t)=>(f||s.schema).$_createError(e,m,t,s.state,n),o=await s.method(m,{schema:s.schema,linked:f,state:s.state,prefs:r,original:y,error:e,errorsArray:l.errorsArray,warn:(e,t)=>i.warnings.push((f||s.schema).$_createError(e,m,t,s.state,n)),message:(e,t)=>(f||s.schema).$_createError("external",m,t,s.state,n,{messages:e})});if(void 0===o||o===m)continue;if(o instanceof c.Report){if(i.tracer.log(s.schema,s.state,"rule","external","error"),u.push(o),n.abortEarly)break;continue}if(Array.isArray(o)&&o[a.symbols.errors]){if(i.tracer.log(s.schema,s.state,"rule","external","error"),u.push(...o),n.abortEarly)break;continue}h?(i.tracer.value(s.state,"rule",m,o,"external"),h[p]=o):(i.tracer.value(s.state,"rule",t,o,"external"),t=o)}catch(e){throw n.errors.label&&(e.message+=` (${s.label})`),e}}if(s.value=t,u.length)throw s.error=c.process(u,e,n),i.debug&&(s.error.debug=i.debug),s.error}if(!n.warnings&&!n.debug&&!n.artifacts)return s.value;const u={value:s.value};return i.warnings.length&&(u.warning=c.details(i.warnings)),i.debug&&(u.debug=i.debug),i.artifacts&&(u.artifacts=i.artifacts),u},l.Mainstay=class{constructor(e,t,r){this.externals=[],this.warnings=[],this.tracer=e,this.debug=t,this.links=r,this.shadow=null,this.artifacts=null,this._snapshots=[]}snapshot(){this._snapshots.push({externals:this.externals.slice(),warnings:this.warnings.slice()})}restore(){const e=this._snapshots.pop();this.externals=e.externals,this.warnings=e.warnings}commit(){this._snapshots.pop()}},l.entry=function(e,r,n){const{tracer:s,cleanup:i}=l.tracer(r,n),o=n.debug?[]:null,a=r._ids._schemaChain?new Map:null,d=new l.Mainstay(s,o,a),f=r._ids._schemaChain?[{schema:r}]:null,p=new u([],[],{mainstay:d,schemas:f}),h=t.validate(e,r,p,n);i&&r.$_root.untrace();const m=c.process(h.errors,e,n);return{value:h.value,error:m,mainstay:d}},l.tracer=function(e,t){return e.$_root._tracer?{tracer:e.$_root._tracer._register(e)}:t.debug?(n(e.$_root.trace,"Debug mode not supported"),{tracer:e.$_root.trace()._register(e),cleanup:!0}):{tracer:l.ignore}},t.validate=function(e,t,r,n,s={}){if(t.$_terms.whens&&(t=t._generate(e,r,n).schema),t._preferences&&(n=l.prefs(t,n)),t._cache&&n.cache){const n=t._cache.get(e);if(r.mainstay.tracer.debug(r,"validate","cached",!!n),n)return n}const i=(s,i,o)=>t.$_createError(s,e,i,o||r,n),o={original:e,prefs:n,schema:t,state:r,error:i,errorsArray:l.errorsArray,warn:(e,t,n)=>r.mainstay.warnings.push(i(e,t,n)),message:(s,i)=>t.$_createError("custom",e,i,r,n,{messages:s})};r.mainstay.tracer.entry(t,r);const c=t._definition;if(c.prepare&&void 0!==e&&n.convert){const t=c.prepare(e,o);if(t){if(r.mainstay.tracer.value(r,"prepare",e,t.value),t.errors)return l.finalize(t.value,[].concat(t.errors),o);e=t.value}}if(c.coerce&&void 0!==e&&n.convert&&(!c.coerce.from||c.coerce.from.includes(typeof e))){const t=c.coerce.method(e,o);if(t){if(r.mainstay.tracer.value(r,"coerced",e,t.value),t.errors)return l.finalize(t.value,[].concat(t.errors),o);e=t.value}}const u=t._flags.empty;u&&u.$_match(l.trim(e,t),r.nest(u),a.defaults)&&(r.mainstay.tracer.value(r,"empty",e,void 0),e=void 0);const d=s.presence||t._flags.presence||(t._flags._endedSwitch?null:n.presence);if(void 0===e){if("forbidden"===d)return l.finalize(e,null,o);if("required"===d)return l.finalize(e,[t.$_createError("any.required",e,null,r,n)],o);if("optional"===d){if(t._flags.default!==a.symbols.deepDefault)return l.finalize(e,null,o);r.mainstay.tracer.value(r,"default",e,{}),e={}}}else if("forbidden"===d)return l.finalize(e,[t.$_createError("any.unknown",e,null,r,n)],o);const f=[];if(t._valids){const s=t._valids.get(e,r,n,t._flags.insensitive);if(s)return n.convert&&(r.mainstay.tracer.value(r,"valids",e,s.value),e=s.value),r.mainstay.tracer.filter(t,r,"valid",s),l.finalize(e,null,o);if(t._flags.only){const s=t.$_createError("any.only",e,{valids:t._valids.values({display:!0})},r,n);if(n.abortEarly)return l.finalize(e,[s],o);f.push(s)}}if(t._invalids){const s=t._invalids.get(e,r,n,t._flags.insensitive);if(s){r.mainstay.tracer.filter(t,r,"invalid",s);const i=t.$_createError("any.invalid",e,{invalids:t._invalids.values({display:!0})},r,n);if(n.abortEarly)return l.finalize(e,[i],o);f.push(i)}}if(c.validate){const t=c.validate(e,o);if(t&&(r.mainstay.tracer.value(r,"base",e,t.value),e=t.value,t.errors)){if(!Array.isArray(t.errors))return f.push(t.errors),l.finalize(e,f,o);if(t.errors.length)return f.push(...t.errors),l.finalize(e,f,o)}}return t._rules.length?l.rules(e,f,o):l.finalize(e,f,o)},l.rules=function(e,t,r){const{schema:n,state:s,prefs:i}=r;for(const o of n._rules){const c=n._definition.rules[o.method];if(c.convert&&i.convert){s.mainstay.tracer.log(n,s,"rule",o.name,"full");continue}let u,d=o.args;if(o._resolve.length){d=Object.assign({},d);for(const t of o._resolve){const r=c.argsByName.get(t),o=d[t].resolve(e,s,i),l=r.normalize?r.normalize(o):o,f=a.validateArg(l,null,r);if(f){u=n.$_createError("any.ref",o,{arg:t,ref:d[t],reason:f},s,i);break}d[t]=l}}u=u||c.validate(e,r,d,o);const f=l.rule(u,o);if(f.errors){if(s.mainstay.tracer.log(n,s,"rule",o.name,"error"),o.warn){s.mainstay.warnings.push(...f.errors);continue}if(i.abortEarly)return l.finalize(e,f.errors,r);t.push(...f.errors)}else s.mainstay.tracer.log(n,s,"rule",o.name,"pass"),s.mainstay.tracer.value(s,"rule",e,f.value,o.name),e=f.value}return l.finalize(e,t,r)},l.rule=function(e,t){return e instanceof c.Report?(l.error(e,t),{errors:[e],value:null}):Array.isArray(e)&&e[a.symbols.errors]?(e.forEach((e=>l.error(e,t))),{errors:e,value:null}):{errors:null,value:e}},l.error=function(e,t){return t.message&&e._setTemplate(t.message),e},l.finalize=function(e,t,r){t=t||[];const{schema:s,state:i,prefs:o}=r;if(t.length){const n=l.default("failover",void 0,t,r);void 0!==n&&(i.mainstay.tracer.value(i,"failover",e,n),e=n,t=[])}if(t.length&&s._flags.error)if("function"==typeof s._flags.error){t=s._flags.error(t),Array.isArray(t)||(t=[t]);for(const e of t)n(e instanceof Error||e instanceof c.Report,"error() must return an Error object")}else t=[s._flags.error];if(void 0===e){const n=l.default("default",e,t,r);i.mainstay.tracer.value(i,"default",e,n),e=n}if(s._flags.cast&&void 0!==e){const t=s._definition.cast[s._flags.cast];if(t.from(e)){const n=t.to(e,r);i.mainstay.tracer.value(i,"cast",e,n,s._flags.cast),e=n}}if(s.$_terms.externals&&o.externals&&!1!==o._externals)for(const{method:e}of s.$_terms.externals)i.mainstay.externals.push({method:e,schema:s,state:i,label:c.label(s._flags,i,o)});const a={value:e,errors:t.length?t:null};return s._flags.result&&(a.value="strip"===s._flags.result?void 0:r.original,i.mainstay.tracer.value(i,s._flags.result,e,a.value),i.shadow(e,s._flags.result)),s._cache&&!1!==o.cache&&!s._refs.length&&s._cache.set(r.original,a),void 0===e||a.errors||void 0===s._flags.artifact||(i.mainstay.artifacts=i.mainstay.artifacts||new Map,i.mainstay.artifacts.has(s._flags.artifact)||i.mainstay.artifacts.set(s._flags.artifact,[]),i.mainstay.artifacts.get(s._flags.artifact).push(i.path)),a},l.prefs=function(e,t){const r=t===a.defaults;return r&&e._preferences[a.symbols.prefs]?e._preferences[a.symbols.prefs]:(t=a.preferences(t,e._preferences),r&&(e._preferences[a.symbols.prefs]=t),t)},l.default=function(e,t,r,n){const{schema:i,state:o,prefs:c}=n,u=i._flags[e];if(c.noDefaults||void 0===u)return t;if(o.mainstay.tracer.log(i,o,"rule",e,"full"),!u)return u;if("function"==typeof u){const a=u.length?[s(o.ancestors[0]),n]:[];try{return u(...a)}catch(t){return void r.push(i.$_createError(`any.${e}`,null,{error:t},o,c))}}return"object"!=typeof u?u:u[a.symbols.literal]?u.literal:a.isResolvable(u)?u.resolve(t,o,c):s(u)},l.trim=function(e,t){if("string"!=typeof e)return e;const r=t.$_getRule("trim");return r&&r.args.enabled?e.trim():e},l.ignore={active:!1,debug:i,entry:i,filter:i,log:i,resolve:i,value:i},l.errorsArray=function(){const e=[];return e[a.symbols.errors]=!0,e}},2036:(e,t,r)=>{"use strict";const n=r(375),s=r(9474),i=r(8160),o={};e.exports=o.Values=class{constructor(e,t){this._values=new Set(e),this._refs=new Set(t),this._lowercase=o.lowercases(e),this._override=!1}get length(){return this._values.size+this._refs.size}add(e,t){i.isResolvable(e)?this._refs.has(e)||(this._refs.add(e),t&&t.register(e)):this.has(e,null,null,!1)||(this._values.add(e),"string"==typeof e&&this._lowercase.set(e.toLowerCase(),e))}static merge(e,t,r){if(e=e||new o.Values,t){if(t._override)return t.clone();for(const r of[...t._values,...t._refs])e.add(r)}if(r)for(const t of[...r._values,...r._refs])e.remove(t);return e.length?e:null}remove(e){i.isResolvable(e)?this._refs.delete(e):(this._values.delete(e),"string"==typeof e&&this._lowercase.delete(e.toLowerCase()))}has(e,t,r,n){return!!this.get(e,t,r,n)}get(e,t,r,n){if(!this.length)return!1;if(this._values.has(e))return{value:e};if("string"==typeof e&&e&&n){const t=this._lowercase.get(e.toLowerCase());if(t)return{value:t}}if(!this._refs.size&&"object"!=typeof e)return!1;if("object"==typeof e)for(const t of this._values)if(s(t,e))return{value:t};if(t)for(const i of this._refs){const o=i.resolve(e,t,r,null,{in:!0});if(void 0===o)continue;const a=i.in&&"object"==typeof o?Array.isArray(o)?o:Object.keys(o):[o];for(const t of a)if(typeof t==typeof e)if(n&&e&&"string"==typeof e){if(t.toLowerCase()===e.toLowerCase())return{value:t,ref:i}}else if(s(t,e))return{value:t,ref:i}}return!1}override(){this._override=!0}values(e){if(e&&e.display){const e=[];for(const t of[...this._values,...this._refs])void 0!==t&&e.push(t);return e}return Array.from([...this._values,...this._refs])}clone(){const e=new o.Values(this._values,this._refs);return e._override=this._override,e}concat(e){n(!e._override,"Cannot concat override set of values");const t=new o.Values([...this._values,...e._values],[...this._refs,...e._refs]);return t._override=this._override,t}describe(){const e=[];this._override&&e.push({override:!0});for(const t of this._values.values())e.push(t&&"object"==typeof t?{value:t}:t);for(const t of this._refs.values())e.push(t.describe());return e}},o.Values.prototype[i.symbols.values]=!0,o.Values.prototype.slice=o.Values.prototype.clone,o.lowercases=function(e){const t=new Map;if(e)for(const r of e)"string"==typeof r&&t.set(r.toLowerCase(),r);return t}},978:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(1687),o=r(9621),a={};e.exports=function(e,t,r={}){if(n(e&&"object"==typeof e,"Invalid defaults value: must be an object"),n(!t||!0===t||"object"==typeof t,"Invalid source value: must be true, falsy or an object"),n("object"==typeof r,"Invalid options: must be an object"),!t)return null;if(r.shallow)return a.applyToDefaultsWithShallow(e,t,r);const o=s(e);if(!0===t)return o;const c=void 0!==r.nullOverride&&r.nullOverride;return i(o,t,{nullOverride:c,mergeArrays:!1})},a.applyToDefaultsWithShallow=function(e,t,r){const c=r.shallow;n(Array.isArray(c),"Invalid keys");const u=new Map,l=!0===t?null:new Set;for(let r of c){r=Array.isArray(r)?r:r.split(".");const n=o(e,r);n&&"object"==typeof n?u.set(n,l&&o(t,r)||n):l&&l.add(r)}const d=s(e,{},u);if(!l)return d;for(const e of l)a.reachCopy(d,t,e);const f=void 0!==r.nullOverride&&r.nullOverride;return i(d,t,{nullOverride:f,mergeArrays:!1})},a.reachCopy=function(e,t,r){for(const e of r){if(!(e in t))return;const r=t[e];if("object"!=typeof r||null===r)return;t=r}const n=t;let s=e;for(let e=0;e<r.length-1;++e){const t=r[e];"object"!=typeof s[t]&&(s[t]={}),s=s[t]}s[r[r.length-1]]=n}},375:(e,t,r)=>{"use strict";const n=r(7916);e.exports=function(e,...t){if(!e){if(1===t.length&&t[0]instanceof Error)throw t[0];throw new n(t)}}},8571:(e,t,r)=>{"use strict";const n=r(9621),s=r(4277),i=r(7043),o={needsProtoHack:new Set([s.set,s.map,s.weakSet,s.weakMap])};e.exports=o.clone=function(e,t={},r=null){if("object"!=typeof e||null===e)return e;let n=o.clone,a=r;if(t.shallow){if(!0!==t.shallow)return o.cloneWithShallow(e,t);n=e=>e}else if(a){const t=a.get(e);if(t)return t}else a=new Map;const c=s.getInternalProto(e);if(c===s.buffer)return!1;if(c===s.date)return new Date(e.getTime());if(c===s.regex)return new RegExp(e);const u=o.base(e,c,t);if(u===e)return e;if(a&&a.set(e,u),c===s.set)for(const r of e)u.add(n(r,t,a));else if(c===s.map)for(const[r,s]of e)u.set(r,n(s,t,a));const l=i.keys(e,t);for(const r of l){if("__proto__"===r)continue;if(c===s.array&&"length"===r){u.length=e.length;continue}const i=Object.getOwnPropertyDescriptor(e,r);i?i.get||i.set?Object.defineProperty(u,r,i):i.enumerable?u[r]=n(e[r],t,a):Object.defineProperty(u,r,{enumerable:!1,writable:!0,configurable:!0,value:n(e[r],t,a)}):Object.defineProperty(u,r,{enumerable:!0,writable:!0,configurable:!0,value:n(e[r],t,a)})}return u},o.cloneWithShallow=function(e,t){const r=t.shallow;(t=Object.assign({},t)).shallow=!1;const s=new Map;for(const t of r){const r=n(e,t);"object"!=typeof r&&"function"!=typeof r||s.set(r,r)}return o.clone(e,t,s)},o.base=function(e,t,r){if(!1===r.prototype)return o.needsProtoHack.has(t)?new t.constructor:t===s.array?[]:{};const n=Object.getPrototypeOf(e);if(n&&n.isImmutable)return e;if(t===s.array){const e=[];return n!==t&&Object.setPrototypeOf(e,n),e}if(o.needsProtoHack.has(t)){const e=new n.constructor;return n!==t&&Object.setPrototypeOf(e,n),e}return Object.create(n)}},9474:(e,t,r)=>{"use strict";const n=r(4277),s={mismatched:null};e.exports=function(e,t,r){return r=Object.assign({prototype:!0},r),!!s.isDeepEqual(e,t,r,[])},s.isDeepEqual=function(e,t,r,i){if(e===t)return 0!==e||1/e==1/t;const o=typeof e;if(o!==typeof t)return!1;if(null===e||null===t)return!1;if("function"===o){if(!r.deepFunction||e.toString()!==t.toString())return!1}else if("object"!==o)return e!=e&&t!=t;const a=s.getSharedType(e,t,!!r.prototype);switch(a){case n.buffer:return!1;case n.promise:return e===t;case n.regex:return e.toString()===t.toString();case s.mismatched:return!1}for(let r=i.length-1;r>=0;--r)if(i[r].isSame(e,t))return!0;i.push(new s.SeenEntry(e,t));try{return!!s.isDeepEqualObj(a,e,t,r,i)}finally{i.pop()}},s.getSharedType=function(e,t,r){if(r)return Object.getPrototypeOf(e)!==Object.getPrototypeOf(t)?s.mismatched:n.getInternalProto(e);const i=n.getInternalProto(e);return i!==n.getInternalProto(t)?s.mismatched:i},s.valueOf=function(e){const t=e.valueOf;if(void 0===t)return e;try{return t.call(e)}catch(e){return e}},s.hasOwnEnumerableProperty=function(e,t){return Object.prototype.propertyIsEnumerable.call(e,t)},s.isSetSimpleEqual=function(e,t){for(const r of Set.prototype.values.call(e))if(!Set.prototype.has.call(t,r))return!1;return!0},s.isDeepEqualObj=function(e,t,r,i,o){const{isDeepEqual:a,valueOf:c,hasOwnEnumerableProperty:u}=s,{keys:l,getOwnPropertySymbols:d}=Object;if(e===n.array){if(!i.part){if(t.length!==r.length)return!1;for(let e=0;e<t.length;++e)if(!a(t[e],r[e],i,o))return!1;return!0}for(const e of t)for(const t of r)if(a(e,t,i,o))return!0}else if(e===n.set){if(t.size!==r.size)return!1;if(!s.isSetSimpleEqual(t,r)){const e=new Set(Set.prototype.values.call(r));for(const r of Set.prototype.values.call(t)){if(e.delete(r))continue;let t=!1;for(const n of e)if(a(r,n,i,o)){e.delete(n),t=!0;break}if(!t)return!1}}}else if(e===n.map){if(t.size!==r.size)return!1;for(const[e,n]of Map.prototype.entries.call(t)){if(void 0===n&&!Map.prototype.has.call(r,e))return!1;if(!a(n,Map.prototype.get.call(r,e),i,o))return!1}}else if(e===n.error&&(t.name!==r.name||t.message!==r.message))return!1;const f=c(t),p=c(r);if((t!==f||r!==p)&&!a(f,p,i,o))return!1;const h=l(t);if(!i.part&&h.length!==l(r).length&&!i.skip)return!1;let m=0;for(const e of h)if(i.skip&&i.skip.includes(e))void 0===r[e]&&++m;else{if(!u(r,e))return!1;if(!a(t[e],r[e],i,o))return!1}if(!i.part&&h.length-m!==l(r).length)return!1;if(!1!==i.symbols){const e=d(t),n=new Set(d(r));for(const s of e){if(!i.skip||!i.skip.includes(s))if(u(t,s)){if(!u(r,s))return!1;if(!a(t[s],r[s],i,o))return!1}else if(u(r,s))return!1;n.delete(s)}for(const e of n)if(u(r,e))return!1}return!0},s.SeenEntry=class{constructor(e,t){this.obj=e,this.ref=t}isSame(e,t){return this.obj===e&&this.ref===t}}},7916:(e,t,r)=>{"use strict";const n=r(8761);e.exports=class extends Error{constructor(e){super(e.filter((e=>""!==e)).map((e=>"string"==typeof e?e:e instanceof Error?e.message:n(e))).join(" ")||"Unknown error"),"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,t.assert)}}},5277:e=>{"use strict";const t={};e.exports=function(e){if(!e)return"";let r="";for(let n=0;n<e.length;++n){const s=e.charCodeAt(n);t.isSafe(s)?r+=e[n]:r+=t.escapeHtmlChar(s)}return r},t.escapeHtmlChar=function(e){return t.namedHtml.get(e)||(e>=256?"&#"+e+";":`&#x${e.toString(16).padStart(2,"0")};`)},t.isSafe=function(e){return t.safeCharCodes.has(e)},t.namedHtml=new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[34,"&quot;"],[160,"&nbsp;"],[162,"&cent;"],[163,"&pound;"],[164,"&curren;"],[169,"&copy;"],[174,"&reg;"]]),t.safeCharCodes=function(){const e=new Set;for(let t=32;t<123;++t)(t>=97||t>=65&&t<=90||t>=48&&t<=57||32===t||46===t||44===t||45===t||58===t||95===t)&&e.add(t);return e}()},6064:e=>{"use strict";e.exports=function(e){return e.replace(/[\^\$\.\*\+\-\?\=\!\:\|\\\/\(\)\[\]\{\}\,]/g,"\\$&")}},738:e=>{"use strict";e.exports=function(){}},1687:(e,t,r)=>{"use strict";const n=r(375),s=r(8571),i=r(7043),o={};e.exports=o.merge=function(e,t,r){if(n(e&&"object"==typeof e,"Invalid target value: must be an object"),n(null==t||"object"==typeof t,"Invalid source value: must be null, undefined, or an object"),!t)return e;if(r=Object.assign({nullOverride:!0,mergeArrays:!0},r),Array.isArray(t)){n(Array.isArray(e),"Cannot merge array onto an object"),r.mergeArrays||(e.length=0);for(let n=0;n<t.length;++n)e.push(s(t[n],{symbols:r.symbols}));return e}const a=i.keys(t,r);for(let n=0;n<a.length;++n){const i=a[n];if("__proto__"===i||!Object.prototype.propertyIsEnumerable.call(t,i))continue;const c=t[i];if(c&&"object"==typeof c){if(e[i]===c)continue;!e[i]||"object"!=typeof e[i]||Array.isArray(e[i])!==Array.isArray(c)||c instanceof Date||c instanceof RegExp?e[i]=s(c,{symbols:r.symbols}):o.merge(e[i],c,r)}else(null!=c||r.nullOverride)&&(e[i]=c)}return e}},9621:(e,t,r)=>{"use strict";const n=r(375),s={};e.exports=function(e,t,r){if(!1===t||null==t)return e;"string"==typeof(r=r||{})&&(r={separator:r});const i=Array.isArray(t);n(!i||!r.separator,"Separator option is not valid for array-based chain");const o=i?t:t.split(r.separator||".");let a=e;for(let e=0;e<o.length;++e){let i=o[e];const c=r.iterables&&s.iterables(a);if(Array.isArray(a)||"set"===c){const e=Number(i);Number.isInteger(e)&&(i=e<0?a.length+e:e)}if(!a||"function"==typeof a&&!1===r.functions||!c&&void 0===a[i]){n(!r.strict||e+1===o.length,"Missing segment",i,"in reach path ",t),n("object"==typeof a||!0===r.functions||"function"!=typeof a,"Invalid segment",i,"in reach path ",t),a=r.default;break}a=c?"set"===c?[...a][i]:a.get(i):a[i]}return a},s.iterables=function(e){return e instanceof Set?"set":e instanceof Map?"map":void 0}},8761:e=>{"use strict";e.exports=function(...e){try{return JSON.stringify(...e)}catch(e){return"[Cannot display object: "+e.message+"]"}}},4277:(e,t)=>{"use strict";const r={};t=e.exports={array:Array.prototype,buffer:!1,date:Date.prototype,error:Error.prototype,generic:Object.prototype,map:Map.prototype,promise:Promise.prototype,regex:RegExp.prototype,set:Set.prototype,weakMap:WeakMap.prototype,weakSet:WeakSet.prototype},r.typeMap=new Map([["[object Error]",t.error],["[object Map]",t.map],["[object Promise]",t.promise],["[object Set]",t.set],["[object WeakMap]",t.weakMap],["[object WeakSet]",t.weakSet]]),t.getInternalProto=function(e){if(Array.isArray(e))return t.array;if(e instanceof Date)return t.date;if(e instanceof RegExp)return t.regex;if(e instanceof Error)return t.error;const n=Object.prototype.toString.call(e);return r.typeMap.get(n)||t.generic}},7043:(e,t)=>{"use strict";t.keys=function(e,t={}){return!1!==t.symbols?Reflect.ownKeys(e):Object.getOwnPropertyNames(e)}},3652:(e,t,r)=>{"use strict";const n=r(375),s={};t.Sorter=class{constructor(){this._items=[],this.nodes=[]}add(e,t){const r=[].concat((t=t||{}).before||[]),s=[].concat(t.after||[]),i=t.group||"?",o=t.sort||0;n(!r.includes(i),`Item cannot come before itself: ${i}`),n(!r.includes("?"),"Item cannot come before unassociated items"),n(!s.includes(i),`Item cannot come after itself: ${i}`),n(!s.includes("?"),"Item cannot come after unassociated items"),Array.isArray(e)||(e=[e]);for(const t of e){const e={seq:this._items.length,sort:o,before:r,after:s,group:i,node:t};this._items.push(e)}if(!t.manual){const e=this._sort();n(e,"item","?"!==i?`added into group ${i}`:"","created a dependencies error")}return this.nodes}merge(e){Array.isArray(e)||(e=[e]);for(const t of e)if(t)for(const e of t._items)this._items.push(Object.assign({},e));this._items.sort(s.mergeSort);for(let e=0;e<this._items.length;++e)this._items[e].seq=e;const t=this._sort();return n(t,"merge created a dependencies error"),this.nodes}sort(){const e=this._sort();return n(e,"sort created a dependencies error"),this.nodes}_sort(){const e={},t=Object.create(null),r=Object.create(null);for(const n of this._items){const s=n.seq,i=n.group;r[i]=r[i]||[],r[i].push(s),e[s]=n.before;for(const e of n.after)t[e]=t[e]||[],t[e].push(s)}for(const t in e){const n=[];for(const s in e[t]){const i=e[t][s];r[i]=r[i]||[],n.push(...r[i])}e[t]=n}for(const n in t)if(r[n])for(const s of r[n])e[s].push(...t[n]);const n={};for(const t in e){const r=e[t];for(const e of r)n[e]=n[e]||[],n[e].push(t)}const s={},i=[];for(let e=0;e<this._items.length;++e){let t=e;if(n[e]){t=null;for(let e=0;e<this._items.length;++e){if(!0===s[e])continue;n[e]||(n[e]=[]);const r=n[e].length;let i=0;for(let t=0;t<r;++t)s[n[e][t]]&&++i;if(i===r){t=e;break}}}null!==t&&(s[t]=!0,i.push(t))}if(i.length!==this._items.length)return!1;const o={};for(const e of this._items)o[e.seq]=e;this._items=[],this.nodes=[];for(const e of i){const t=o[e];this.nodes.push(t.node),this._items.push(t)}return!0}},s.mergeSort=(e,t)=>e.sort===t.sort?0:e.sort<t.sort?-1:1},5380:(e,t,r)=>{"use strict";const n=r(443),s=r(2178),i={minDomainSegments:2,nonAsciiRx:/[^\x00-\x7f]/,domainControlRx:/[\x00-\x20@\:\/\\#!\$&\'\(\)\*\+,;=\?]/,tldSegmentRx:/^[a-zA-Z](?:[a-zA-Z0-9\-]*[a-zA-Z0-9])?$/,domainSegmentRx:/^[a-zA-Z0-9](?:[a-zA-Z0-9\-]*[a-zA-Z0-9])?$/,URL:n.URL||URL};t.analyze=function(e,t={}){if(!e)return s.code("DOMAIN_NON_EMPTY_STRING");if("string"!=typeof e)throw new Error("Invalid input: domain must be a string");if(e.length>256)return s.code("DOMAIN_TOO_LONG");if(i.nonAsciiRx.test(e)){if(!1===t.allowUnicode)return s.code("DOMAIN_INVALID_UNICODE_CHARS");e=e.normalize("NFC")}if(i.domainControlRx.test(e))return s.code("DOMAIN_INVALID_CHARS");e=i.punycode(e),t.allowFullyQualified&&"."===e[e.length-1]&&(e=e.slice(0,-1));const r=t.minDomainSegments||i.minDomainSegments,n=e.split(".");if(n.length<r)return s.code("DOMAIN_SEGMENTS_COUNT");if(t.maxDomainSegments&&n.length>t.maxDomainSegments)return s.code("DOMAIN_SEGMENTS_COUNT_MAX");const o=t.tlds;if(o){const e=n[n.length-1].toLowerCase();if(o.deny&&o.deny.has(e)||o.allow&&!o.allow.has(e))return s.code("DOMAIN_FORBIDDEN_TLDS")}for(let e=0;e<n.length;++e){const t=n[e];if(!t.length)return s.code("DOMAIN_EMPTY_SEGMENT");if(t.length>63)return s.code("DOMAIN_LONG_SEGMENT");if(e<n.length-1){if(!i.domainSegmentRx.test(t))return s.code("DOMAIN_INVALID_CHARS")}else if(!i.tldSegmentRx.test(t))return s.code("DOMAIN_INVALID_TLDS_CHARS")}return null},t.isValid=function(e,r){return!t.analyze(e,r)},i.punycode=function(e){e.includes("%")&&(e=e.replace(/%/g,"%25"));try{return new i.URL(`http://${e}`).host}catch(t){return e}}},1745:(e,t,r)=>{"use strict";const n=r(9848),s=r(5380),i=r(2178),o={nonAsciiRx:/[^\x00-\x7f]/,encoder:new(n.TextEncoder||TextEncoder)};t.analyze=function(e,t){return o.email(e,t)},t.isValid=function(e,t){return!o.email(e,t)},o.email=function(e,t={}){if("string"!=typeof e)throw new Error("Invalid input: email must be a string");if(!e)return i.code("EMPTY_STRING");const r=!o.nonAsciiRx.test(e);if(!r){if(!1===t.allowUnicode)return i.code("FORBIDDEN_UNICODE");e=e.normalize("NFC")}const n=e.split("@");if(2!==n.length)return n.length>2?i.code("MULTIPLE_AT_CHAR"):i.code("MISSING_AT_CHAR");const[a,c]=n;if(!a)return i.code("EMPTY_LOCAL");if(!t.ignoreLength){if(e.length>254)return i.code("ADDRESS_TOO_LONG");if(o.encoder.encode(a).length>64)return i.code("LOCAL_TOO_LONG")}return o.local(a,r)||s.analyze(c,t)},o.local=function(e,t){const r=e.split(".");for(const e of r){if(!e.length)return i.code("EMPTY_LOCAL_SEGMENT");if(t){if(!o.atextRx.test(e))return i.code("INVALID_LOCAL_CHARS")}else for(const t of e){if(o.atextRx.test(t))continue;const e=o.binary(t);if(!o.atomRx.test(e))return i.code("INVALID_LOCAL_CHARS")}}},o.binary=function(e){return Array.from(o.encoder.encode(e)).map((e=>String.fromCharCode(e))).join("")},o.atextRx=/^[\w!#\$%&'\*\+\-/=\?\^`\{\|\}~]+$/,o.atomRx=new RegExp(["(?:[\\xc2-\\xdf][\\x80-\\xbf])","(?:\\xe0[\\xa0-\\xbf][\\x80-\\xbf])|(?:[\\xe1-\\xec][\\x80-\\xbf]{2})|(?:\\xed[\\x80-\\x9f][\\x80-\\xbf])|(?:[\\xee-\\xef][\\x80-\\xbf]{2})","(?:\\xf0[\\x90-\\xbf][\\x80-\\xbf]{2})|(?:[\\xf1-\\xf3][\\x80-\\xbf]{3})|(?:\\xf4[\\x80-\\x8f][\\x80-\\xbf]{2})"].join("|"))},2178:(e,t)=>{"use strict";t.codes={EMPTY_STRING:"Address must be a non-empty string",FORBIDDEN_UNICODE:"Address contains forbidden Unicode characters",MULTIPLE_AT_CHAR:"Address cannot contain more than one @ character",MISSING_AT_CHAR:"Address must contain one @ character",EMPTY_LOCAL:"Address local part cannot be empty",ADDRESS_TOO_LONG:"Address too long",LOCAL_TOO_LONG:"Address local part too long",EMPTY_LOCAL_SEGMENT:"Address local part contains empty dot-separated segment",INVALID_LOCAL_CHARS:"Address local part contains invalid character",DOMAIN_NON_EMPTY_STRING:"Domain must be a non-empty string",DOMAIN_TOO_LONG:"Domain too long",DOMAIN_INVALID_UNICODE_CHARS:"Domain contains forbidden Unicode characters",DOMAIN_INVALID_CHARS:"Domain contains invalid character",DOMAIN_INVALID_TLDS_CHARS:"Domain contains invalid tld character",DOMAIN_SEGMENTS_COUNT:"Domain lacks the minimum required number of segments",DOMAIN_SEGMENTS_COUNT_MAX:"Domain contains too many segments",DOMAIN_FORBIDDEN_TLDS:"Domain uses forbidden TLD",DOMAIN_EMPTY_SEGMENT:"Domain contains empty dot-separated segment",DOMAIN_LONG_SEGMENT:"Domain contains dot-separated segment that is too long"},t.code=function(e){return{code:e,error:t.codes[e]}}},9959:(e,t,r)=>{"use strict";const n=r(375),s=r(5752);t.regex=function(e={}){n(void 0===e.cidr||"string"==typeof e.cidr,"options.cidr must be a string");const t=e.cidr?e.cidr.toLowerCase():"optional";n(["required","optional","forbidden"].includes(t),"options.cidr must be one of required, optional, forbidden"),n(void 0===e.version||"string"==typeof e.version||Array.isArray(e.version),"options.version must be a string or an array of string");let r=e.version||["ipv4","ipv6","ipvfuture"];Array.isArray(r)||(r=[r]),n(r.length>=1,"options.version must have at least 1 version specified");for(let e=0;e<r.length;++e)n("string"==typeof r[e],"options.version must only contain strings"),r[e]=r[e].toLowerCase(),n(["ipv4","ipv6","ipvfuture"].includes(r[e]),"options.version contains unknown version "+r[e]+" - must be one of ipv4, ipv6, ipvfuture");r=Array.from(new Set(r));const i=`(?:${r.map((e=>{if("forbidden"===t)return s.ip[e];const r=`\\/${"ipv4"===e?s.ip.v4Cidr:s.ip.v6Cidr}`;return"required"===t?`${s.ip[e]}${r}`:`${s.ip[e]}(?:${r})?`})).join("|")})`,o=new RegExp(`^${i}$`);return{cidr:t,versions:r,regex:o,raw:i}}},5752:(e,t,r)=>{"use strict";const n=r(375),s=r(6064),i={generate:function(){const e={},t="\\dA-Fa-f",r="["+t+"]",n="\\w-\\.~",s="!\\$&'\\(\\)\\*\\+,;=",i="%"+t,o=n+i+s+":@",a="["+o+"]",c="(?:0{0,2}\\d|0?[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])";e.ipv4address="(?:"+c+"\\.){3}"+c;const u=r+"{1,4}",l="(?:"+u+":"+u+"|"+e.ipv4address+")",d="(?:"+u+":){6}"+l,f="::(?:"+u+":){5}"+l,p="(?:"+u+")?::(?:"+u+":){4}"+l,h="(?:(?:"+u+":){0,1}"+u+")?::(?:"+u+":){3}"+l,m="(?:(?:"+u+":){0,2}"+u+")?::(?:"+u+":){2}"+l,g="(?:(?:"+u+":){0,3}"+u+")?::"+u+":"+l,y="(?:(?:"+u+":){0,4}"+u+")?::"+l,v="(?:(?:"+u+":){0,5}"+u+")?::"+u,b="(?:(?:"+u+":){0,6}"+u+")?::";e.ipv4Cidr="(?:\\d|[1-2]\\d|3[0-2])",e.ipv6Cidr="(?:0{0,2}\\d|0?[1-9]\\d|1[01]\\d|12[0-8])",e.ipv6address="(?:"+d+"|"+f+"|"+p+"|"+h+"|"+m+"|"+g+"|"+y+"|"+v+"|"+b+")",e.ipvFuture="v"+r+"+\\.["+n+s+":]+",e.scheme="[a-zA-Z][a-zA-Z\\d+-\\.]*",e.schemeRegex=new RegExp(e.scheme);const w="["+n+i+s+":]*",_="["+n+i+s+"]{1,255}",x="(?:\\[(?:"+e.ipv6address+"|"+e.ipvFuture+")\\]|"+e.ipv4address+"|"+_+")",S="(?:"+w+"@)?"+x+"(?::\\d*)?",A="(?:"+w+"@)?("+x+")(?::\\d*)?",E=a+"*",k=a+"+",R="(?:\\/"+E+")*",I="\\/(?:"+k+R+")?",O=k+R,$="["+n+i+s+"@]+"+R,C="(?:\\/\\/\\/"+E+R+")";return e.hierPart="(?:(?:\\/\\/"+S+R+")|"+I+"|"+O+"|"+C+")",e.hierPartCapture="(?:(?:\\/\\/"+A+R+")|"+I+"|"+O+")",e.relativeRef="(?:(?:\\/\\/"+S+R+")|"+I+"|"+$+"|)",e.relativeRefCapture="(?:(?:\\/\\/"+A+R+")|"+I+"|"+$+"|)",e.query="["+o+"\\/\\?]*(?=#|$)",e.queryWithSquareBrackets="["+o+"\\[\\]\\/\\?]*(?=#|$)",e.fragment="["+o+"\\/\\?]*",e}};i.rfc3986=i.generate(),t.ip={v4Cidr:i.rfc3986.ipv4Cidr,v6Cidr:i.rfc3986.ipv6Cidr,ipv4:i.rfc3986.ipv4address,ipv6:i.rfc3986.ipv6address,ipvfuture:i.rfc3986.ipvFuture},i.createRegex=function(e){const t=i.rfc3986,r="(?:\\?"+(e.allowQuerySquareBrackets?t.queryWithSquareBrackets:t.query)+")?(?:#"+t.fragment+")?",o=e.domain?t.relativeRefCapture:t.relativeRef;if(e.relativeOnly)return i.wrap(o+r);let a="";if(e.scheme){n(e.scheme instanceof RegExp||"string"==typeof e.scheme||Array.isArray(e.scheme),"scheme must be a RegExp, String, or Array");const r=[].concat(e.scheme);n(r.length>=1,"scheme must have at least 1 scheme specified");const i=[];for(let e=0;e<r.length;++e){const o=r[e];n(o instanceof RegExp||"string"==typeof o,"scheme at position "+e+" must be a RegExp or String"),o instanceof RegExp?i.push(o.source.toString()):(n(t.schemeRegex.test(o),"scheme at position "+e+" must be a valid scheme"),i.push(s(o)))}a=i.join("|")}const c="(?:"+(a?"(?:"+a+")":t.scheme)+":"+(e.domain?t.hierPartCapture:t.hierPart)+")",u=e.allowRelative?"(?:"+c+"|"+o+")":c;return i.wrap(u+r,a)},i.wrap=function(e,t){return{raw:e=`(?=.)(?!https?:/(?:$|[^/]))(?!https?:///)(?!https?:[^/])${e}`,regex:new RegExp(`^${e}$`),scheme:t}},i.uriRegex=i.createRegex({}),t.regex=function(e={}){return e.scheme||e.allowRelative||e.relativeOnly||e.allowQuerySquareBrackets||e.domain?i.createRegex(e):i.uriRegex}},1447:(e,t)=>{"use strict";const r={operators:["!","^","*","/","%","+","-","<","<=",">",">=","==","!=","&&","||","??"],operatorCharacters:["!","^","*","/","%","+","-","<","=",">","&","|","?"],operatorsOrder:[["^"],["*","/","%"],["+","-"],["<","<=",">",">="],["==","!="],["&&"],["||","??"]],operatorsPrefix:["!","n"],literals:{'"':'"',"`":"`","'":"'","[":"]"},numberRx:/^(?:[0-9]*(\.[0-9]*)?){1}$/,tokenRx:/^[\w\$\#\.\@\:\{\}]+$/,symbol:Symbol("formula"),settings:Symbol("settings")};t.Parser=class{constructor(e,t={}){if(!t[r.settings]&&t.constants)for(const e in t.constants){const r=t.constants[e];if(null!==r&&!["boolean","number","string"].includes(typeof r))throw new Error(`Formula constant ${e} contains invalid ${typeof r} value type`)}this.settings=t[r.settings]?t:Object.assign({[r.settings]:!0,constants:{},functions:{}},t),this.single=null,this._parts=null,this._parse(e)}_parse(e){let n=[],s="",i=0,o=!1;const a=e=>{if(i)throw new Error("Formula missing closing parenthesis");const a=n.length?n[n.length-1]:null;if(o||s||e){if(a&&"reference"===a.type&&")"===e)return a.type="function",a.value=this._subFormula(s,a.value),void(s="");if(")"===e){const e=new t.Parser(s,this.settings);n.push({type:"segment",value:e})}else if(o){if("]"===o)return n.push({type:"reference",value:s}),void(s="");n.push({type:"literal",value:s})}else if(r.operatorCharacters.includes(s))a&&"operator"===a.type&&r.operators.includes(a.value+s)?a.value+=s:n.push({type:"operator",value:s});else if(s.match(r.numberRx))n.push({type:"constant",value:parseFloat(s)});else if(void 0!==this.settings.constants[s])n.push({type:"constant",value:this.settings.constants[s]});else{if(!s.match(r.tokenRx))throw new Error(`Formula contains invalid token: ${s}`);n.push({type:"reference",value:s})}s=""}};for(const t of e)o?t===o?(a(),o=!1):s+=t:i?"("===t?(s+=t,++i):")"===t?(--i,i?s+=t:a(t)):s+=t:t in r.literals?o=r.literals[t]:"("===t?(a(),++i):r.operatorCharacters.includes(t)?(a(),s=t,a()):" "!==t?s+=t:a();a(),n=n.map(((e,t)=>"operator"!==e.type||"-"!==e.value||t&&"operator"!==n[t-1].type?e:{type:"operator",value:"n"}));let c=!1;for(const e of n){if("operator"===e.type){if(r.operatorsPrefix.includes(e.value))continue;if(!c)throw new Error("Formula contains an operator in invalid position");if(!r.operators.includes(e.value))throw new Error(`Formula contains an unknown operator ${e.value}`)}else if(c)throw new Error("Formula missing expected operator");c=!c}if(!c)throw new Error("Formula contains invalid trailing operator");1===n.length&&["reference","literal","constant"].includes(n[0].type)&&(this.single={type:"reference"===n[0].type?"reference":"value",value:n[0].value}),this._parts=n.map((e=>{if("operator"===e.type)return r.operatorsPrefix.includes(e.value)?e:e.value;if("reference"!==e.type)return e.value;if(this.settings.tokenRx&&!this.settings.tokenRx.test(e.value))throw new Error(`Formula contains invalid reference ${e.value}`);return this.settings.reference?this.settings.reference(e.value):r.reference(e.value)}))}_subFormula(e,n){const s=this.settings.functions[n];if("function"!=typeof s)throw new Error(`Formula contains unknown function ${n}`);let i=[];if(e){let t="",s=0,o=!1;const a=()=>{if(!t)throw new Error(`Formula contains function ${n} with invalid arguments ${e}`);i.push(t),t=""};for(let n=0;n<e.length;++n){const i=e[n];o?(t+=i,i===o&&(o=!1)):i in r.literals&&!s?(t+=i,o=r.literals[i]):","!==i||s?(t+=i,"("===i?++s:")"===i&&--s):a()}a()}return i=i.map((e=>new t.Parser(e,this.settings))),function(e){const t=[];for(const r of i)t.push(r.evaluate(e));return s.call(e,...t)}}evaluate(e){const t=this._parts.slice();for(let n=t.length-2;n>=0;--n){const s=t[n];if(s&&"operator"===s.type){const i=t[n+1];t.splice(n+1,1);const o=r.evaluate(i,e);t[n]=r.single(s.value,o)}}return r.operatorsOrder.forEach((n=>{for(let s=1;s<t.length-1;)if(n.includes(t[s])){const n=t[s],i=r.evaluate(t[s-1],e),o=r.evaluate(t[s+1],e);t.splice(s,2);const a=r.calculate(n,i,o);t[s-1]=0===a?0:a}else s+=2})),r.evaluate(t[0],e)}},t.Parser.prototype[r.symbol]=!0,r.reference=function(e){return function(t){return t&&void 0!==t[e]?t[e]:null}},r.evaluate=function(e,t){return null===e?null:"function"==typeof e?e(t):e[r.symbol]?e.evaluate(t):e},r.single=function(e,t){if("!"===e)return!t;const r=-t;return 0===r?0:r},r.calculate=function(e,t,n){if("??"===e)return r.exists(t)?t:n;if("string"==typeof t||"string"==typeof n){if("+"===e)return(t=r.exists(t)?t:"")+(r.exists(n)?n:"")}else switch(e){case"^":return Math.pow(t,n);case"*":return t*n;case"/":return t/n;case"%":return t%n;case"+":return t+n;case"-":return t-n}switch(e){case"<":return t<n;case"<=":return t<=n;case">":return t>n;case">=":return t>=n;case"==":return t===n;case"!=":return t!==n;case"&&":return t&&n;case"||":return t||n}return null},r.exists=function(e){return null!=e}},9926:()=>{},5688:()=>{},9708:()=>{},1152:()=>{},443:()=>{},9848:()=>{},5934:e=>{"use strict";e.exports=JSON.parse('{"version":"17.13.3"}')}},t={},function r(n){var s=t[n];if(void 0!==s)return s.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}(5107);var e,t},e.exports=t()},7975:e=>{"use strict";function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",s=0,i=-1,o=0,a=0;a<=e.length;++a){if(a<e.length)r=e.charCodeAt(a);else{if(47===r)break;r=47}if(47===r){if(i===a-1||1===o);else if(i!==a-1&&2===o){if(n.length<2||2!==s||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var c=n.lastIndexOf("/");if(c!==n.length-1){-1===c?(n="",s=0):s=(n=n.slice(0,c)).length-1-n.lastIndexOf("/"),i=a,o=0;continue}}else if(2===n.length||1===n.length){n="",s=0,i=a,o=0;continue}t&&(n.length>0?n+="/..":n="..",s=2)}else n.length>0?n+="/"+e.slice(i+1,a):n=e.slice(i+1,a),s=a-i-1;i=a,o=0}else 46===r&&-1!==o?++o:o=-1}return n}var n={resolve:function(){for(var e,n="",s=!1,i=arguments.length-1;i>=-1&&!s;i--){var o;i>=0?o=arguments[i]:(void 0===e&&(e=process.cwd()),o=e),t(o),0!==o.length&&(n=o+"/"+n,s=47===o.charCodeAt(0))}return n=r(n,!s),s?n.length>0?"/"+n:"/":n.length>0?n:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),s=47===e.charCodeAt(e.length-1);return 0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&s&&(e+="/"),n?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var s=arguments[r];t(s),s.length>0&&(void 0===e?e=s:e+="/"+s)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r)return"";if((e=n.resolve(e))===(r=n.resolve(r)))return"";for(var s=1;s<e.length&&47===e.charCodeAt(s);++s);for(var i=e.length,o=i-s,a=1;a<r.length&&47===r.charCodeAt(a);++a);for(var c=r.length-a,u=o<c?o:c,l=-1,d=0;d<=u;++d){if(d===u){if(c>u){if(47===r.charCodeAt(a+d))return r.slice(a+d+1);if(0===d)return r.slice(a+d)}else o>u&&(47===e.charCodeAt(s+d)?l=d:0===d&&(l=0));break}var f=e.charCodeAt(s+d);if(f!==r.charCodeAt(a+d))break;47===f&&(l=d)}var p="";for(d=s+l+1;d<=i;++d)d!==i&&47!==e.charCodeAt(d)||(0===p.length?p+="..":p+="/..");return p.length>0?p+r.slice(a+l):(a+=l,47===r.charCodeAt(a)&&++a,r.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,s=-1,i=!0,o=e.length-1;o>=1;--o)if(47===(r=e.charCodeAt(o))){if(!i){s=o;break}}else i=!1;return-1===s?n?"/":".":n&&1===s?"//":e.slice(0,s)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw new TypeError('"ext" argument must be a string');t(e);var n,s=0,i=-1,o=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var a=r.length-1,c=-1;for(n=e.length-1;n>=0;--n){var u=e.charCodeAt(n);if(47===u){if(!o){s=n+1;break}}else-1===c&&(o=!1,c=n+1),a>=0&&(u===r.charCodeAt(a)?-1==--a&&(i=n):(a=-1,i=c))}return s===i?i=c:-1===i&&(i=e.length),e.slice(s,i)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!o){s=n+1;break}}else-1===i&&(o=!1,i=n+1);return-1===i?"":e.slice(s,i)},extname:function(e){t(e);for(var r=-1,n=0,s=-1,i=!0,o=0,a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(47!==c)-1===s&&(i=!1,s=a+1),46===c?-1===r?r=a:1!==o&&(o=1):-1!==r&&(o=-1);else if(!i){n=a+1;break}}return-1===r||-1===s||0===o||1===o&&r===s-1&&r===n+1?"":e.slice(r,s)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var r=t.dir||t.root,n=t.base||(t.name||"")+(t.ext||"");return r?r===t.root?r+n:r+"/"+n:n}(0,e)},parse:function(e){t(e);var r={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return r;var n,s=e.charCodeAt(0),i=47===s;i?(r.root="/",n=1):n=0;for(var o=-1,a=0,c=-1,u=!0,l=e.length-1,d=0;l>=n;--l)if(47!==(s=e.charCodeAt(l)))-1===c&&(u=!1,c=l+1),46===s?-1===o?o=l:1!==d&&(d=1):-1!==o&&(d=-1);else if(!u){a=l+1;break}return-1===o||-1===c||0===d||1===d&&o===c-1&&o===a+1?-1!==c&&(r.base=r.name=0===a&&i?e.slice(1,c):e.slice(a,c)):(0===a&&i?(r.name=e.slice(1,o),r.base=e.slice(1,c)):(r.name=e.slice(a,o),r.base=e.slice(a,c)),r.ext=e.slice(o,c)),a>0?r.dir=e.slice(0,a-1):i&&(r.dir="/"),r},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n},2424:(e,t,r)=>{"use strict";var n=r(6743),s=/[\/\?<>\\:\*\|"]/g,i=/[\x00-\x1f\x80-\x9f]/g,o=/^\.+$/,a=/^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i,c=/[\. ]+$/;function u(e,t){if("string"!=typeof e)throw new Error("Input must be string");var r=e.replace(s,t).replace(i,t).replace(o,t).replace(a,t).replace(c,t);return n(r,255)}e.exports=function(e,t){var r=t&&t.replacement||"",n=u(e,r);return""===r?n:u(n,"")}},6743:(e,t,r)=>{"use strict";var n=r(5045),s=r(8446);e.exports=n.bind(null,s)},5045:e=>{"use strict";function t(e){return e>=55296&&e<=56319}function r(e){return e>=56320&&e<=57343}e.exports=function(e,n,s){if("string"!=typeof n)throw new Error("Input must be string");for(var i,o,a=n.length,c=0,u=0;u<a;u+=1){if(i=n.charCodeAt(u),o=n[u],t(i)&&r(n.charCodeAt(u+1))&&(o+=n[u+=1]),(c+=e(o))===s)return n.slice(0,u+1);if(c>s)return n.slice(0,u-o.length+1)}return n}},8446:e=>{"use strict";function t(e){return e>=55296&&e<=56319}function r(e){return e>=56320&&e<=57343}e.exports=function(e){if("string"!=typeof e)throw new Error("Input must be string");for(var n=e.length,s=0,i=null,o=null,a=0;a<n;a++)r(i=e.charCodeAt(a))?null!=o&&t(o)?s+=1:s+=3:i<=127?s+=1:i>=128&&i<=2047?s+=2:i>=2048&&i<=65535&&(s+=3),o=i;return s}},6815:function(e,t){var r,n;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self&&self,r=function(e){"use strict";if(!(globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.id))throw new Error("This script should only be loaded in a browser extension.");if(globalThis.browser&&globalThis.browser.runtime&&globalThis.browser.runtime.id)e.exports=globalThis.browser;else{const t="The message port closed before a response was received.",r=e=>{const r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(0===Object.keys(r).length)throw new Error("api-metadata.json has not been included in browser-polyfill");class n extends WeakMap{constructor(e,t=void 0){super(t),this.createItem=e}get(e){return this.has(e)||this.set(e,this.createItem(e)),super.get(e)}}const s=(t,r)=>(...n)=>{e.runtime.lastError?t.reject(new Error(e.runtime.lastError.message)):r.singleCallbackArg||n.length<=1&&!1!==r.singleCallbackArg?t.resolve(n[0]):t.resolve(n)},i=e=>1==e?"argument":"arguments",o=(e,t,r)=>new Proxy(t,{apply:(t,n,s)=>r.call(n,e,...s)});let a=Function.call.bind(Object.prototype.hasOwnProperty);const c=(e,t={},r={})=>{let n=Object.create(null),u={has:(t,r)=>r in e||r in n,get(u,l,d){if(l in n)return n[l];if(!(l in e))return;let f=e[l];if("function"==typeof f)if("function"==typeof t[l])f=o(e,e[l],t[l]);else if(a(r,l)){let t=((e,t)=>function(r,...n){if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${i(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${i(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((i,o)=>{if(t.fallbackToNoCallback)try{r[e](...n,s({resolve:i,reject:o},t))}catch(s){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,s),r[e](...n),t.fallbackToNoCallback=!1,t.noCallback=!0,i()}else t.noCallback?(r[e](...n),i()):r[e](...n,s({resolve:i,reject:o},t))}))})(l,r[l]);f=o(e,e[l],t)}else f=f.bind(e);else if("object"==typeof f&&null!==f&&(a(t,l)||a(r,l)))f=c(f,t[l],r[l]);else{if(!a(r,"*"))return Object.defineProperty(n,l,{configurable:!0,enumerable:!0,get:()=>e[l],set(t){e[l]=t}}),f;f=c(f,t[l],r["*"])}return n[l]=f,f},set:(t,r,s,i)=>(r in n?n[r]=s:e[r]=s,!0),defineProperty:(e,t,r)=>Reflect.defineProperty(n,t,r),deleteProperty:(e,t)=>Reflect.deleteProperty(n,t)},l=Object.create(e);return new Proxy(l,u)},u=e=>({addListener(t,r,...n){t.addListener(e.get(r),...n)},hasListener:(t,r)=>t.hasListener(e.get(r)),removeListener(t,r){t.removeListener(e.get(r))}}),l=new n((e=>"function"!=typeof e?e:function(t){const r=c(t,{},{getContent:{minArgs:0,maxArgs:0}});e(r)})),d=new n((e=>"function"!=typeof e?e:function(t,r,n){let s,i,o=!1,a=new Promise((e=>{s=function(t){o=!0,e(t)}}));try{i=e(t,r,s)}catch(e){i=Promise.reject(e)}const c=!0!==i&&((u=i)&&"object"==typeof u&&"function"==typeof u.then);var u;if(!0!==i&&!c&&!o)return!1;return(c?i:a).then((e=>{n(e)}),(e=>{let t;t=e&&(e instanceof Error||"string"==typeof e.message)?e.message:"An unexpected error occurred",n({__mozWebExtensionPolyfillReject__:!0,message:t})})).catch((e=>{console.error("Failed to send onMessage rejected reply",e)})),!0})),f=({reject:r,resolve:n},s)=>{e.runtime.lastError?e.runtime.lastError.message===t?n():r(new Error(e.runtime.lastError.message)):s&&s.__mozWebExtensionPolyfillReject__?r(new Error(s.message)):n(s)},p=(e,t,r,...n)=>{if(n.length<t.minArgs)throw new Error(`Expected at least ${t.minArgs} ${i(t.minArgs)} for ${e}(), got ${n.length}`);if(n.length>t.maxArgs)throw new Error(`Expected at most ${t.maxArgs} ${i(t.maxArgs)} for ${e}(), got ${n.length}`);return new Promise(((e,t)=>{const s=f.bind(null,{resolve:e,reject:t});n.push(s),r.sendMessage(...n)}))},h={devtools:{network:{onRequestFinished:u(l)}},runtime:{onMessage:u(d),onMessageExternal:u(d),sendMessage:p.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:p.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},m={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":m},services:{"*":m},websites:{"*":m}},c(e,h,r)};e.exports=r(chrome)}},void 0===(n=r.apply(t,[e]))||(e.exports=n)},9306:(e,t,r)=>{"use strict";var n=r(4901),s=r(6823),i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(s(e)+" is not a function")}},6194:(e,t,r)=>{"use strict";var n=r(2248).has;e.exports=function(e){return n(e),e}},7080:(e,t,r)=>{"use strict";var n=r(4402).has;e.exports=function(e){return n(e),e}},6469:(e,t,r)=>{"use strict";var n=r(8227),s=r(2360),i=r(4913).f,o=n("unscopables"),a=Array.prototype;void 0===a[o]&&i(a,o,{configurable:!0,value:s(null)}),e.exports=function(e){a[o][e]=!0}},679:(e,t,r)=>{"use strict";var n=r(1625),s=TypeError;e.exports=function(e,t){if(n(t,e))return e;throw new s("Incorrect invocation")}},8551:(e,t,r)=>{"use strict";var n=r(34),s=String,i=TypeError;e.exports=function(e){if(n(e))return e;throw new i(s(e)+" is not an object")}},5370:(e,t,r)=>{"use strict";var n=r(6198);e.exports=function(e,t,r){for(var s=0,i=arguments.length>2?r:n(t),o=new e(i);i>s;)o[s]=t[s++];return o}},7957:(e,t,r)=>{"use strict";var n=r(6080),s=r(9504),i=r(7055),o=r(8981),a=r(6969),c=r(6198),u=r(2360),l=r(5370),d=Array,f=s([].push);e.exports=function(e,t,r,s){for(var p,h,m,g=o(e),y=i(g),v=n(t,r),b=u(null),w=c(y),_=0;w>_;_++)m=y[_],(h=a(v(m,_,g)))in b?f(b[h],m):b[h]=[m];if(s&&(p=s(g))!==d)for(h in b)b[h]=l(p,b[h]);return b}},9617:(e,t,r)=>{"use strict";var n=r(5397),s=r(5610),i=r(6198),o=function(e){return function(t,r,o){var a=n(t),c=i(a);if(0===c)return!e&&-1;var u,l=s(o,c);if(e&&r!=r){for(;c>l;)if((u=a[l++])!=u)return!0}else for(;c>l;l++)if((e||l in a)&&a[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:o(!0),indexOf:o(!1)}},4598:(e,t,r)=>{"use strict";var n=r(9039);e.exports=function(e,t){var r=[][e];return!!r&&n((function(){r.call(null,t||function(){return 1},1)}))}},4527:(e,t,r)=>{"use strict";var n=r(3724),s=r(4376),i=TypeError,o=Object.getOwnPropertyDescriptor,a=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(s(e)&&!o(e,"length").writable)throw new i("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},772:(e,t,r)=>{"use strict";var n=r(9565),s=r(7751),i=r(5966);e.exports=function(e,t,r,o){try{var a=i(e,"return");if(a)return s("Promise").resolve(n(a,e)).then((function(){t(r)}),(function(e){o(e)}))}catch(e){return o(e)}t(r)}},2059:(e,t,r)=>{"use strict";var n=r(9565),s=r(1103),i=r(8551),o=r(2360),a=r(6699),c=r(6279),u=r(8227),l=r(1181),d=r(7751),f=r(5966),p=r(3982),h=r(2529),m=r(9539),g=d("Promise"),y=u("toStringTag"),v="AsyncIteratorHelper",b="WrapForValidAsyncIterator",w=l.set,_=function(e){var t=!e,r=l.getterFor(e?b:v),a=function(e){var n=s((function(){return r(e)})),i=n.error,o=n.value;return i||t&&o.done?{exit:!0,value:i?g.reject(o):g.resolve(h(void 0,!0))}:{exit:!1,value:o}};return c(o(p),{next:function(){var e=a(this),t=e.value;if(e.exit)return t;var r=s((function(){return i(t.nextHandler(g))})),n=r.error,o=r.value;return n&&(t.done=!0),n?g.reject(o):g.resolve(o)},return:function(){var t=a(this),r=t.value;if(t.exit)return r;r.done=!0;var o,c,u=r.iterator,l=s((function(){if(r.inner)try{m(r.inner.iterator,"normal")}catch(e){return m(u,"throw",e)}return f(u,"return")}));return o=c=l.value,l.error?g.reject(c):void 0===o?g.resolve(h(void 0,!0)):(c=(l=s((function(){return n(o,u)}))).value,l.error?g.reject(c):e?g.resolve(c):g.resolve(c).then((function(e){return i(e),h(void 0,!0)})))}})},x=_(!0),S=_(!1);a(S,y,"Async Iterator Helper"),e.exports=function(e,t){var r=function(r,n){n?(n.iterator=r.iterator,n.next=r.next):n=r,n.type=t?b:v,n.nextHandler=e,n.counter=0,n.done=!1,w(this,n)};return r.prototype=t?x:S,r}},6639:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),i=r(8551),o=r(34),a=r(6837),c=r(7751),u=r(1767),l=r(772),d=function(e){var t=0===e,r=1===e,d=2===e,f=3===e;return function(e,p,h){i(e);var m=void 0!==p;!m&&t||s(p);var g=u(e),y=c("Promise"),v=g.iterator,b=g.next,w=0;return new y((function(e,s){var c=function(e){l(v,s,e,s)},u=function(){try{if(m)try{a(w)}catch(e){c(e)}y.resolve(i(n(b,v))).then((function(n){try{if(i(n).done)t?(h.length=w,e(h)):e(!f&&(d||void 0));else{var a=n.value;try{if(m){var g=p(a,w),b=function(n){if(r)u();else if(d)n?u():l(v,e,!1,s);else if(t)try{h[w++]=n,u()}catch(e){c(e)}else n?l(v,e,f||a,s):u()};o(g)?y.resolve(g).then(b,c):b(g)}else h[w++]=a,u()}catch(e){c(e)}}}catch(e){s(e)}}),s)}catch(e){s(e)}};u()}))}};e.exports={toArray:d(0),forEach:d(1),every:d(2),some:d(3),find:d(4)}},1750:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),i=r(8551),o=r(34),a=r(1767),c=r(2059),u=r(2529),l=r(772),d=c((function(e){var t=this,r=t.iterator,s=t.mapper;return new e((function(a,c){var d=function(e){t.done=!0,c(e)},f=function(e){l(r,d,e,d)};e.resolve(i(n(t.next,r))).then((function(r){try{if(i(r).done)t.done=!0,a(u(void 0,!0));else{var n=r.value;try{var c=s(n,t.counter++),l=function(e){a(u(e,!1))};o(c)?e.resolve(c).then(l,f):l(c)}catch(e){f(e)}}}catch(e){d(e)}}),d)}))}));e.exports=function(e){return i(this),s(e),new d(a(this),{mapper:e})}},3982:(e,t,r)=>{"use strict";var n,s,i=r(4576),o=r(7629),a=r(4901),c=r(2360),u=r(2787),l=r(6840),d=r(8227),f=r(6395),p="USE_FUNCTION_CONSTRUCTOR",h=d("asyncIterator"),m=i.AsyncIterator,g=o.AsyncIteratorPrototype;if(g)n=g;else if(a(m))n=m.prototype;else if(o[p]||i[p])try{s=u(u(u(Function("return async function*(){}()")()))),u(s)===Object.prototype&&(n=s)}catch(e){}n?f&&(n=c(n)):n={},a(n[h])||l(n,h,(function(){return this})),e.exports=n},6319:(e,t,r)=>{"use strict";var n=r(8551),s=r(9539);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(t){s(e,"throw",t)}}},2195:(e,t,r)=>{"use strict";var n=r(9504),s=n({}.toString),i=n("".slice);e.exports=function(e){return i(s(e),8,-1)}},6955:(e,t,r)=>{"use strict";var n=r(2140),s=r(4901),i=r(2195),o=r(8227)("toStringTag"),a=Object,c="Arguments"===i(function(){return arguments}());e.exports=n?i:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(e){}}(t=a(e),o))?r:c?i(t):"Object"===(n=i(t))&&s(t.callee)?"Arguments":n}},7740:(e,t,r)=>{"use strict";var n=r(9297),s=r(5031),i=r(7347),o=r(4913);e.exports=function(e,t,r){for(var a=s(t),c=o.f,u=i.f,l=0;l<a.length;l++){var d=a[l];n(e,d)||r&&n(r,d)||c(e,d,u(t,d))}}},2211:(e,t,r)=>{"use strict";var n=r(9039);e.exports=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},2529:e=>{"use strict";e.exports=function(e,t){return{value:e,done:t}}},6699:(e,t,r)=>{"use strict";var n=r(3724),s=r(4913),i=r(6980);e.exports=n?function(e,t,r){return s.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},6980:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},4659:(e,t,r)=>{"use strict";var n=r(3724),s=r(4913),i=r(6980);e.exports=function(e,t,r){n?s.f(e,t,i(0,r)):e[t]=r}},2106:(e,t,r)=>{"use strict";var n=r(283),s=r(4913);e.exports=function(e,t,r){return r.get&&n(r.get,t,{getter:!0}),r.set&&n(r.set,t,{setter:!0}),s.f(e,t,r)}},6840:(e,t,r)=>{"use strict";var n=r(4901),s=r(4913),i=r(283),o=r(9433);e.exports=function(e,t,r,a){a||(a={});var c=a.enumerable,u=void 0!==a.name?a.name:t;if(n(r)&&i(r,u,a),a.global)c?e[t]=r:o(t,r);else{try{a.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=r:s.f(e,t,{value:r,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},6279:(e,t,r)=>{"use strict";var n=r(6840);e.exports=function(e,t,r){for(var s in t)n(e,s,t[s],r);return e}},9433:(e,t,r)=>{"use strict";var n=r(4576),s=Object.defineProperty;e.exports=function(e,t){try{s(n,e,{value:t,configurable:!0,writable:!0})}catch(r){n[e]=t}return t}},3724:(e,t,r)=>{"use strict";var n=r(9039);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4055:(e,t,r)=>{"use strict";var n=r(4576),s=r(34),i=n.document,o=s(i)&&s(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},6837:e=>{"use strict";var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},8727:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2839:(e,t,r)=>{"use strict";var n=r(4576).navigator,s=n&&n.userAgent;e.exports=s?String(s):""},9519:(e,t,r)=>{"use strict";var n,s,i=r(4576),o=r(2839),a=i.process,c=i.Deno,u=a&&a.versions||c&&c.version,l=u&&u.v8;l&&(s=(n=l.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!s&&o&&(!(n=o.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=o.match(/Chrome\/(\d+)/))&&(s=+n[1]),e.exports=s},6518:(e,t,r)=>{"use strict";var n=r(4576),s=r(7347).f,i=r(6699),o=r(6840),a=r(9433),c=r(7740),u=r(2796);e.exports=function(e,t){var r,l,d,f,p,h=e.target,m=e.global,g=e.stat;if(r=m?n:g?n[h]||a(h,{}):n[h]&&n[h].prototype)for(l in t){if(f=t[l],d=e.dontCallGetSet?(p=s(r,l))&&p.value:r[l],!u(m?l:h+(g?".":"#")+l,e.forced)&&void 0!==d){if(typeof f==typeof d)continue;c(f,d)}(e.sham||d&&d.sham)&&i(f,"sham",!0),o(r,l,f,e)}}},9039:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(e){return!0}}},6080:(e,t,r)=>{"use strict";var n=r(7476),s=r(9306),i=r(616),o=n(n.bind);e.exports=function(e,t){return s(e),void 0===t?e:i?o(e,t):function(){return e.apply(t,arguments)}}},616:(e,t,r)=>{"use strict";var n=r(9039);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},9565:(e,t,r)=>{"use strict";var n=r(616),s=Function.prototype.call;e.exports=n?s.bind(s):function(){return s.apply(s,arguments)}},350:(e,t,r)=>{"use strict";var n=r(3724),s=r(9297),i=Function.prototype,o=n&&Object.getOwnPropertyDescriptor,a=s(i,"name"),c=a&&"something"===function(){}.name,u=a&&(!n||n&&o(i,"name").configurable);e.exports={EXISTS:a,PROPER:c,CONFIGURABLE:u}},6706:(e,t,r)=>{"use strict";var n=r(9504),s=r(9306);e.exports=function(e,t,r){try{return n(s(Object.getOwnPropertyDescriptor(e,t)[r]))}catch(e){}}},7476:(e,t,r)=>{"use strict";var n=r(2195),s=r(9504);e.exports=function(e){if("Function"===n(e))return s(e)}},9504:(e,t,r)=>{"use strict";var n=r(616),s=Function.prototype,i=s.call,o=n&&s.bind.bind(i,i);e.exports=n?o:function(e){return function(){return i.apply(e,arguments)}}},7751:(e,t,r)=>{"use strict";var n=r(4576),s=r(4901);e.exports=function(e,t){return arguments.length<2?(r=n[e],s(r)?r:void 0):n[e]&&n[e][t];var r}},1767:e=>{"use strict";e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},851:(e,t,r)=>{"use strict";var n=r(6955),s=r(5966),i=r(4117),o=r(6269),a=r(8227)("iterator");e.exports=function(e){if(!i(e))return s(e,a)||s(e,"@@iterator")||o[n(e)]}},81:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),i=r(8551),o=r(6823),a=r(851),c=TypeError;e.exports=function(e,t){var r=arguments.length<2?a(e):t;if(s(r))return i(n(r,e));throw new c(o(e)+" is not iterable")}},5966:(e,t,r)=>{"use strict";var n=r(9306),s=r(4117);e.exports=function(e,t){var r=e[t];return s(r)?void 0:n(r)}},3789:(e,t,r)=>{"use strict";var n=r(9306),s=r(8551),i=r(9565),o=r(1291),a=r(1767),c="Invalid size",u=RangeError,l=TypeError,d=Math.max,f=function(e,t){this.set=e,this.size=d(t,0),this.has=n(e.has),this.keys=n(e.keys)};f.prototype={getIterator:function(){return a(s(i(this.keys,this.set)))},includes:function(e){return i(this.has,this.set,e)}},e.exports=function(e){s(e);var t=+e.size;if(t!=t)throw new l(c);var r=o(t);if(r<0)throw new u(c);return new f(e,r)}},4576:function(e,t,r){"use strict";var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:(e,t,r)=>{"use strict";var n=r(9504),s=r(8981),i=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(s(e),t)}},421:e=>{"use strict";e.exports={}},397:(e,t,r)=>{"use strict";var n=r(7751);e.exports=n("document","documentElement")},5917:(e,t,r)=>{"use strict";var n=r(3724),s=r(9039),i=r(4055);e.exports=!n&&!s((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},7055:(e,t,r)=>{"use strict";var n=r(9504),s=r(9039),i=r(2195),o=Object,a=n("".split);e.exports=s((function(){return!o("z").propertyIsEnumerable(0)}))?function(e){return"String"===i(e)?a(e,""):o(e)}:o},3706:(e,t,r)=>{"use strict";var n=r(9504),s=r(4901),i=r(7629),o=n(Function.toString);s(i.inspectSource)||(i.inspectSource=function(e){return o(e)}),e.exports=i.inspectSource},1181:(e,t,r)=>{"use strict";var n,s,i,o=r(8622),a=r(4576),c=r(34),u=r(6699),l=r(9297),d=r(7629),f=r(6119),p=r(421),h="Object already initialized",m=a.TypeError,g=a.WeakMap;if(o||d.state){var y=d.state||(d.state=new g);y.get=y.get,y.has=y.has,y.set=y.set,n=function(e,t){if(y.has(e))throw new m(h);return t.facade=e,y.set(e,t),t},s=function(e){return y.get(e)||{}},i=function(e){return y.has(e)}}else{var v=f("state");p[v]=!0,n=function(e,t){if(l(e,v))throw new m(h);return t.facade=e,u(e,v,t),t},s=function(e){return l(e,v)?e[v]:{}},i=function(e){return l(e,v)}}e.exports={set:n,get:s,has:i,enforce:function(e){return i(e)?s(e):n(e,{})},getterFor:function(e){return function(t){var r;if(!c(t)||(r=s(t)).type!==e)throw new m("Incompatible receiver, "+e+" required");return r}}}},4209:(e,t,r)=>{"use strict";var n=r(8227),s=r(6269),i=n("iterator"),o=Array.prototype;e.exports=function(e){return void 0!==e&&(s.Array===e||o[i]===e)}},4376:(e,t,r)=>{"use strict";var n=r(2195);e.exports=Array.isArray||function(e){return"Array"===n(e)}},4901:e=>{"use strict";var t="object"==typeof document&&document.all;e.exports=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},2796:(e,t,r)=>{"use strict";var n=r(9039),s=r(4901),i=/#|\.prototype\./,o=function(e,t){var r=c[a(e)];return r===l||r!==u&&(s(t)?n(t):!!t)},a=o.normalize=function(e){return String(e).replace(i,".").toLowerCase()},c=o.data={},u=o.NATIVE="N",l=o.POLYFILL="P";e.exports=o},1563:(e,t,r)=>{"use strict";var n=r(6955),s=r(9297),i=r(4117),o=r(8227),a=r(6269),c=o("iterator"),u=Object;e.exports=function(e){if(i(e))return!1;var t=u(e);return void 0!==t[c]||"@@iterator"in t||s(a,n(t))}},4117:e=>{"use strict";e.exports=function(e){return null==e}},34:(e,t,r)=>{"use strict";var n=r(4901);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},6395:e=>{"use strict";e.exports=!1},757:(e,t,r)=>{"use strict";var n=r(7751),s=r(4901),i=r(1625),o=r(7040),a=Object;e.exports=o?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return s(t)&&i(t.prototype,a(e))}},507:(e,t,r)=>{"use strict";var n=r(9565);e.exports=function(e,t,r){for(var s,i,o=r?e:e.iterator,a=e.next;!(s=n(a,o)).done;)if(void 0!==(i=t(s.value)))return i}},2652:(e,t,r)=>{"use strict";var n=r(6080),s=r(9565),i=r(8551),o=r(6823),a=r(4209),c=r(6198),u=r(1625),l=r(81),d=r(851),f=r(9539),p=TypeError,h=function(e,t){this.stopped=e,this.result=t},m=h.prototype;e.exports=function(e,t,r){var g,y,v,b,w,_,x,S=r&&r.that,A=!(!r||!r.AS_ENTRIES),E=!(!r||!r.IS_RECORD),k=!(!r||!r.IS_ITERATOR),R=!(!r||!r.INTERRUPTED),I=n(t,S),O=function(e){return g&&f(g,"normal",e),new h(!0,e)},$=function(e){return A?(i(e),R?I(e[0],e[1],O):I(e[0],e[1])):R?I(e,O):I(e)};if(E)g=e.iterator;else if(k)g=e;else{if(!(y=d(e)))throw new p(o(e)+" is not iterable");if(a(y)){for(v=0,b=c(e);b>v;v++)if((w=$(e[v]))&&u(m,w))return w;return new h(!1)}g=l(e,y)}for(_=E?e.next:g.next;!(x=s(_,g)).done;){try{w=$(x.value)}catch(e){f(g,"throw",e)}if("object"==typeof w&&w&&u(m,w))return w}return new h(!1)}},9539:(e,t,r)=>{"use strict";var n=r(9565),s=r(8551),i=r(5966);e.exports=function(e,t,r){var o,a;s(e);try{if(!(o=i(e,"return"))){if("throw"===t)throw r;return r}o=n(o,e)}catch(e){a=!0,o=e}if("throw"===t)throw r;if(a)throw o;return s(o),r}},9462:(e,t,r)=>{"use strict";var n=r(9565),s=r(2360),i=r(6699),o=r(6279),a=r(8227),c=r(1181),u=r(5966),l=r(7657).IteratorPrototype,d=r(2529),f=r(9539),p=a("toStringTag"),h="IteratorHelper",m="WrapForValidIterator",g=c.set,y=function(e){var t=c.getterFor(e?m:h);return o(s(l),{next:function(){var r=t(this);if(e)return r.nextHandler();if(r.done)return d(void 0,!0);try{var n=r.nextHandler();return r.returnHandlerResult?n:d(n,r.done)}catch(e){throw r.done=!0,e}},return:function(){var r=t(this),s=r.iterator;if(r.done=!0,e){var i=u(s,"return");return i?n(i,s):d(void 0,!0)}if(r.inner)try{f(r.inner.iterator,"normal")}catch(e){return f(s,"throw",e)}return s&&f(s,"normal"),d(void 0,!0)}})},v=y(!0),b=y(!1);i(b,p,"Iterator Helper"),e.exports=function(e,t,r){var n=function(n,s){s?(s.iterator=n.iterator,s.next=n.next):s=n,s.type=t?m:h,s.returnHandlerResult=!!r,s.nextHandler=e,s.counter=0,s.done=!1,g(this,s)};return n.prototype=t?v:b,n}},713:(e,t,r)=>{"use strict";var n=r(9565),s=r(9306),i=r(8551),o=r(1767),a=r(9462),c=r(6319),u=a((function(){var e=this.iterator,t=i(n(this.next,e));if(!(this.done=!!t.done))return c(e,this.mapper,[t.value,this.counter++],!0)}));e.exports=function(e){return i(this),s(e),new u(o(this),{mapper:e})}},7657:(e,t,r)=>{"use strict";var n,s,i,o=r(9039),a=r(4901),c=r(34),u=r(2360),l=r(2787),d=r(6840),f=r(8227),p=r(6395),h=f("iterator"),m=!1;[].keys&&("next"in(i=[].keys())?(s=l(l(i)))!==Object.prototype&&(n=s):m=!0),!c(n)||o((function(){var e={};return n[h].call(e)!==e}))?n={}:p&&(n=u(n)),a(n[h])||d(n,h,(function(){return this})),e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:m}},6269:e=>{"use strict";e.exports={}},6198:(e,t,r)=>{"use strict";var n=r(8014);e.exports=function(e){return n(e.length)}},283:(e,t,r)=>{"use strict";var n=r(9504),s=r(9039),i=r(4901),o=r(9297),a=r(3724),c=r(350).CONFIGURABLE,u=r(3706),l=r(1181),d=l.enforce,f=l.get,p=String,h=Object.defineProperty,m=n("".slice),g=n("".replace),y=n([].join),v=a&&!s((function(){return 8!==h((function(){}),"length",{value:8}).length})),b=String(String).split("String"),w=e.exports=function(e,t,r){"Symbol("===m(p(t),0,7)&&(t="["+g(p(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(t="get "+t),r&&r.setter&&(t="set "+t),(!o(e,"name")||c&&e.name!==t)&&(a?h(e,"name",{value:t,configurable:!0}):e.name=t),v&&r&&o(r,"arity")&&e.length!==r.arity&&h(e,"length",{value:r.arity});try{r&&o(r,"constructor")&&r.constructor?a&&h(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var n=d(e);return o(n,"source")||(n.source=y(b,"string"==typeof t?t:"")),e};Function.prototype.toString=w((function(){return i(this)&&f(this).source||u(this)}),"toString")},2248:(e,t,r)=>{"use strict";var n=r(9504),s=Map.prototype;e.exports={Map,set:n(s.set),get:n(s.get),has:n(s.has),remove:n(s.delete),proto:s}},6223:(e,t,r)=>{"use strict";var n=r(9504),s=r(507),i=r(2248),o=i.Map,a=i.proto,c=n(a.forEach),u=n(a.entries),l=u(new o).next;e.exports=function(e,t,r){return r?s({iterator:u(e),next:l},(function(e){return t(e[1],e[0])})):c(e,t)}},741:e=>{"use strict";var t=Math.ceil,r=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?r:t)(n)}},2360:(e,t,r)=>{"use strict";var n,s=r(8551),i=r(6801),o=r(8727),a=r(421),c=r(397),u=r(4055),l=r(6119),d="prototype",f="script",p=l("IE_PROTO"),h=function(){},m=function(e){return"<"+f+">"+e+"</"+f+">"},g=function(e){e.write(m("")),e.close();var t=e.parentWindow.Object;return e=null,t},y=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}var e,t,r;y="undefined"!=typeof document?document.domain&&n?g(n):(t=u("iframe"),r="java"+f+":",t.style.display="none",c.appendChild(t),t.src=String(r),(e=t.contentWindow.document).open(),e.write(m("document.F=Object")),e.close(),e.F):g(n);for(var s=o.length;s--;)delete y[d][o[s]];return y()};a[p]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(h[d]=s(e),r=new h,h[d]=null,r[p]=e):r=y(),void 0===t?r:i.f(r,t)}},6801:(e,t,r)=>{"use strict";var n=r(3724),s=r(8686),i=r(4913),o=r(8551),a=r(5397),c=r(1072);t.f=n&&!s?Object.defineProperties:function(e,t){o(e);for(var r,n=a(t),s=c(t),u=s.length,l=0;u>l;)i.f(e,r=s[l++],n[r]);return e}},4913:(e,t,r)=>{"use strict";var n=r(3724),s=r(5917),i=r(8686),o=r(8551),a=r(6969),c=TypeError,u=Object.defineProperty,l=Object.getOwnPropertyDescriptor,d="enumerable",f="configurable",p="writable";t.f=n?i?function(e,t,r){if(o(e),t=a(t),o(r),"function"==typeof e&&"prototype"===t&&"value"in r&&p in r&&!r[p]){var n=l(e,t);n&&n[p]&&(e[t]=r.value,r={configurable:f in r?r[f]:n[f],enumerable:d in r?r[d]:n[d],writable:!1})}return u(e,t,r)}:u:function(e,t,r){if(o(e),t=a(t),o(r),s)try{return u(e,t,r)}catch(e){}if("get"in r||"set"in r)throw new c("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},7347:(e,t,r)=>{"use strict";var n=r(3724),s=r(9565),i=r(8773),o=r(6980),a=r(5397),c=r(6969),u=r(9297),l=r(5917),d=Object.getOwnPropertyDescriptor;t.f=n?d:function(e,t){if(e=a(e),t=c(t),l)try{return d(e,t)}catch(e){}if(u(e,t))return o(!s(i.f,e,t),e[t])}},8480:(e,t,r)=>{"use strict";var n=r(1828),s=r(8727).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,s)}},3717:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},2787:(e,t,r)=>{"use strict";var n=r(9297),s=r(4901),i=r(8981),o=r(6119),a=r(2211),c=o("IE_PROTO"),u=Object,l=u.prototype;e.exports=a?u.getPrototypeOf:function(e){var t=i(e);if(n(t,c))return t[c];var r=t.constructor;return s(r)&&t instanceof r?r.prototype:t instanceof u?l:null}},1625:(e,t,r)=>{"use strict";var n=r(9504);e.exports=n({}.isPrototypeOf)},1828:(e,t,r)=>{"use strict";var n=r(9504),s=r(9297),i=r(5397),o=r(9617).indexOf,a=r(421),c=n([].push);e.exports=function(e,t){var r,n=i(e),u=0,l=[];for(r in n)!s(a,r)&&s(n,r)&&c(l,r);for(;t.length>u;)s(n,r=t[u++])&&(~o(l,r)||c(l,r));return l}},1072:(e,t,r)=>{"use strict";var n=r(1828),s=r(8727);e.exports=Object.keys||function(e){return n(e,s)}},8773:(e,t)=>{"use strict";var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!r.call({1:2},1);t.f=s?function(e){var t=n(this,e);return!!t&&t.enumerable}:r},4270:(e,t,r)=>{"use strict";var n=r(9565),s=r(4901),i=r(34),o=TypeError;e.exports=function(e,t){var r,a;if("string"===t&&s(r=e.toString)&&!i(a=n(r,e)))return a;if(s(r=e.valueOf)&&!i(a=n(r,e)))return a;if("string"!==t&&s(r=e.toString)&&!i(a=n(r,e)))return a;throw new o("Can't convert object to primitive value")}},5031:(e,t,r)=>{"use strict";var n=r(7751),s=r(9504),i=r(8480),o=r(3717),a=r(8551),c=s([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=i.f(a(e)),r=o.f;return r?c(t,r(e)):t}},8235:(e,t,r)=>{"use strict";var n=r(9504),s=r(9297),i=SyntaxError,o=parseInt,a=String.fromCharCode,c=n("".charAt),u=n("".slice),l=n(/./.exec),d={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},f=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;e.exports=function(e,t){for(var r=!0,n="";t<e.length;){var h=c(e,t);if("\\"===h){var m=u(e,t,t+2);if(s(d,m))n+=d[m],t+=2;else{if("\\u"!==m)throw new i('Unknown escape sequence: "'+m+'"');var g=u(e,t+=2,t+4);if(!l(f,g))throw new i("Bad Unicode escape at: "+t);n+=a(o(g,16)),t+=4}}else{if('"'===h){r=!1,t++;break}if(l(p,h))throw new i("Bad control character in string literal at: "+t);n+=h,t++}}if(r)throw new i("Unterminated string at: "+t);return{value:n,end:t}}},1103:e=>{"use strict";e.exports=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}}},7750:(e,t,r)=>{"use strict";var n=r(4117),s=TypeError;e.exports=function(e){if(n(e))throw new s("Can't call method on "+e);return e}},3317:e=>{"use strict";e.exports=function(e,t){return e===t||e!=e&&t!=t}},9286:(e,t,r)=>{"use strict";var n=r(4402),s=r(8469),i=n.Set,o=n.add;e.exports=function(e){var t=new i;return s(e,(function(e){o(t,e)})),t}},3440:(e,t,r)=>{"use strict";var n=r(7080),s=r(4402),i=r(9286),o=r(5170),a=r(3789),c=r(8469),u=r(507),l=s.has,d=s.remove;e.exports=function(e){var t=n(this),r=a(e),s=i(t);return o(t)<=r.size?c(t,(function(e){r.includes(e)&&d(s,e)})):u(r.getIterator(),(function(e){l(t,e)&&d(s,e)})),s}},4402:(e,t,r)=>{"use strict";var n=r(9504),s=Set.prototype;e.exports={Set,add:n(s.add),has:n(s.has),remove:n(s.delete),proto:s}},8750:(e,t,r)=>{"use strict";var n=r(7080),s=r(4402),i=r(5170),o=r(3789),a=r(8469),c=r(507),u=s.Set,l=s.add,d=s.has;e.exports=function(e){var t=n(this),r=o(e),s=new u;return i(t)>r.size?c(r.getIterator(),(function(e){d(t,e)&&l(s,e)})):a(t,(function(e){r.includes(e)&&l(s,e)})),s}},4449:(e,t,r)=>{"use strict";var n=r(7080),s=r(4402).has,i=r(5170),o=r(3789),a=r(8469),c=r(507),u=r(9539);e.exports=function(e){var t=n(this),r=o(e);if(i(t)<=r.size)return!1!==a(t,(function(e){if(r.includes(e))return!1}),!0);var l=r.getIterator();return!1!==c(l,(function(e){if(s(t,e))return u(l,"normal",!1)}))}},3838:(e,t,r)=>{"use strict";var n=r(7080),s=r(5170),i=r(8469),o=r(3789);e.exports=function(e){var t=n(this),r=o(e);return!(s(t)>r.size)&&!1!==i(t,(function(e){if(!r.includes(e))return!1}),!0)}},8527:(e,t,r)=>{"use strict";var n=r(7080),s=r(4402).has,i=r(5170),o=r(3789),a=r(507),c=r(9539);e.exports=function(e){var t=n(this),r=o(e);if(i(t)<r.size)return!1;var u=r.getIterator();return!1!==a(u,(function(e){if(!s(t,e))return c(u,"normal",!1)}))}},8469:(e,t,r)=>{"use strict";var n=r(9504),s=r(507),i=r(4402),o=i.Set,a=i.proto,c=n(a.forEach),u=n(a.keys),l=u(new o).next;e.exports=function(e,t,r){return r?s({iterator:u(e),next:l},t):c(e,t)}},4916:(e,t,r)=>{"use strict";var n=r(7751),s=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},i=function(e){return{size:e,has:function(){return!0},keys:function(){throw new Error("e")}}};e.exports=function(e,t){var r=n("Set");try{(new r)[e](s(0));try{return(new r)[e](s(-1)),!1}catch(n){if(!t)return!0;try{return(new r)[e](i(-1/0)),!1}catch(n){var o=new r;return o.add(1),o.add(2),t(o[e](i(1/0)))}}}catch(e){return!1}}},5170:(e,t,r)=>{"use strict";var n=r(6706),s=r(4402);e.exports=n(s.proto,"size","get")||function(e){return e.size}},3650:(e,t,r)=>{"use strict";var n=r(7080),s=r(4402),i=r(9286),o=r(3789),a=r(507),c=s.add,u=s.has,l=s.remove;e.exports=function(e){var t=n(this),r=o(e).getIterator(),s=i(t);return a(r,(function(e){u(t,e)?l(s,e):c(s,e)})),s}},4204:(e,t,r)=>{"use strict";var n=r(7080),s=r(4402).add,i=r(9286),o=r(3789),a=r(507);e.exports=function(e){var t=n(this),r=o(e).getIterator(),c=i(t);return a(r,(function(e){s(c,e)})),c}},6119:(e,t,r)=>{"use strict";var n=r(5745),s=r(3392),i=n("keys");e.exports=function(e){return i[e]||(i[e]=s(e))}},7629:(e,t,r)=>{"use strict";var n=r(6395),s=r(4576),i=r(9433),o="__core-js_shared__",a=e.exports=s[o]||i(o,{});(a.versions||(a.versions=[])).push({version:"3.40.0",mode:n?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.40.0/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:(e,t,r)=>{"use strict";var n=r(7629);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},8183:(e,t,r)=>{"use strict";var n=r(9504),s=r(1291),i=r(655),o=r(7750),a=n("".charAt),c=n("".charCodeAt),u=n("".slice),l=function(e){return function(t,r){var n,l,d=i(o(t)),f=s(r),p=d.length;return f<0||f>=p?e?"":void 0:(n=c(d,f))<55296||n>56319||f+1===p||(l=c(d,f+1))<56320||l>57343?e?a(d,f):n:e?u(d,f,f+2):l-56320+(n-55296<<10)+65536}};e.exports={codeAt:l(!1),charAt:l(!0)}},4495:(e,t,r)=>{"use strict";var n=r(9519),s=r(9039),i=r(4576).String;e.exports=!!Object.getOwnPropertySymbols&&!s((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},5610:(e,t,r)=>{"use strict";var n=r(1291),s=Math.max,i=Math.min;e.exports=function(e,t){var r=n(e);return r<0?s(r+t,0):i(r,t)}},5397:(e,t,r)=>{"use strict";var n=r(7055),s=r(7750);e.exports=function(e){return n(s(e))}},1291:(e,t,r)=>{"use strict";var n=r(741);e.exports=function(e){var t=+e;return t!=t||0===t?0:n(t)}},8014:(e,t,r)=>{"use strict";var n=r(1291),s=Math.min;e.exports=function(e){var t=n(e);return t>0?s(t,9007199254740991):0}},8981:(e,t,r)=>{"use strict";var n=r(7750),s=Object;e.exports=function(e){return s(n(e))}},2777:(e,t,r)=>{"use strict";var n=r(9565),s=r(34),i=r(757),o=r(5966),a=r(4270),c=r(8227),u=TypeError,l=c("toPrimitive");e.exports=function(e,t){if(!s(e)||i(e))return e;var r,c=o(e,l);if(c){if(void 0===t&&(t="default"),r=n(c,e,t),!s(r)||i(r))return r;throw new u("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},6969:(e,t,r)=>{"use strict";var n=r(2777),s=r(757);e.exports=function(e){var t=n(e,"string");return s(t)?t:t+""}},7650:(e,t,r)=>{"use strict";var n=r(7751),s=r(4901),i=r(1563),o=r(34),a=n("Set");e.exports=function(e){return function(e){return o(e)&&"number"==typeof e.size&&s(e.has)&&s(e.keys)}(e)?e:i(e)?new a(e):e}},2140:(e,t,r)=>{"use strict";var n={};n[r(8227)("toStringTag")]="z",e.exports="[object z]"===String(n)},655:(e,t,r)=>{"use strict";var n=r(6955),s=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return s(e)}},6823:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},3392:(e,t,r)=>{"use strict";var n=r(9504),s=0,i=Math.random(),o=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+o(++s+i,36)}},7040:(e,t,r)=>{"use strict";var n=r(4495);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:(e,t,r)=>{"use strict";var n=r(3724),s=r(9039);e.exports=n&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:e=>{"use strict";var t=TypeError;e.exports=function(e,r){if(e<r)throw new t("Not enough arguments");return e}},8622:(e,t,r)=>{"use strict";var n=r(4576),s=r(4901),i=n.WeakMap;e.exports=s(i)&&/native code/.test(String(i))},8227:(e,t,r)=>{"use strict";var n=r(4576),s=r(5745),i=r(9297),o=r(3392),a=r(4495),c=r(7040),u=n.Symbol,l=s("wks"),d=c?u.for||u:u&&u.withoutSetter||o;e.exports=function(e){return i(l,e)||(l[e]=a&&i(u,e)?u[e]:d("Symbol."+e)),l[e]}},4114:(e,t,r)=>{"use strict";var n=r(6518),s=r(8981),i=r(6198),o=r(4527),a=r(6837);n({target:"Array",proto:!0,arity:1,forced:r(9039)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function(e){var t=s(this),r=i(t),n=arguments.length;a(r+n);for(var c=0;c<n;c++)t[r]=arguments[c],r++;return o(t,r),r}})},8111:(e,t,r)=>{"use strict";var n=r(6518),s=r(4576),i=r(679),o=r(8551),a=r(4901),c=r(2787),u=r(2106),l=r(4659),d=r(9039),f=r(9297),p=r(8227),h=r(7657).IteratorPrototype,m=r(3724),g=r(6395),y="constructor",v="Iterator",b=p("toStringTag"),w=TypeError,_=s[v],x=g||!a(_)||_.prototype!==h||!d((function(){_({})})),S=function(){if(i(this,h),c(this)===h)throw new w("Abstract class Iterator not directly constructable")},A=function(e,t){m?u(h,e,{configurable:!0,get:function(){return t},set:function(t){if(o(this),this===h)throw new w("You can't redefine this property");f(this,e)?this[e]=t:l(this,e,t)}}):h[e]=t};f(h,b)||A(b,v),!x&&f(h,y)&&h[y]!==Object||A(y,S),S.prototype=h,n({global:!0,constructor:!0,forced:x},{Iterator:S})},1148:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),i=r(9306),o=r(8551),a=r(1767);n({target:"Iterator",proto:!0,real:!0},{every:function(e){o(this),i(e);var t=a(this),r=0;return!s(t,(function(t,n){if(!e(t,r++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},2489:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(9306),o=r(8551),a=r(1767),c=r(9462),u=r(6319),l=r(6395),d=c((function(){for(var e,t,r=this.iterator,n=this.predicate,i=this.next;;){if(e=o(s(i,r)),this.done=!!e.done)return;if(t=e.value,u(r,n,[t,this.counter++],!0))return t}}));n({target:"Iterator",proto:!0,real:!0,forced:l},{filter:function(e){return o(this),i(e),new d(a(this),{predicate:e})}})},116:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),i=r(9306),o=r(8551),a=r(1767);n({target:"Iterator",proto:!0,real:!0},{find:function(e){o(this),i(e);var t=a(this),r=0;return s(t,(function(t,n){if(e(t,r++))return n(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},7588:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),i=r(9306),o=r(8551),a=r(1767);n({target:"Iterator",proto:!0,real:!0},{forEach:function(e){o(this),i(e);var t=a(this),r=0;s(t,(function(t){e(t,r++)}),{IS_RECORD:!0})}})},1701:(e,t,r)=>{"use strict";var n=r(6518),s=r(713);n({target:"Iterator",proto:!0,real:!0,forced:r(6395)},{map:s})},8237:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),i=r(9306),o=r(8551),a=r(1767),c=TypeError;n({target:"Iterator",proto:!0,real:!0},{reduce:function(e){o(this),i(e);var t=a(this),r=arguments.length<2,n=r?void 0:arguments[1],u=0;if(s(t,(function(t){r?(r=!1,n=t):n=e(n,t,u),u++}),{IS_RECORD:!0}),r)throw new c("Reduce of empty iterator with no initial value");return n}})},3579:(e,t,r)=>{"use strict";var n=r(6518),s=r(2652),i=r(9306),o=r(8551),a=r(1767);n({target:"Iterator",proto:!0,real:!0},{some:function(e){o(this),i(e);var t=a(this),r=0;return s(t,(function(t,n){if(e(t,r++))return n()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},7642:(e,t,r)=>{"use strict";var n=r(6518),s=r(3440);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("difference",(function(e){return 0===e.size}))},{difference:s})},8004:(e,t,r)=>{"use strict";var n=r(6518),s=r(9039),i=r(8750);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("intersection",(function(e){return 2===e.size&&e.has(1)&&e.has(2)}))||s((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}))},{intersection:i})},3853:(e,t,r)=>{"use strict";var n=r(6518),s=r(4449);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("isDisjointFrom",(function(e){return!e}))},{isDisjointFrom:s})},5876:(e,t,r)=>{"use strict";var n=r(6518),s=r(3838);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("isSubsetOf",(function(e){return e}))},{isSubsetOf:s})},2475:(e,t,r)=>{"use strict";var n=r(6518),s=r(8527);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("isSupersetOf",(function(e){return!e}))},{isSupersetOf:s})},5024:(e,t,r)=>{"use strict";var n=r(6518),s=r(3650);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("symmetricDifference")},{symmetricDifference:s})},1698:(e,t,r)=>{"use strict";var n=r(6518),s=r(4204);n({target:"Set",proto:!0,real:!0,forced:!r(4916)("union")},{union:s})},6986:(e,t,r)=>{"use strict";var n=r(6518),s=r(7957),i=r(4598),o=r(6469);n({target:"Array",proto:!0,forced:!i("groupBy")},{groupBy:function(e){return s(this,e,arguments.length>1?arguments[1]:void 0)}}),o("groupBy")},6058:(e,t,r)=>{"use strict";var n=r(6518),s=r(7957),i=r(6469);n({target:"Array",proto:!0},{group:function(e){return s(this,e,arguments.length>1?arguments[1]:void 0)}}),i("group")},4648:(e,t,r)=>{"use strict";var n=r(6518),s=r(6639).every;n({target:"AsyncIterator",proto:!0,real:!0},{every:function(e){return s(this,e)}})},7333:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(9306),o=r(8551),a=r(34),c=r(1767),u=r(2059),l=r(2529),d=r(772),f=r(6395),p=u((function(e){var t=this,r=t.iterator,n=t.predicate;return new e((function(i,c){var u=function(e){t.done=!0,c(e)},f=function(e){d(r,u,e,u)},p=function(){try{e.resolve(o(s(t.next,r))).then((function(r){try{if(o(r).done)t.done=!0,i(l(void 0,!0));else{var s=r.value;try{var c=n(s,t.counter++),d=function(e){e?i(l(s,!1)):p()};a(c)?e.resolve(c).then(d,f):d(c)}catch(e){f(e)}}}catch(e){u(e)}}),u)}catch(e){u(e)}};p()}))}));n({target:"AsyncIterator",proto:!0,real:!0,forced:f},{filter:function(e){return o(this),i(e),new p(c(this),{predicate:e})}})},3064:(e,t,r)=>{"use strict";var n=r(6518),s=r(6639).find;n({target:"AsyncIterator",proto:!0,real:!0},{find:function(e){return s(this,e)}})},9920:(e,t,r)=>{"use strict";var n=r(6518),s=r(6639).forEach;n({target:"AsyncIterator",proto:!0,real:!0},{forEach:function(e){return s(this,e)}})},1393:(e,t,r)=>{"use strict";var n=r(6518),s=r(1750);n({target:"AsyncIterator",proto:!0,real:!0,forced:r(6395)},{map:s})},4905:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(9306),o=r(8551),a=r(34),c=r(7751),u=r(1767),l=r(772),d=c("Promise"),f=TypeError;n({target:"AsyncIterator",proto:!0,real:!0},{reduce:function(e){o(this),i(e);var t=u(this),r=t.iterator,n=t.next,c=arguments.length<2,p=c?void 0:arguments[1],h=0;return new d((function(t,i){var u=function(e){l(r,i,e,i)},m=function(){try{d.resolve(o(s(n,r))).then((function(r){try{if(o(r).done)c?i(new f("Reduce of empty iterator with no initial value")):t(p);else{var n=r.value;if(c)c=!1,p=n,m();else try{var s=e(p,n,h),l=function(e){p=e,m()};a(s)?d.resolve(s).then(l,u):l(s)}catch(e){u(e)}}h++}catch(e){i(e)}}),i)}catch(e){i(e)}};m()}))}})},8159:(e,t,r)=>{"use strict";var n=r(6518),s=r(6639).some;n({target:"AsyncIterator",proto:!0,real:!0},{some:function(e){return s(this,e)}})},8992:(e,t,r)=>{"use strict";r(8111)},3215:(e,t,r)=>{"use strict";r(1148)},4520:(e,t,r)=>{"use strict";r(2489)},2577:(e,t,r)=>{"use strict";r(116)},3949:(e,t,r)=>{"use strict";r(7588)},1454:(e,t,r)=>{"use strict";r(1701)},8872:(e,t,r)=>{"use strict";r(8237)},7550:(e,t,r)=>{"use strict";r(3579)},8335:(e,t,r)=>{"use strict";var n=r(6518),s=r(3724),i=r(4576),o=r(7751),a=r(9504),c=r(9565),u=r(4901),l=r(34),d=r(4376),f=r(9297),p=r(655),h=r(6198),m=r(4659),g=r(9039),y=r(8235),v=r(4495),b=i.JSON,w=i.Number,_=i.SyntaxError,x=b&&b.parse,S=o("Object","keys"),A=Object.getOwnPropertyDescriptor,E=a("".charAt),k=a("".slice),R=a(/./.exec),I=a([].push),O=/^\d$/,$=/^[1-9]$/,C=/^[\d-]$/,T=/^[\t\n\r ]$/,P=function(e,t,r,n){var s,i,o,a,u,p=e[t],m=n&&p===n.value,g=m&&"string"==typeof n.source?{source:n.source}:{};if(l(p)){var y=d(p),v=m?n.nodes:y?[]:{};if(y)for(s=v.length,o=h(p),a=0;a<o;a++)j(p,a,P(p,""+a,r,a<s?v[a]:void 0));else for(i=S(p),o=h(i),a=0;a<o;a++)u=i[a],j(p,u,P(p,u,r,f(v,u)?v[u]:void 0))}return c(r,e,t,p,g)},j=function(e,t,r){if(s){var n=A(e,t);if(n&&!n.configurable)return}void 0===r?delete e[t]:m(e,t,r)},M=function(e,t,r,n){this.value=e,this.end=t,this.source=r,this.nodes=n},D=function(e,t){this.source=e,this.index=t};D.prototype={fork:function(e){return new D(this.source,e)},parse:function(){var e=this.source,t=this.skip(T,this.index),r=this.fork(t),n=E(e,t);if(R(C,n))return r.number();switch(n){case"{":return r.object();case"[":return r.array();case'"':return r.string();case"t":return r.keyword(!0);case"f":return r.keyword(!1);case"n":return r.keyword(null)}throw new _('Unexpected character: "'+n+'" at: '+t)},node:function(e,t,r,n,s){return new M(t,n,e?null:k(this.source,r,n),s)},object:function(){for(var e=this.source,t=this.index+1,r=!1,n={},s={};t<e.length;){if(t=this.until(['"',"}"],t),"}"===E(e,t)&&!r){t++;break}var i=this.fork(t).string(),o=i.value;t=i.end,t=this.until([":"],t)+1,t=this.skip(T,t),i=this.fork(t).parse(),m(s,o,i),m(n,o,i.value),t=this.until([",","}"],i.end);var a=E(e,t);if(","===a)r=!0,t++;else if("}"===a){t++;break}}return this.node(1,n,this.index,t,s)},array:function(){for(var e=this.source,t=this.index+1,r=!1,n=[],s=[];t<e.length;){if(t=this.skip(T,t),"]"===E(e,t)&&!r){t++;break}var i=this.fork(t).parse();if(I(s,i),I(n,i.value),t=this.until([",","]"],i.end),","===E(e,t))r=!0,t++;else if("]"===E(e,t)){t++;break}}return this.node(1,n,this.index,t,s)},string:function(){var e=this.index,t=y(this.source,this.index+1);return this.node(0,t.value,e,t.end)},number:function(){var e=this.source,t=this.index,r=t;if("-"===E(e,r)&&r++,"0"===E(e,r))r++;else{if(!R($,E(e,r)))throw new _("Failed to parse number at: "+r);r=this.skip(O,r+1)}if(!("."===E(e,r)&&(r=this.skip(O,r+1)),"e"!==E(e,r)&&"E"!==E(e,r)||(r++,"+"!==E(e,r)&&"-"!==E(e,r)||r++,r!==(r=this.skip(O,r)))))throw new _("Failed to parse number's exponent value at: "+r);return this.node(0,w(k(e,t,r)),t,r)},keyword:function(e){var t=""+e,r=this.index,n=r+t.length;if(k(this.source,r,n)!==t)throw new _("Failed to parse value at: "+r);return this.node(0,e,r,n)},skip:function(e,t){for(var r=this.source;t<r.length&&R(e,E(r,t));t++);return t},until:function(e,t){t=this.skip(T,t);for(var r=E(this.source,t),n=0;n<e.length;n++)if(e[n]===r)return t;throw new _('Unexpected character: "'+r+'" at: '+t)}};var N=g((function(){var e,t="9007199254740993";return x(t,(function(t,r,n){e=n.source})),e!==t})),q=v&&!g((function(){return 1/x("-0 \t")!=-1/0}));n({target:"JSON",stat:!0,forced:N},{parse:function(e,t){return q&&!u(t)?x(e):function(e,t){e=p(e);var r=new D(e,0,""),n=r.parse(),s=n.value,i=r.skip(T,n.end);if(i<e.length)throw new _('Unexpected extra character: "'+E(e,i)+'" after the parsed data at: '+i);return u(t)?P({"":s},"",t,n):s}(e,t)}})},1517:(e,t,r)=>{"use strict";var n=r(6518),s=r(6194),i=r(2248).remove;n({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=s(this),r=!0,n=0,o=arguments.length;n<o;n++)e=i(t,arguments[n]),r=r&&e;return!!r}})},1379:(e,t,r)=>{"use strict";var n=r(6518),s=r(6194),i=r(2248),o=i.get,a=i.has,c=i.set;n({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var r,n,i=s(this);return a(i,e)?(r=o(i,e),"update"in t&&(r=t.update(r,e,i),c(i,e,r)),r):(n=t.insert(e,i),c(i,e,n),n)}})},3777:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(6223);n({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0);return!1!==o(t,(function(e,n){if(!r(e,n,t))return!1}),!0)}})},4190:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(2248),a=r(6223),c=o.Map,u=o.set;n({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=new c;return a(t,(function(e,s){r(e,s,t)&&u(n,s,e)})),n}})},6097:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(6223);n({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=o(t,(function(e,n){if(r(e,n,t))return{key:n}}),!0);return n&&n.key}})},2359:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(6223);n({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=o(t,(function(e,n){if(r(e,n,t))return{value:e}}),!0);return n&&n.value}})},7273:(e,t,r)=>{"use strict";var n=r(6518),s=r(3317),i=r(6194),o=r(6223);n({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return!0===o(i(this),(function(t){if(s(t,e))return!0}),!0)}})},7415:(e,t,r)=>{"use strict";var n=r(6518),s=r(6194),i=r(6223);n({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){var t=i(s(this),(function(t,r){if(t===e)return{key:r}}),!0);return t&&t.key}})},9929:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(2248),a=r(6223),c=o.Map,u=o.set;n({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=new c;return a(t,(function(e,s){u(n,r(e,s,t),e)})),n}})},7583:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(2248),a=r(6223),c=o.Map,u=o.set;n({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=new c;return a(t,(function(e,s){u(n,s,r(e,s,t))})),n}})},5122:(e,t,r)=>{"use strict";var n=r(6518),s=r(6194),i=r(2652),o=r(2248).set;n({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=s(this),r=arguments.length,n=0;n<r;)i(arguments[n++],(function(e,r){o(t,e,r)}),{AS_ENTRIES:!0});return t}})},230:(e,t,r)=>{"use strict";var n=r(6518),s=r(9306),i=r(6194),o=r(6223),a=TypeError;n({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=i(this),r=arguments.length<2,n=r?void 0:arguments[1];if(s(e),o(t,(function(s,i){r?(r=!1,n=s):n=e(n,s,i,t)})),r)throw new a("Reduce of empty map with no initial value");return n}})},7268:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(6194),o=r(6223);n({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0);return!0===o(t,(function(e,n){if(r(e,n,t))return!0}),!0)}})},9733:(e,t,r)=>{"use strict";var n=r(6518),s=r(9306),i=r(6194),o=r(2248),a=TypeError,c=o.get,u=o.has,l=o.set;n({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var r=i(this),n=arguments.length;s(t);var o=u(r,e);if(!o&&n<3)throw new a("Updating absent value");var d=o?c(r,e):s(n>2?arguments[2]:void 0)(e,r);return l(r,e,t(d,e,r)),r}})},5509:(e,t,r)=>{"use strict";var n=r(6518),s=r(7080),i=r(4402).add;n({target:"Set",proto:!0,real:!0,forced:!0},{addAll:function(){for(var e=s(this),t=0,r=arguments.length;t<r;t++)i(e,arguments[t]);return e}})},5223:(e,t,r)=>{"use strict";var n=r(6518),s=r(7080),i=r(4402).remove;n({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=s(this),r=!0,n=0,o=arguments.length;n<o;n++)e=i(t,arguments[n]),r=r&&e;return!!r}})},321:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(3440);n({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){return s(o,this,i(e))}})},1927:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(7080),o=r(8469);n({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0);return!1!==o(t,(function(e){if(!r(e,e,t))return!1}),!0)}})},1632:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(7080),o=r(4402),a=r(8469),c=o.Set,u=o.add;n({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=new c;return a(t,(function(e){r(e,e,t)&&u(n,e)})),n}})},4377:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(7080),o=r(8469);n({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=o(t,(function(e){if(r(e,e,t))return{value:e}}),!0);return n&&n.value}})},6771:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(8750);n({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){return s(o,this,i(e))}})},2516:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(4449);n({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){return s(o,this,i(e))}})},8931:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(3838);n({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){return s(o,this,i(e))}})},2514:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(8527);n({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){return s(o,this,i(e))}})},5694:(e,t,r)=>{"use strict";var n=r(6518),s=r(9504),i=r(7080),o=r(8469),a=r(655),c=s([].join),u=s([].push);n({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=i(this),r=void 0===e?",":a(e),n=[];return o(t,(function(e){u(n,e)})),c(n,r)}})},2774:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(7080),o=r(4402),a=r(8469),c=o.Set,u=o.add;n({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0),n=new c;return a(t,(function(e){u(n,r(e,e,t))})),n}})},9536:(e,t,r)=>{"use strict";var n=r(6518),s=r(9306),i=r(7080),o=r(8469),a=TypeError;n({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=i(this),r=arguments.length<2,n=r?void 0:arguments[1];if(s(e),o(t,(function(s){r?(r=!1,n=s):n=e(n,s,s,t)})),r)throw new a("Reduce of empty set with no initial value");return n}})},1926:(e,t,r)=>{"use strict";var n=r(6518),s=r(6080),i=r(7080),o=r(8469);n({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=i(this),r=s(e,arguments.length>1?arguments[1]:void 0);return!0===o(t,(function(e){if(r(e,e,t))return!0}),!0)}})},4483:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(3650);n({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){return s(o,this,i(e))}})},6215:(e,t,r)=>{"use strict";var n=r(6518),s=r(9565),i=r(7650),o=r(4204);n({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){return s(o,this,i(e))}})},8898:(e,t,r)=>{"use strict";var n=r(6518),s=r(8183).charAt,i=r(7750),o=r(1291),a=r(655);n({target:"String",proto:!0,forced:!0},{at:function(e){var t=a(i(this)),r=t.length,n=o(e),c=n>=0?n:r+n;return c<0||c>=r?void 0:s(t,c)}})},4603:(e,t,r)=>{"use strict";var n=r(6840),s=r(9504),i=r(655),o=r(2812),a=URLSearchParams,c=a.prototype,u=s(c.append),l=s(c.delete),d=s(c.forEach),f=s([].push),p=new a("a=1&a=2&b=3");p.delete("a",1),p.delete("b",void 0),p+""!="a=2"&&n(c,"delete",(function(e){var t=arguments.length,r=t<2?void 0:arguments[1];if(t&&void 0===r)return l(this,e);var n=[];d(this,(function(e,t){f(n,{key:t,value:e})})),o(t,1);for(var s,a=i(e),c=i(r),p=0,h=0,m=!1,g=n.length;p<g;)s=n[p++],m||s.key===a?(m=!0,l(this,s.key)):h++;for(;h<g;)(s=n[h++]).key===a&&s.value===c||u(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},7566:(e,t,r)=>{"use strict";var n=r(6840),s=r(9504),i=r(655),o=r(2812),a=URLSearchParams,c=a.prototype,u=s(c.getAll),l=s(c.has),d=new a("a=1");!d.has("a",2)&&d.has("a",void 0)||n(c,"has",(function(e){var t=arguments.length,r=t<2?void 0:arguments[1];if(t&&void 0===r)return l(this,e);var n=u(this,e);o(t,1);for(var s=i(r),a=0;a<n.length;)if(n[a++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},8721:(e,t,r)=>{"use strict";var n=r(3724),s=r(9504),i=r(2106),o=URLSearchParams.prototype,a=s(o.forEach);n&&!("size"in o)&&i(o,"size",{get:function(){var e=0;return a(this,(function(){e++})),e},configurable:!0,enumerable:!0})}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";class e{extensionId;constructor(e){this.extensionId=e}isSameExtension(e){return e.byExtensionId===this.extensionId}process(e){return!(!this.isSameExtension(e.item)||!e.allowJSON&&t(e.item))}}const t=e=>"application/json"===e?.mime;r(4114),r(6058);class n{handlerMap;constructor(){this.handlerMap={}}async publish(e){if(e.name in this.handlerMap)for(const t of this.handlerMap[e.name])try{await t(e,this)}catch(t){console.info(`[${e.name}]: failed to handle`),console.error(t)}else console.info(`[${e.name}]: no handler to handle`)}async publishAll(...e){for(const t of e)await this.publish(t)}register(e,t){const r=Array.isArray(t)?[...t]:[t];return e in this.handlerMap?(this.handlerMap[e].push(...r),this):(this.handlerMap[e]=r,this)}clearHandlers(e){delete this.handlerMap[e]}clearAllHandlers(){this.handlerMap=Object.create({})}}const s=(()=>{let e;return()=>(e||(e=new n),e)})();const i="8.50.0",o=globalThis;function a(e,t,r){const n=r||o,s=n.__SENTRY__=n.__SENTRY__||{},a=s[i]=s[i]||{};return a[e]||(a[e]=t())}function c(){return u(o),o}function u(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||i,t[i]=t[i]||{}}function l(){return Date.now()/1e3}const d=function(){const{performance:e}=o;if(!e||!e.now)return l;const t=Date.now()-e.now(),r=null==e.timeOrigin?t:e.timeOrigin;return()=>(r+e.now())/1e3}();let f;(()=>{const{performance:e}=o;if(!e||!e.now)return void(f="none");const t=36e5,r=e.now(),n=Date.now(),s=e.timeOrigin?Math.abs(e.timeOrigin+r-n):t,i=s<t,a=e.timing&&e.timing.navigationStart,c="number"==typeof a?Math.abs(a+r-n):t;i||c<t?s<=c?(f="timeOrigin",e.timeOrigin):f="navigationStart":f="dateNow"})();const p=Object.prototype.toString;function h(e){switch(p.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return S(e,Error)}}function m(e,t){return p.call(e)===`[object ${t}]`}function g(e){return m(e,"ErrorEvent")}function y(e){return m(e,"DOMError")}function v(e){return m(e,"String")}function b(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function w(e){return m(e,"Object")}function _(e){return"undefined"!=typeof Event&&S(e,Event)}function x(e){return Boolean(e&&e.then&&"function"==typeof e.then)}function S(e,t){try{return e instanceof t}catch(e){return!1}}function A(e){return!("object"!=typeof e||null===e||!e.__isVue&&!e._isVue)}const E=o;function k(e,t){const r=e,n=[];if(!r||!r.tagName)return"";if(E.HTMLElement&&r instanceof HTMLElement&&r.dataset){if(r.dataset.sentryComponent)return r.dataset.sentryComponent;if(r.dataset.sentryElement)return r.dataset.sentryElement}n.push(r.tagName.toLowerCase());const s=t&&t.length?t.filter((e=>r.getAttribute(e))).map((e=>[e,r.getAttribute(e)])):null;if(s&&s.length)s.forEach((e=>{n.push(`[${e[0]}="${e[1]}"]`)}));else{r.id&&n.push(`#${r.id}`);const e=r.className;if(e&&v(e)){const t=e.split(/\s+/);for(const e of t)n.push(`.${e}`)}}const i=["aria-label","type","name","title","alt"];for(const e of i){const t=r.getAttribute(e);t&&n.push(`[${e}="${t}"]`)}return n.join("")}const R=["debug","info","warn","error","log","assert","trace"],I={};function O(e){if(!("console"in o))return e();const t=o.console,r={},n=Object.keys(I);n.forEach((e=>{const n=I[e];r[e]=t[e],t[e]=n}));try{return e()}finally{n.forEach((e=>{t[e]=r[e]}))}}const $=a("logger",(function(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return R.forEach((e=>{t[e]=()=>{}})),t}));function C(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function T(e,t){if(!Array.isArray(e))return"";const r=[];for(let t=0;t<e.length;t++){const n=e[t];try{A(n)?r.push("[VueViewModel]"):r.push(String(n))}catch(e){r.push("[value cannot be serialized]")}}return r.join(t)}function P(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch(e){}}function j(e){if(h(e))return{message:e.message,name:e.name,stack:e.stack,...D(e)};if(_(e)){const t={type:e.type,target:M(e.target),currentTarget:M(e.currentTarget),...D(e)};return"undefined"!=typeof CustomEvent&&S(e,CustomEvent)&&(t.detail=e.detail),t}return e}function M(e){try{return"undefined"!=typeof Element&&S(e,Element)?function(e,t={}){if(!e)return"<unknown>";try{let r=e;const n=5,s=[];let i=0,o=0;const a=" > ",c=a.length;let u;const l=Array.isArray(t)?t:t.keyAttrs,d=!Array.isArray(t)&&t.maxStringLength||80;for(;r&&i++<n&&(u=k(r,l),!("html"===u||i>1&&o+s.length*c+u.length>=d));)s.push(u),o+=u.length,r=r.parentNode;return s.reverse().join(a)}catch(e){return"<unknown>"}}(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function D(e){if("object"==typeof e&&null!==e){const t={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}return{}}function N(e){return q(e,new Map)}function q(e,t){if(function(e){if(!w(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||"Object"===t}catch(e){return!0}}(e)){const r=t.get(e);if(void 0!==r)return r;const n={};t.set(e,n);for(const r of Object.getOwnPropertyNames(e))void 0!==e[r]&&(n[r]=q(e[r],t));return n}if(Array.isArray(e)){const r=t.get(e);if(void 0!==r)return r;const n=[];return t.set(e,n),e.forEach((e=>{n.push(q(e,t))})),n}return e}function L(){const e=o,t=e.crypto||e.msCrypto;let r=()=>16*Math.random();try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(r=()=>{const e=new Uint8Array(1);return t.getRandomValues(e),e[0]})}catch(e){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(e=>(e^(15&r())>>e/4).toString(16)))}function F(e,t,r){const n=e.exception=e.exception||{},s=n.values=n.values||[],i=s[0]=s[0]||{};i.value||(i.value=t||""),i.type||(i.type=r||"Error")}function U(e,t){const r=function(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}(e);if(!r)return;const n=r.mechanism;if(r.mechanism={type:"generic",handled:!0,...n,...t},t&&"data"in t){const e={...n&&n.data,...t.data};r.mechanism.data=e}}function B(e){if(function(e){try{return e.__sentry_captured__}catch(e){}}(e))return!0;try{P(e,"__sentry_captured__",!0)}catch(e){}return!1}function z(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||d(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:L()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{const t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function W(){return L()}function H(){return L().substring(16)}function V(e,t,r=2){if(!t||"object"!=typeof t||r<=0)return t;if(e&&t&&0===Object.keys(t).length)return e;const n={...e};for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=V(n[e],t[e],r-1));return n}const K="_sentrySpan";function G(e,t){t?P(e,K,t):delete e[K]}function Q(e){return e[K]}class J{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:W(),spanId:H()}}clone(){const e=new J;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,G(e,Q(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&z(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const t="function"==typeof e?e(this):e,[r,n]=t instanceof Z?[t.getScopeData(),t.getRequestSession()]:w(t)?[e,e.requestSession]:[],{tags:s,extra:i,user:o,contexts:a,level:c,fingerprint:u=[],propagationContext:l}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...i},this._contexts={...this._contexts,...a},o&&Object.keys(o).length&&(this._user=o),c&&(this._level=c),u.length&&(this._fingerprint=u),l&&(this._propagationContext=l),n&&(this._requestSession=n),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,G(this,void 0),this._attachments=[],this.setPropagationContext({traceId:W()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){const r="number"==typeof t?t:100;if(r<=0)return this;const n={timestamp:l(),...e},s=this._breadcrumbs;return s.push(n),this._breadcrumbs=s.length>r?s.slice(-r):s,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Q(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=V(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:H(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,t){const r=t&&t.event_id?t.event_id:L();if(!this._client)return $.warn("No client configured on scope - will not capture exception!"),r;const n=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:n,...t,event_id:r},this),r}captureMessage(e,t,r){const n=r&&r.event_id?r.event_id:L();if(!this._client)return $.warn("No client configured on scope - will not capture message!"),n;const s=new Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:s,...r,event_id:n},this),n}captureEvent(e,t){const r=t&&t.event_id?t.event_id:L();return this._client?(this._client.captureEvent(e,{...t,event_id:r},this),r):($.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((e=>{e(this)})),this._notifyingListeners=!1)}}const Z=J;class Y{constructor(e,t){let r,n;r=e||new Z,n=t||new Z,this._stack=[{scope:r}],this._isolationScope=n}withScope(e){const t=this._pushScope();let r;try{r=e(t)}catch(e){throw this._popScope(),e}return x(r)?r.then((e=>(this._popScope(),e)),(e=>{throw this._popScope(),e})):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1||!this._stack.pop())}}function X(){const e=u(c());return e.stack=e.stack||new Y(a("defaultCurrentScope",(()=>new Z)),a("defaultIsolationScope",(()=>new Z)))}function ee(e){return X().withScope(e)}function te(e,t){const r=X();return r.withScope((()=>(r.getStackTop().scope=e,t(e))))}function re(e){return X().withScope((()=>e(X().getIsolationScope())))}function ne(e){const t=u(e);return t.acs?t.acs:{withIsolationScope:re,withScope:ee,withSetScope:te,withSetIsolationScope:(e,t)=>re(t),getCurrentScope:()=>X().getScope(),getIsolationScope:()=>X().getIsolationScope()}}function se(){return ne(c()).getCurrentScope()}function ie(){return ne(c()).getIsolationScope()}function oe(){return se().getClient()}function ae(e){const t=e.getPropagationContext(),{traceId:r,spanId:n,parentSpanId:s}=t;return N({trace_id:r,span_id:n,parent_span_id:s})}const ce=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function ue(e,t=!1){const{host:r,path:n,pass:s,port:i,projectId:o,protocol:a,publicKey:c}=e;return`${a}://${c}${t&&s?`:${s}`:""}@${r}${i?`:${i}`:""}/${n?`${n}/`:n}${o}`}function le(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}const de="?",fe=/\(error: (.*)\)/,pe=/captureMessage|captureException/;function he(e){return e[e.length-1]||{}}const me="<anonymous>";function ge(e,t=100,r=1/0){try{return ve("",e,t,r)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function ye(e,t=3,r=102400){const n=ge(e,t);return s=n,function(e){return~-encodeURI(e).split(/%..|./).length}(JSON.stringify(s))>r?ye(e,t-1,r):n;var s}function ve(e,t,r=1/0,n=1/0,s=function(){const e="function"==typeof WeakSet,t=e?new WeakSet:[];return[function(r){if(e)return!!t.has(r)||(t.add(r),!1);for(let e=0;e<t.length;e++)if(t[e]===r)return!0;return t.push(r),!1},function(r){if(e)t.delete(r);else for(let e=0;e<t.length;e++)if(t[e]===r){t.splice(e,1);break}}]}()){const[i,o]=s;if(null==t||["boolean","string"].includes(typeof t)||"number"==typeof t&&Number.isFinite(t))return t;const a=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(A(t))return"[VueViewModel]";if(w(r=t)&&"nativeEvent"in r&&"preventDefault"in r&&"stopPropagation"in r)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${function(e){try{return e&&"function"==typeof e&&e.name||me}catch(e){return me}}(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;const n=function(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(e){return`**non-serializable** (${e})`}var r}(e,t);if(!a.startsWith("[object "))return a;if(t.__sentry_skip_normalization__)return t;const c="number"==typeof t.__sentry_override_normalization_depth__?t.__sentry_override_normalization_depth__:r;if(0===c)return a.replace("object ","");if(i(t))return"[Circular ~]";const u=t;if(u&&"function"==typeof u.toJSON)try{return ve("",u.toJSON(),c-1,n,s)}catch(e){}const l=Array.isArray(t)?[]:{};let d=0;const f=j(t);for(const e in f){if(!Object.prototype.hasOwnProperty.call(f,e))continue;if(d>=n){l[e]="[MaxProperties ~]";break}const t=f[e];l[e]=ve(e,t,c-1,n,s),d++}return o(t),l}function be(e,t=[]){return[e,t]}function we(e,t){const[r,n]=e;return[r,[...n,t]]}function _e(e,t){const r=e[1];for(const e of r)if(t(e,e[0].type))return!0;return!1}function xe(e){return o.__SENTRY__&&o.__SENTRY__.encodePolyfill?o.__SENTRY__.encodePolyfill(e):(new TextEncoder).encode(e)}function Se(e){const[t,r]=e;let n=JSON.stringify(t);function s(e){"string"==typeof n?n="string"==typeof e?n+e:[xe(n),e]:n.push("string"==typeof e?xe(e):e)}for(const e of r){const[t,r]=e;if(s(`\n${JSON.stringify(t)}\n`),"string"==typeof r||r instanceof Uint8Array)s(r);else{let e;try{e=JSON.stringify(r)}catch(t){e=JSON.stringify(ge(r))}s(e)}}return"string"==typeof n?n:function(e){const t=e.reduce(((e,t)=>e+t.length),0),r=new Uint8Array(t);let n=0;for(const t of e)r.set(t,n),n+=t.length;return r}(n)}function Ae(e){const t="string"==typeof e.data?xe(e.data):e.data;return[N({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const Ee={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function ke(e){return Ee[e]}function Re(e){if(!e||!e.sdk)return;const{name:t,version:r}=e.sdk;return{name:t,version:r}}const Ie=[];function Oe(e,t){for(const r of t)r&&r.afterAllSetup&&r.afterAllSetup(e)}function $e(e,t,r){if(!r[t.name]){if(r[t.name]=t,-1===Ie.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),Ie.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){const r=t.preprocessEvent.bind(t);e.on("preprocessEvent",((t,n)=>r(t,n,e)))}if("function"==typeof t.processEvent){const r=t.processEvent.bind(t),n=Object.assign(((t,n)=>r(t,n,e)),{id:t.name});e.addEventProcessor(n)}}}const Ce="production",Te=/^sentry-/;function Pe(e){return e.split(",").map((e=>e.split("=").map((e=>decodeURIComponent(e.trim()))))).reduce(((e,[t,r])=>(t&&r&&(e[t]=r),e)),{})}function je(e){const t=e._sentryMetrics;if(!t)return;const r={};for(const[,[e,n]]of t)(r[e]||(r[e]=[])).push(N(n));return r}let Me=!1;function De(e){const{spanId:t,traceId:r,isRemote:n}=e.spanContext();return N({parent_span_id:n?t:Le(e).parent_span_id,span_id:n?H():t,trace_id:r})}function Ne(e){return"number"==typeof e?qe(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?qe(e.getTime()):d()}function qe(e){return e>9999999999?e/1e3:e}function Le(e){if(function(e){return"function"==typeof e.getSpanJSON}(e))return e.getSpanJSON();try{const{spanId:t,traceId:r}=e.spanContext();if(function(e){const t=e;return!!(t.attributes&&t.startTime&&t.name&&t.endTime&&t.status)}(e)){const{attributes:n,startTime:s,name:i,endTime:o,parentSpanId:a,status:c}=e;return N({span_id:t,trace_id:r,data:n,description:i,parent_span_id:a,start_timestamp:Ne(s),timestamp:Ne(o)||void 0,status:Fe(c),op:n["sentry.op"],origin:n["sentry.origin"],_metrics_summary:je(e)})}return{span_id:t,trace_id:r}}catch(e){return{}}}function Fe(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}function Ue(e){return e._sentryRootSpan||e}function Be(e,t){const r=t.getOptions(),{publicKey:n}=t.getDsn()||{},s=N({environment:r.environment||Ce,release:r.release,public_key:n,trace_id:e});return t.emit("createDsc",s),s}function ze(e){const t=oe();if(!t)return{};const r=Ue(e),n=r._frozenDsc;if(n)return n;const s=r.spanContext().traceState,i=s&&s.get("sentry.dsc"),o=i&&function(e){const t=function(e){if(e&&(v(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce(((e,t)=>{const r=Pe(t);return Object.entries(r).forEach((([t,r])=>{e[t]=r})),e}),{}):Pe(e)}(e);if(!t)return;const r=Object.entries(t).reduce(((e,[t,r])=>(t.match(Te)&&(e[t.slice(7)]=r),e)),{});return Object.keys(r).length>0?r:void 0}(i);if(o)return o;const a=Be(e.spanContext().traceId,t),c=Le(r),u=c.data||{},l=u["sentry.sample_rate"];null!=l&&(a.sample_rate=`${l}`);const d=u["sentry.source"],f=c.description;return"url"!==d&&f&&(a.transaction=f),function(){const e=oe(),t=e&&e.getOptions();return!!t&&(t.enableTracing||"tracesSampleRate"in t||"tracesSampler"in t)}()&&(a.sampled=String(function(e){const{traceFlags:t}=e.spanContext();return 1===t}(r))),t.emit("createDsc",a,r),a}class We extends Error{constructor(e,t="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=t}}var He,Ve;function Ke(e){return new Qe((t=>{t(e)}))}function Ge(e){return new Qe(((t,r)=>{r(e)}))}(Ve=He||(He={}))[Ve.PENDING=0]="PENDING",Ve[Ve.RESOLVED=1]="RESOLVED",Ve[Ve.REJECTED=2]="REJECTED";class Qe{constructor(e){Qe.prototype.__init.call(this),Qe.prototype.__init2.call(this),Qe.prototype.__init3.call(this),Qe.prototype.__init4.call(this),this._state=He.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(e){this._reject(e)}}then(e,t){return new Qe(((r,n)=>{this._handlers.push([!1,t=>{if(e)try{r(e(t))}catch(e){n(e)}else r(t)},e=>{if(t)try{r(t(e))}catch(e){n(e)}else n(e)}]),this._executeHandlers()}))}catch(e){return this.then((e=>e),e)}finally(e){return new Qe(((t,r)=>{let n,s;return this.then((t=>{s=!1,n=t,e&&e()}),(t=>{s=!0,n=t,e&&e()})).then((()=>{s?r(n):t(n)}))}))}__init(){this._resolve=e=>{this._setResult(He.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(He.REJECTED,e)}}__init3(){this._setResult=(e,t)=>{this._state===He.PENDING&&(x(t)?t.then(this._resolve,this._reject):(this._state=e,this._value=t,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===He.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach((e=>{e[0]||(this._state===He.RESOLVED&&e[1](this._value),this._state===He.REJECTED&&e[2](this._value),e[0]=!0)}))}}}function Je(e,t,r,n=0){return new Qe(((s,i)=>{const o=e[n];if(null===t||"function"!=typeof o)s(t);else{const a=o({...t},r);x(a)?a.then((t=>Je(e,t,r,n+1).then(s))).then(null,i):Je(e,a,r,n+1).then(s).then(null,i)}}))}let Ze,Ye,Xe;function et(e,t){const{extra:r,tags:n,user:s,contexts:i,level:o,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:l,attachments:d,propagationContext:f,transactionName:p,span:h}=t;tt(e,"extra",r),tt(e,"tags",n),tt(e,"user",s),tt(e,"contexts",i),e.sdkProcessingMetadata=V(e.sdkProcessingMetadata,a,2),o&&(e.level=o),p&&(e.transactionName=p),h&&(e.span=h),c.length&&(e.breadcrumbs=[...e.breadcrumbs,...c]),u.length&&(e.fingerprint=[...e.fingerprint,...u]),l.length&&(e.eventProcessors=[...e.eventProcessors,...l]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...f}}function tt(e,t,r){e[t]=V(e[t],r,1)}function rt(e,t,r,n,s,i){const{normalizeDepth:c=3,normalizeMaxBreadth:u=1e3}=e,d={...t,event_id:t.event_id||r.event_id||L(),timestamp:t.timestamp||l()},f=r.integrations||e.integrations.map((e=>e.name));!function(e,t){const{environment:r,release:n,dist:s,maxValueLength:i=250}=t;e.environment=e.environment||r||Ce,!e.release&&n&&(e.release=n),!e.dist&&s&&(e.dist=s),e.message&&(e.message=C(e.message,i));const o=e.exception&&e.exception.values&&e.exception.values[0];o&&o.value&&(o.value=C(o.value,i));const a=e.request;a&&a.url&&(a.url=C(a.url,i))}(d,e),function(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}(d,f),s&&s.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){const r=function(e){const t=o._sentryDebugIds;if(!t)return{};const r=Object.keys(t);return Xe&&r.length===Ye||(Ye=r.length,Xe=r.reduce(((r,n)=>{Ze||(Ze={});const s=Ze[n];if(s)r[s[0]]=s[1];else{const s=e(n);for(let e=s.length-1;e>=0;e--){const i=s[e],o=i&&i.filename,a=t[n];if(o&&a){r[o]=a,Ze[n]=[o,a];break}}}return r}),{})),Xe}(t);try{e.exception.values.forEach((e=>{e.stacktrace.frames.forEach((e=>{r&&e.filename&&(e.debug_id=r[e.filename])}))}))}catch(e){}}(d,e.stackParser);const p=function(e,t){if(!t)return e;const r=e?e.clone():new Z;return r.update(t),r}(n,r.captureContext);r.mechanism&&U(d,r.mechanism);const h=s?s.getEventProcessors():[],m=a("globalScope",(()=>new Z)).getScopeData();i&&et(m,i.getScopeData()),p&&et(m,p.getScopeData());const g=[...r.attachments||[],...m.attachments];return g.length&&(r.attachments=g),function(e,t){const{fingerprint:r,span:n,breadcrumbs:s,sdkProcessingMetadata:i}=t;!function(e,t){const{extra:r,tags:n,user:s,contexts:i,level:o,transactionName:a}=t,c=N(r);c&&Object.keys(c).length&&(e.extra={...c,...e.extra});const u=N(n);u&&Object.keys(u).length&&(e.tags={...u,...e.tags});const l=N(s);l&&Object.keys(l).length&&(e.user={...l,...e.user});const d=N(i);d&&Object.keys(d).length&&(e.contexts={...d,...e.contexts}),o&&(e.level=o),a&&"transaction"!==e.type&&(e.transaction=a)}(e,t),n&&function(e,t){e.contexts={trace:De(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:ze(t),...e.sdkProcessingMetadata};const r=Le(Ue(t)).description;r&&!e.transaction&&"transaction"===e.type&&(e.transaction=r)}(e,n),function(e,t){e.fingerprint=e.fingerprint?Array.isArray(e.fingerprint)?e.fingerprint:[e.fingerprint]:[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}(e,r),function(e,t){const r=[...e.breadcrumbs||[],...t];e.breadcrumbs=r.length?r:void 0}(e,s),function(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}(e,i)}(d,m),Je([...h,...m.eventProcessors],d,r).then((e=>(e&&function(e){const t={};try{e.exception.values.forEach((e=>{e.stacktrace.frames.forEach((e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)}))}))}catch(e){}if(0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const r=e.debug_meta.images;Object.entries(t).forEach((([e,t])=>{r.push({type:"sourcemap",code_file:e,debug_id:t})}))}(e),"number"==typeof c&&c>0?function(e,t,r){if(!e)return null;const n={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map((e=>({...e,...e.data&&{data:ge(e.data,t,r)}})))},...e.user&&{user:ge(e.user,t,r)},...e.contexts&&{contexts:ge(e.contexts,t,r)},...e.extra&&{extra:ge(e.extra,t,r)}};return e.contexts&&e.contexts.trace&&n.contexts&&(n.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(n.contexts.trace.data=ge(e.contexts.trace.data,t,r))),e.spans&&(n.spans=e.spans.map((e=>({...e,...e.data&&{data:ge(e.data,t,r)}})))),e.contexts&&e.contexts.flags&&n.contexts&&(n.contexts.flags=ge(e.contexts.flags,3,r)),n}(e,c,u):e)))}const nt=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];class st{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn&&(this._dsn=function(e){const t="string"==typeof e?function(e){const t=ce.exec(e);if(!t)return void O((()=>{console.error(`Invalid Sentry Dsn: ${e}`)}));const[r,n,s="",i="",o="",a=""]=t.slice(1);let c="",u=a;const l=u.split("/");if(l.length>1&&(c=l.slice(0,-1).join("/"),u=l.pop()),u){const e=u.match(/^\d+/);e&&(u=e[0])}return le({host:i,pass:s,path:c,projectId:u,port:o,protocol:r,publicKey:n})}(e):le(e);if(t)return t}(e.dsn)),this._dsn){const s=(t=this._dsn,r=e.tunnel,n=e._metadata?e._metadata.sdk:void 0,r||`${function(e){return`${function(e){const t=e.protocol?`${e.protocol}:`:"",r=e.port?`:${e.port}`:"";return`${t}//${e.host}${r}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/`}(t)}?${function(e,t){const r={sentry_version:"7"};return e.publicKey&&(r.sentry_key=e.publicKey),t&&(r.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(r).toString()}(t,n)}`);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:s})}var t,r,n;const s=["enableTracing","tracesSampleRate","tracesSampler"].find((t=>t in e&&null==e[t]));s&&O((()=>{console.warn(`[Sentry] Deprecation warning: \`${s}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)}))}captureException(e,t,r){const n=L();if(B(e))return n;const s={event_id:n,...t};return this._process(this.eventFromException(e,s).then((e=>this._captureEvent(e,s,r)))),s.event_id}captureMessage(e,t,r,n){const s={event_id:L(),...r},i=b(e)?e:String(e),o=null===(a=e)||b(a)||"object"!=typeof a&&"function"!=typeof a?this.eventFromMessage(i,t,s):this.eventFromException(e,s);var a;return this._process(o.then((e=>this._captureEvent(e,s,n)))),s.event_id}captureEvent(e,t,r){const n=L();if(t&&t.originalException&&B(t.originalException))return n;const s={event_id:n,...t},i=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(e,s,i||r)),s.event_id}captureSession(e){"string"!=typeof e.release||(this.sendSession(e),z(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then((r=>t.flush(e).then((e=>r&&e))))):Ke(!0)}close(e){return this.flush(e).then((e=>(this.getOptions().enabled=!1,this.emit("close"),e)))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some((({name:e})=>e.startsWith("Spotlight"))))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const t=this._integrations[e.name];$e(this,e,this._integrations),t||Oe(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let r=function(e,t,r,n){const s=Re(r),i=e.type&&"replay_event"!==e.type?e.type:"event";!function(e,t){t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]])}(e,r&&r.sdk);const o=function(e,t,r,n){const s=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&{sdk:t},...!!r&&n&&{dsn:ue(n)},...s&&{trace:N({...s})}}}(e,s,n,t);return delete e.sdkProcessingMetadata,be(o,[[{type:i},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(const e of t.attachments||[])r=we(r,Ae(e));const n=this.sendEnvelope(r);n&&n.then((t=>this.emit("afterSendEvent",e,t)),null)}sendSession(e){const t=function(e,t,r,n){const s=Re(r);return be({sent_at:(new Date).toISOString(),...s&&{sdk:s},...!!n&&t&&{dsn:ue(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(t)}recordDroppedEvent(e,t,r){if(this._options.sendClientReports){const n="number"==typeof r?r:1,s=`${e}:${t}`;this._outcomes[s]=(this._outcomes[s]||0)+n}}on(e,t){const r=this._hooks[e]=this._hooks[e]||[];return r.push(t),()=>{const e=r.indexOf(t);e>-1&&r.splice(e,1)}}emit(e,...t){const r=this._hooks[e];r&&r.forEach((e=>e(...t)))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,(e=>e)):Ke({})}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(e,t){const r={};return t.forEach((t=>{t&&$e(e,t,r)})),r}(this,e),Oe(this,e)}_updateSessionFromEvent(e,t){let r=!1,n=!1;const s=t.exception&&t.exception.values;if(s){n=!0;for(const e of s){const t=e.mechanism;if(t&&!1===t.handled){r=!0;break}}}const i="ok"===e.status;(i&&0===e.errors||i&&r)&&(z(e,{...r&&{status:"crashed"},errors:e.errors||Number(n||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Qe((t=>{let r=0;const n=setInterval((()=>{0==this._numProcessing?(clearInterval(n),t(!0)):(r+=1,e&&r>=e&&(clearInterval(n),t(!1)))}),1)}))}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,r=se(),n=ie()){const s=this.getOptions(),i=Object.keys(this._integrations);return!t.integrations&&i.length>0&&(t.integrations=i),this.emit("preprocessEvent",e,t),e.type||n.setLastEventId(e.event_id||t.event_id),rt(s,e,t,r,this,n).then((e=>{if(null===e)return e;e.contexts={trace:ae(r),...e.contexts};const t=function(e,t){const r=t.getPropagationContext();return r.dsc||Be(r.traceId,e)}(this,r);return e.sdkProcessingMetadata={dynamicSamplingContext:t,...e.sdkProcessingMetadata},e}))}_captureEvent(e,t={},r){return this._processEvent(e,t,r).then((e=>e.event_id),(e=>{}))}_processEvent(e,t,r){const n=this.getOptions(),{sampleRate:s}=n,i=ot(e),o=it(e),a=e.type||"error",c=`before send for type \`${a}\``,u=void 0===s?void 0:function(e){if("boolean"==typeof e)return Number(e);const t="string"==typeof e?parseFloat(e):e;return"number"!=typeof t||isNaN(t)||t<0||t>1?void 0:t}(s);if(o&&"number"==typeof u&&Math.random()>u)return this.recordDroppedEvent("sample_rate","error",e),Ge(new We(`Discarding event because it's not included in the random sample (sampling rate = ${s})`,"log"));const l="replay_event"===a?"replay":a,d=(e.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(e,t,r,d).then((r=>{if(null===r)throw this.recordDroppedEvent("event_processor",l,e),new We("An event processor returned `null`, will not send event.","log");if(t.data&&!0===t.data.__sentry__)return r;const s=function(e,t,r,n){const{beforeSend:s,beforeSendTransaction:i,beforeSendSpan:o}=t;if(it(r)&&s)return s(r,n);if(ot(r)){if(r.spans&&o){const t=[];for(const n of r.spans){const r=o(n);r?t.push(r):(Me||(O((()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")})),Me=!0),e.recordDroppedEvent("before_send","span"))}r.spans=t}if(i){if(r.spans){const e=r.spans.length;r.sdkProcessingMetadata={...r.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return i(r,n)}}return r}(this,n,r,t);return function(e,t){const r=`${t} must return \`null\` or a valid event.`;if(x(e))return e.then((e=>{if(!w(e)&&null!==e)throw new We(r);return e}),(e=>{throw new We(`${t} rejected with ${e}`)}));if(!w(e)&&null!==e)throw new We(r);return e}(s,c)})).then((n=>{if(null===n){if(this.recordDroppedEvent("before_send",l,e),i){const t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw new We(`${c} returned \`null\`, will not send event.`,"log")}const s=r&&r.getSession();if(!i&&s&&this._updateSessionFromEvent(s,n),i){const e=(n.sdkProcessingMetadata&&n.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(n.spans?n.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}const o=n.transaction_info;if(i&&o&&n.transaction!==e.transaction){const e="custom";n.transaction_info={...o,source:e}}return this.sendEvent(n,t),n})).then(null,(e=>{if(e instanceof We)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),new We(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${e}`)}))}_process(e){this._numProcessing++,e.then((e=>(this._numProcessing--,e)),(e=>(this._numProcessing--,e)))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map((([e,t])=>{const[r,n]=e.split(":");return{reason:r,category:n,quantity:t}}))}_flushOutcomes(){const e=this._clearOutcomes();if(0===e.length)return;if(!this._dsn)return;const t=(r=e,be((n=this._options.tunnel&&ue(this._dsn))?{dsn:n}:{},[[{type:"client_report"},{timestamp:l(),discarded_events:r}]]));var r,n;this.sendEnvelope(t)}}function it(e){return void 0===e.type}function ot(e){return"transaction"===e.type}function at(e,t){const r=ut(e,t),n={type:ft(t),value:pt(t)};return r.length&&(n.stacktrace={frames:r}),void 0===n.type&&""===n.value&&(n.value="Unrecoverable error caught"),n}function ct(e,t){return{exception:{values:[at(e,t)]}}}function ut(e,t){const r=t.stacktrace||t.stack||"",n=function(e){return e&&lt.test(e.message)?1:0}(t),s=function(e){return"number"==typeof e.framesToPop?e.framesToPop:0}(t);try{return e(r,n,s)}catch(e){}return[]}const lt=/Minified React error #\d+;/i;function dt(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function ft(e){const t=e&&e.name;return!t&&dt(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}function pt(e){const t=e&&e.message;return t?t.error&&"string"==typeof t.error.message?t.error.message:dt(e)&&Array.isArray(e.message)&&2==e.message.length?e.message[1]:t:"No error message"}function ht(e,t,r,n){const s=function(e,t,r,n){let s;if(g(t)&&t.error)return ct(e,t.error);if(y(t)||m(t,"DOMException")){const i=t;if("stack"in t)s=ct(e,t);else{const t=i.name||(y(i)?"DOMError":"DOMException"),o=i.message?`${t}: ${i.message}`:t;s=mt(e,o,r,n),F(s,o)}return"code"in i&&(s.tags={...s.tags,"DOMException.code":`${i.code}`}),s}return h(t)?ct(e,t):w(t)||_(t)?(s=function(e,t,r,n){const s=oe(),i=s&&s.getOptions().normalizeDepth,o=function(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const r=e[t];if(r instanceof Error)return r}}(t),a={__serialized__:ye(t,i)};if(o)return{exception:{values:[at(e,o)]},extra:a};const c={exception:{values:[{type:_(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:gt(t,{isUnhandledRejection:n})}]},extra:a};if(r){const t=ut(e,r);t.length&&(c.exception.values[0].stacktrace={frames:t})}return c}(e,t,r,void 0),U(s,{synthetic:!0}),s):(s=mt(e,t,r,n),F(s,`${t}`,void 0),U(s,{synthetic:!0}),s)}(e,t,r&&r.syntheticException||void 0,n);return U(s),s.level="error",r&&r.event_id&&(s.event_id=r.event_id),Ke(s)}function mt(e,t,r,n){const s={};if(n&&r){const n=ut(e,r);n.length&&(s.exception={values:[{value:t,stacktrace:{frames:n}}]}),U(s,{synthetic:!0})}if(b(t)){const{__sentry_template_string__:e,__sentry_template_values__:r}=t;return s.logentry={message:e,params:r},s}return s.message=t,s}function gt(e,{isUnhandledRejection:t}){const r=function(e,t=40){const r=Object.keys(j(e));r.sort();const n=r[0];if(!n)return"[object has no keys]";if(n.length>=t)return C(n,t);for(let e=r.length;e>0;e--){const n=r.slice(0,e).join(", ");if(!(n.length>t))return e===r.length?n:C(n,t)}return""}(e),n=t?"promise rejection":"exception";return g(e)?`Event \`ErrorEvent\` captured as ${n} with message \`${e.message}\``:_(e)?`Event \`${function(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e)}\` (type=${e.type}) captured as ${n}`:`Object captured as ${n} with keys: ${r}`}const yt=o;class vt extends st{constructor(e){const t={parentSpanIsAlwaysRootSpan:!0,...e};!function(e,t,r=[t],n="npm"){const s=e._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${t}`,packages:r.map((e=>({name:`${n}:@sentry/${e}`,version:i}))),version:i}),e._metadata=s}(t,"browser",["browser"],yt.SENTRY_SDK_SOURCE||"npm"),super(t),t.sendClientReports&&yt.document&&yt.document.addEventListener("visibilitychange",(()=>{"hidden"===yt.document.visibilityState&&this._flushOutcomes()}))}eventFromException(e,t){return ht(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",r){return function(e,t,r="info",n,s){const i=mt(e,t,n&&n.syntheticException||void 0,s);return i.level=r,n&&n.event_id&&(i.event_id=n.event_id),Ke(i)}(this._options.stackParser,e,t,r,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled())return;const t=function(e,{metadata:t,tunnel:r,dsn:n}){const s={event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!r&&!!n&&{dsn:ue(n)}},i=function(e){return[{type:"user_report"},e]}(e);return be(s,[i])}(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(t)}_prepareEvent(e,t,r){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,r)}}function bt(e,t){const r="string"==typeof t?t:void 0,n="string"!=typeof t?{captureContext:t}:void 0;return se().captureMessage(e,r,n)}const wt={},_t={};function xt(){"console"in o&&R.forEach((function(e){e in o.console&&function(e,t,r){if(!(t in e))return;const n=e[t],s=r(n);"function"==typeof s&&function(e,t){try{const r=t.prototype||{};e.prototype=t.prototype=r,P(e,"__sentry_original__",t)}catch(e){}}(s,n);try{e[t]=s}catch(e){}}(o.console,e,(function(t){return I[e]=t,function(...t){!function(e,t){const r=e&&wt[e];if(r)for(const e of r)try{e(t)}catch(e){}}("console",{args:t,level:e});const r=I[e];r&&r.apply(o.console,t)}}))}))}function St(e){return"warn"===e?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}const At=(e={})=>{const t=e.levels||R,r=!!e.handled;return{name:"CaptureConsole",setup(e){"console"in o&&function(e){const t="console";!function(e,t){wt[e]=wt[e]||[],wt[e].push(t)}(t,e),function(e,t){if(!_t[e]){_t[e]=!0;try{t()}catch(e){}}}(t,xt)}((({args:n,level:s})=>{oe()===e&&t.includes(s)&&function(e,t,r){const n={level:St(t),extra:{arguments:e}};!function(...e){const t=ne(c());if(2===e.length){const[r,n]=e;return r?t.withSetScope(r,n):t.withScope(n)}t.withScope(e[0])}((s=>{if(s.addEventProcessor((e=>(e.logger="console",U(e,{handled:r,type:"console"}),e))),"assert"===t){if(!e[0]){const t=`Assertion failed: ${T(e.slice(1)," ")||"console.assert"}`;s.setExtra("arguments",e.slice(1)),bt(t,n)}return}const i=e.find((e=>e instanceof Error));if(i)return o=i,a=n,void se().captureException(o,function(e){if(e)return function(e){return e instanceof Z||"function"==typeof e}(e)||function(e){return Object.keys(e).some((e=>nt.includes(e)))}(e)?{captureContext:e}:e}(a));var o,a;bt(T(e," "),n)}))}(n,s,r)}))}}};const Et=o,kt={};function Rt(e){kt[e]=void 0}function It(e,t,r=function(e){const t=[];function r(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(n){if(!(void 0===e||t.length<e))return Ge(new We("Not adding Promise because buffer limit was reached."));const s=n();return-1===t.indexOf(s)&&t.push(s),s.then((()=>r(s))).then(null,(()=>r(s).then(null,(()=>{})))),s},drain:function(e){return new Qe(((r,n)=>{let s=t.length;if(!s)return r(!0);const i=setTimeout((()=>{e&&e>0&&r(!1)}),e);t.forEach((e=>{Ke(e).then((()=>{--s||(clearTimeout(i),r(!0))}),n)}))}))}}}(e.bufferSize||64)){let n={};return{send:function(s){const i=[];if(_e(s,((t,r)=>{const s=ke(r);if(function(e,t,r=Date.now()){return function(e,t){return e[t]||e.all||0}(e,t)>r}(n,s)){const n=Ot(t,r);e.recordDroppedEvent("ratelimit_backoff",s,n)}else i.push(t)})),0===i.length)return Ke({});const o=be(s[0],i),a=t=>{_e(o,((r,n)=>{const s=Ot(r,n);e.recordDroppedEvent(t,ke(n),s)}))};return r.add((()=>t({body:Se(o)}).then((e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode),n=function(e,{statusCode:t,headers:r},n=Date.now()){const s={...e},i=r&&r["x-sentry-rate-limits"],o=r&&r["retry-after"];if(i)for(const e of i.trim().split(",")){const[t,r,,,i]=e.split(":",5),o=parseInt(t,10),a=1e3*(isNaN(o)?60:o);if(r)for(const e of r.split(";"))"metric_bucket"===e&&i&&!i.split(";").includes("custom")||(s[e]=n+a);else s.all=n+a}else o?s.all=n+function(e,t=Date.now()){const r=parseInt(`${e}`,10);if(!isNaN(r))return 1e3*r;const n=Date.parse(`${e}`);return isNaN(n)?6e4:n-t}(o,n):429===t&&(s.all=n+6e4);return s}(n,e),e)),(e=>{throw a("network_error"),e})))).then((e=>e),(e=>{if(e instanceof We)return a("queue_overflow"),Ke({});throw e}))},flush:e=>r.drain(e)}}function Ot(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}function $t(e,t=function(e){const t=kt[e];if(t)return t;let r=Et[e];if((n=r)&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(n.toString()))return kt[e]=r.bind(Et);var n;const s=Et.document;if(s&&"function"==typeof s.createElement)try{const t=s.createElement("iframe");t.hidden=!0,s.head.appendChild(t);const n=t.contentWindow;n&&n[e]&&(r=n[e]),s.head.removeChild(t)}catch(e){}return r?kt[e]=r.bind(Et):r}("fetch")){let r=0,n=0;return It(e,(function(s){const i=s.body.length;r+=i,n++;const o={body:s.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:r<=6e4&&n<15,...e.fetchOptions};if(!t)return Rt("fetch"),Ge("No fetch implementation available");try{return t(e.url,o).then((e=>(r-=i,n--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}})))}catch(e){return Rt("fetch"),r-=i,n--,Ge(e)}}))}function Ct(e,t,r,n){const s={filename:e,function:"<anonymous>"===t?de:t,in_app:!0};return void 0!==r&&(s.lineno=r),void 0!==n&&(s.colno=n),s}const Tt=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Pt=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,jt=/\((\S*)(?::(\d+))(?::(\d+))\)/,Mt=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Dt=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Nt=function(...e){const t=e.sort(((e,t)=>e[0]-t[0])).map((e=>e[1]));return(e,r=0,n=0)=>{const s=[],i=e.split("\n");for(let e=r;e<i.length;e++){const r=i[e];if(r.length>1024)continue;const o=fe.test(r)?r.replace(fe,"$1"):r;if(!o.match(/\S*Error: /)){for(const e of t){const t=e(o);if(t){s.push(t);break}}if(s.length>=50+n)break}}return function(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test(he(t).function||"")&&t.pop(),t.reverse(),pe.test(he(t).function||"")&&(t.pop(),pe.test(he(t).function||"")&&t.pop()),t.slice(0,50).map((e=>({...e,filename:e.filename||he(t).filename,function:e.function||de})))}(s.slice(n))}}([30,e=>{const t=Tt.exec(e);if(t){const[,e,r,n]=t;return Ct(e,de,+r,+n)}const r=Pt.exec(e);if(r){if(r[2]&&0===r[2].indexOf("eval")){const e=jt.exec(r[2]);e&&(r[2]=e[1],r[3]=e[2],r[4]=e[3])}const[e,t]=qt(r[1]||de,r[2]);return Ct(t,e,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],[50,e=>{const t=Mt.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const e=Dt.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],r=t[1]||de;return[r,e]=qt(r,e),Ct(e,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),qt=(e,t)=>{const r=-1!==e.indexOf("safari-extension"),n=-1!==e.indexOf("safari-web-extension");return r||n?[-1!==e.indexOf("@")?e.split("@")[0]:de,r?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]};r(7642),r(8004),r(3853),r(5876),r(2475),r(5024),r(1698),r(7333),r(1393),r(8992),r(4520),r(1454),r(5509),r(5223),r(321),r(1927),r(1632),r(4377),r(6771),r(2516),r(8931),r(2514),r(5694),r(2774),r(9536),r(1926),r(4483),r(6215);const Lt=async()=>{};r(4905),r(8872);const Ft=e=>({error:e,value:void 0}),Ut=e=>({error:void 0,value:e}),Bt=e=>void 0!==e.error,zt=e=>void 0===e.error&&void 0!==e.value,Wt=e=>e.reduce(((e,t)=>(zt(t)&&e.push(t.value),e)),[]),Ht=e=>e.value;r(4648),r(3215);class Vt{props;constructor(e){this.props=Object.freeze(e)}mapBy(e){return e(this.props)}is(e){if(null==e)return!1;if(!(e instanceof Vt&&e instanceof this.constructor))return!1;for(const t in this.props){if(!Object.hasOwn(e.props,t))return!1;const r=this.props[t],n=e.props[t];if(!(r instanceof Vt?r.is(n):Array.isArray(r)&&Array.isArray(n)?Kt(r,n):r===n))return!1}return!0}toJSON(){return this.props}duplicate(){return Object.assign(Object.create(Object.getPrototypeOf(this)),this)}}const Kt=(e,t)=>Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(((e,r)=>e instanceof Vt?e.is(t[r]):e===t[r]));class Gt extends Vt{constructor(e){super({value:e})}get value(){return this.props.value}}class Qt{id;props;constructor(e,t){this.id=e,this.props=t}mapBy(e){return e(this.id,this.props)}is(e){return null!=e&&e instanceof Qt&&this.id.is(e.id)}}class Jt extends Gt{}class Zt extends Qt{}class Yt extends Vt{}class Xt extends Vt{}const er=e=>e.mapBy(((e,t)=>({...t.tweetUser.mapBy((e=>e)),hashtags:new Set(t.hashtags??[]),mediaType:t.mediaType,tweetId:e.value,tweetTime:t.tweetTime,downloadTime:t.downloadTime,thumbnail:t.thumbnail}))),tr=e=>{const t=new Jt(e.tweetId);return new Zt(t,{downloadTime:e.downloadTime,hashtags:Array.from(e.hashtags??nr()),thumbnail:e.thumbnail,mediaType:e.mediaType,tweetTime:e.tweetTime,tweetUser:new Yt({displayName:e.displayName,screenName:e.screenName,userId:e.userId})})},rr=e=>new Xt({displayName:e.displayName,downloadTime:e.downloadTime,hashtags:Array.from(e.hashtags??nr()),mediaType:e.mediaType,screenName:e.screenName,thumbnail:e.thumbnail??"",tweetId:e.tweetId,userId:e.userId,tweetTime:e.tweetTime}),nr=()=>new Set;function sr(e){return function(t,r){return e.chain(t,(function(t){return e.map(r(t),(function(){return t}))}))}}function ir(e){return function(t,r){return function(n){return e.chain(n,(function(n){return e.map(r(n),(function(e){var r;return Object.assign({},n,((r={})[t]=e,r))}))}))}}}function or(e,t,r,n,s,i,o,a,c){switch(arguments.length){case 1:return e;case 2:return function(){return t(e.apply(this,arguments))};case 3:return function(){return r(t(e.apply(this,arguments)))};case 4:return function(){return n(r(t(e.apply(this,arguments))))};case 5:return function(){return s(n(r(t(e.apply(this,arguments)))))};case 6:return function(){return i(s(n(r(t(e.apply(this,arguments))))))};case 7:return function(){return o(i(s(n(r(t(e.apply(this,arguments)))))))};case 8:return function(){return a(o(i(s(n(r(t(e.apply(this,arguments))))))))};case 9:return function(){return c(a(o(i(s(n(r(t(e.apply(this,arguments)))))))))}}}function ar(e,t,r,n,s,i,o,a,c){switch(arguments.length){case 1:return e;case 2:return t(e);case 3:return r(t(e));case 4:return n(r(t(e)));case 5:return s(n(r(t(e))));case 6:return i(s(n(r(t(e)))));case 7:return o(i(s(n(r(t(e))))));case 8:return a(o(i(s(n(r(t(e)))))));case 9:return c(a(o(i(s(n(r(t(e))))))));default:for(var u=arguments[0],l=1;l<arguments.length;l++)u=arguments[l](u);return u}}var cr=function(e,t){var r="number"==typeof e?function(t){return t.length>=e}:e;return function(){var e=Array.from(arguments);return r(arguments)?t.apply(this,e):function(r){return t.apply(void 0,function(e,t,r){if(r||2===arguments.length)for(var n,s=0,i=t.length;s<i;s++)!n&&s in t||(n||(n=Array.prototype.slice.call(t,0,s)),n[s]=t[s]);return e.concat(n||Array.prototype.slice.call(t))}([r],e,!1))}}},ur={_tag:"None"},lr=function(e){return{_tag:"Some",value:e}},dr=function(e){return{_tag:"Left",left:e}},fr=function(e){return{_tag:"Right",right:e}},pr={};function hr(e){return function(t){return function(r){return e.fromEither("None"===r._tag?dr(t()):fr(r.value))}}}function mr(e,t){var r=function(e){return function(t){return or(t,e.fromEither)}}(e),n=sr(t);return function(e,t){return n(e,r(t))}}function gr(e){return function(t,r){return e.map(t,(function(){return r}))}}function yr(e){var t=gr(e);return function(e){return t(e,void 0)}}Object.prototype.hasOwnProperty;var vr=dr,br=fr,wr=cr(2,(function(e,t){return Pr(e)?e:t(e.right)})),_r=function(e,t){return ar(e,Ar(t))},xr=function(e,t){return ar(e,Rr(t))},Sr="Either",Ar=function(e){return function(t){return Pr(t)?t:br(e(t.right))}},Er={URI:Sr,map:_r},kr=(cr(2,gr(Er)),yr(Er),br),Rr=function(e){return function(t){return Pr(t)?t:Pr(e)?e:br(t.right(e.right))}},Ir={URI:Sr,map:_r,ap:xr},Or={URI:Sr,map:_r,ap:xr,chain:wr},$r=function(e){return function(t){return Pr(t)?vr(e(t.left)):t}},Cr={URI:Sr,fromEither:function(e){return e}},Tr=hr(Cr),Pr=function(e){return"Left"===e._tag},jr=function(e,t){return function(r){return Pr(r)?e(r.left):t(r.right)}},Mr=(sr(Or),function(e){return function(t){return null==t?vr(e):br(t)}}),Dr=kr(pr),Nr=ir(Or),qr=wr;function Lr(e){return or(br,e.of)}function Fr(e){return function(t){return e.map(t,br)}}function Ur(e){return function(e,t){return function(r){return function(n){return e.map(n,(function(e){return t.map(e,r)}))}}}(e,Er)}function Br(e){return function(e,t){return function(r){return function(n){return e.ap(e.map(n,(function(e){return function(r){return t.ap(e,r)}})),r)}}}(e,Ir)}function zr(e){return function(t,r){return function(n){return e.map(n,jr(t,r))}}}function Wr(e,t){var r=sr(t);return function(t,n){return r(t,or(n,e.fromIO))}}var Hr=function(e){return function(){return Promise.resolve().then(e)}},Vr=function(e,t){return ar(e,Gr(t))},Kr=function(e,t){return ar(e,Qr(t))},Gr=function(e){return function(t){return function(){return Promise.resolve().then(t).then(e)}}},Qr=function(e){return function(t){return function(){return Promise.all([Promise.resolve().then(t),Promise.resolve().then(e)]).then((function(e){return(0,e[0])(e[1])}))}}},Jr=function(e){return function(){return Promise.resolve(e)}},Zr=cr(2,(function(e,t){return function(){return Promise.resolve().then(e).then((function(e){return t(e)()}))}})),Yr="Task",Xr={URI:Yr,map:Vr},en=(cr(2,gr(Xr)),yr(Xr),{URI:Yr,of:Jr}),tn={URI:Yr,map:Vr,ap:Kr},rn={URI:Yr,map:Vr,ap:Kr,chain:Zr},nn={URI:Yr,map:Vr,of:Jr,ap:Kr,chain:Zr},sn={URI:Yr,fromIO:Hr},on=(sr(rn),Wr(sn,rn),function(e,t,r,n){return new(r||(r=Promise))((function(s,i){function o(e){try{c(n.next(e))}catch(e){i(e)}}function a(e){try{c(n.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?s(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,a)}c((n=n.apply(e,t||[])).next())}))}),an=Lr(en),cn=Fr(Xr),un=or(Hr,cn),ln=cn,dn=Jr,fn=zr(Xr),pn=function(e,t){return function(){return on(void 0,void 0,void 0,(function(){var r;return function(e,t){var r,n,s,i,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(a){return function(c){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;i&&(i=0,a[0]&&(o=0)),o;)try{if(r=1,n&&(s=2&a[0]?n.return:a[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,a[1])).done)return s;switch(n=0,s&&(a=[2&a[0],s.value]),a[0]){case 0:case 1:s=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!((s=(s=o.trys).length>0&&s[s.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!s||a[1]>s[0]&&a[1]<s[3])){o.label=a[1];break}if(6===a[0]&&o.label<s[1]){o.label=s[1],s=a;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(a);break}s[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],n=0}finally{r=s=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,e().then(fr)];case 1:return[2,n.sent()];case 2:return r=n.sent(),[2,dr(t(r))];case 3:return[2]}}))}))}},hn=(function(e){var t=function(e){return function(t){return function(r){return e.chain(r,(function(r){return Pr(r)?t(r.left):e.of(r)}))}}}(e)}(nn),function(e,t){return ar(e,mn(t))}),mn=Ur(Xr),gn=(function(){}(Xr),cr(2,function(e){return function(t,r){return e.map(t,$r(r))}}(Xr))),yn=Br(tn),vn=cr(2,function(e){return function(t,r){return e.chain(t,(function(t){return Pr(t)?e.of(t):r(t.right)}))}}(nn)),bn="TaskEither",wn={URI:bn,map:hn},_n=(cr(2,gr(wn)),yr(wn),{URI:bn,map:hn,ap:function(e,t){return ar(e,yn(t))},chain:vn}),xn={URI:bn,fromEither:dn},Sn={URI:bn,fromIO:un},An={URI:bn,fromIO:un,fromTask:ln},En=cr(2,sr(_n));mr(xn,_n),Wr(Sn,_n),function(e,t){var r=sr(t)}(An,_n);var kn=ir(_n),Rn=r(8934),In=r(3643);const On=async e=>{const t=await e.prepareTransaction(["hashtag","history"],"readwrite");return{historyCollection:t.tx.objectStore("history"),hashtagCollection:t.tx.objectStore("hashtag"),completeTransaction:t.completeTx,abortTransaction:t.abortTx}},$n=(e=Lt)=>t=>({error:(0,Rn.toError)(t),abort:e}),Cn=e=>async t=>{const r=await t.get(e.hashtag);r&&r.tweetIds.has(e.tweetId)&&(r.tweetIds.delete(e.tweetId),await t.put(r))},Tn=e=>async t=>{let r=await t.get(e.hashtag);r?r.tweetIds.add(e.tweetId):r={name:e.hashtag,tweetIds:Pn(e.tweetId)},await t.put(r)},Pn=(...e)=>new Set(e);class jn extends Error{constructor(e){super(`Download record not found. (id: ${e})`)}}class Mn extends Vt{}class Dn extends Vt{}class Nn extends Vt{get screenName(){return this.props.screenName}get tweetId(){return this.props.tweetId}}class qn extends Vt{toJSON(){return{version:"5.0.0",items:this.props.items}}static fromJSON(e){return new qn({items:e.items.map((e=>new Xt(e)))})}}r(8159),r(7550);class Ln{name;occuredAt;constructor(e){this.name=e,this.occuredAt=new Date}}class Fn extends Ln{constructor(){super("client:synced")}}r(9920),r(3949),r(4603),r(7566),r(8721);class Un{static day(e){return 864e5*e}static hour(e){return 36e5*e}static minute(e){return 6e4*e}static second(e){return 1e3*e}}class Bn extends Gt{}const zn=Un.minute(30);class Wn extends Qt{events;constructor(e,t){super(e,t),this.events=[]}get syncToken(){return this.props.syncToken}get uninstallUrl(){return((e,t)=>{if(!e.startsWith("/"))throw new Error(`path should starts with \`/\`. (path: ${e})`);const r=new URL(e,"https://api.webext.prod.mediaharvest.app");return t&&Object.entries(t).forEach((([e,t])=>r.searchParams.set(e,String(t)))),r})("/v1/clients/"+this.id.value+"/uninstall",{uninstallCode:this.props.uninstallCode}).href}get shouldSync(){return Date.now()-this.props.syncedAt>=zn}get usageStatistics(){return this.props.usageStatistics}updateSyncToken(e){this.props={...this.props,syncedAt:Date.now(),syncToken:e},this.events.push(new Fn)}}class Hn extends Vt{increase(e){return new Hn({downloadCount:this.props.downloadCount+e.downloadCount,trafficUsage:this.props.trafficUsage+e.trafficUsage})}isGreaterThan(e){return this.props.downloadCount>=e.mapBy((e=>e.downloadCount))&&this.props.trafficUsage>=e.mapBy((e=>e.trafficUsage))}isLessThan(e){return this.props.downloadCount<=e.mapBy((e=>e.downloadCount))&&this.props.trafficUsage<=e.mapBy((e=>e.trafficUsage))}}var Vn=r(1958),Kn=r(9389),Gn=r(5479),Qn=r(4428);class Jn{config;constructor(e){this.config=e}static initWithConfig(e){return new Jn(e)}get httpHandler(){return new Kn.NC({requestTimeout:this.config.timeout??Un.second(5)})}async makeSigner(){return new Qn.BB({region:this.config.region,service:"execute-api",credentials:"function"==typeof this.config.credentials?await this.config.credentials():this.config.credentials,sha256:Vn.I})}async send(e,t){let r;try{const n=e.prepareRequest({protocol:"https",hostname:this.config.hostName,headers:{host:this.config.hostName,"X-Api-Key":this.config.apiKey,"X-MediaHarvest-Version":this.config.clientVersion}}),s=await this.makeSigner(),i=await s.sign(n);if(!Gn.Kd.isInstance(i))return Ft(new Error("Signed request is not a valid HttpRequest."));r=this.httpHandler;const{response:o}=await this.httpHandler.handle(i,t),a=await e.resolveResponse(o);return r.destroy(),Ut(a)}catch(e){return r?.destroy(),Ft(e)}}}r(8335);const Zn=e=>({$metadata:{httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]}});class Yn{path;config;method;constructor(e,t,r){Xn(t),this.config=r,this.path=t,this.method=e}}const Xn=e=>{if(!e.startsWith("/"))throw new Error(`path should starts with \`/\`. (path: ${e})`)};class es extends Error{name="CommandResopnseError";constructor(e,t){super(e+"\n"+JSON.stringify(t))}}class ts extends Error{}ts.prototype.name="InvalidTokenError";class rs extends Yn{constructor(e){super("POST","/v1/clients",e)}prepareRequest(e){return new Gn.Kd({protocol:e.protocol,method:this.method,hostname:e.hostname,path:this.path,headers:{...e.headers,"Content-Type":"application/json"},body:JSON.stringify(this.config.initStats)})}async resolveResponse(e){const t=JSON.parse((new TextDecoder).decode(await(0,Kn.kv)(e.body))),r=Zn(e);if(201===e.statusCode)try{const e=function(e,t){if("string"!=typeof e)throw new ts("Invalid token specified: must be a string");t||(t={});const r=!0===t.header?0:1,n=e.split(".")[r];if("string"!=typeof n)throw new ts(`Invalid token specified: missing part #${r+1}`);let s;try{s=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return function(e){return decodeURIComponent(atob(e).replace(/(.)/g,((e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r})))}(t)}catch(e){return atob(t)}}(n)}catch(e){throw new ts(`Invalid token specified: invalid base64 for part #${r+1} (${e.message})`)}try{return JSON.parse(s)}catch(e){throw new ts(`Invalid token specified: invalid json for part #${r+1} (${e.message})`)}}(t.token);return{...r,syncToken:t.token,clientUUID:e.uuid,uninstallCode:t.uninstallCode??e.uninstallCode}}catch(e){let n=t?.error??t?.message;throw e instanceof Error&&(n=e.message),n??="Failed to decode response body.",new es(n,r)}throw new es(t?.error??t?.message??"Failed to request.",r)}}class ns extends Yn{constructor(e){super("PUT","/v1/clients/"+e.clientId+"/stats",e)}prepareRequest(e){return new Gn.Kd({protocol:e.protocol,method:this.method,hostname:e.hostname,path:this.path,headers:{...e.headers,"Content-Type":"application/json","x-csrf-token":this.config.syncToken},body:JSON.stringify(this.config.stats)})}async resolveResponse(e){const t=JSON.parse((new TextDecoder).decode(await(0,Kn.kv)(e.body))),r=Zn(e);if(200===e.statusCode)return{...r,syncToken:t.token};throw new es(t?.error??t?.message??"Failed to request.",r)}}const ss="unknown",is={csrfToken:ss,syncedAt:0,uninstallCode:ss,uuid:ss},os={downloadCount:0,trafficUsage:0};var as=r(6815),cs=r.n(as);const us={enableAria2:!1,aggressiveMode:!1,askWhereToSave:!1},ls={autoRevealNsfw:!1,includeVideoThumbnail:!1,keyboardShortcut:!0};r(6986);const ds=(...e)=>t=>{const r={};for(const n of e)Object.hasOwn(t,n)&&(r[n]=t[n]);return r};var fs=r(7975),ps=r.n(fs),hs=r(2424),ms=r.n(hs);const gs=/^[^<>:"/\\|?*][^<>:"\\|?*]+$/;class ys extends Vt{static validateDirectory(e){return e.length>4096?"Maximum path is 4096 characters.":gs.test(e)?e.split("/").every((e=>ms()(e)===e))?void 0:"Contains illegal characters.":'Directory path contains reserved characters. (`\\`, `?`, `<`, `>`, `,`, `:`, `*`, `|`, and `"`)'}static validateFilenamePattern(e){if(!(e.includes("{hash}")||e.includes("{tweetId}")&&e.includes("{serial}")))return"The pattern should contains at least {hash} or {tweetId} and {serial}"}validate(){const e=ys.validateDirectory(this.props.directory);if(e)return e;return ys.validateFilenamePattern(this.props.filenamePattern)||void 0}makeFilename(e,t){const{screenName:r,id:n,createdAt:s,userId:i,hash:o,serial:a}=e.mapBy((e=>({id:e.tweetId,createdAt:e.createdAt,hash:e.hash,serial:e.serial,...e.tweetUser.mapBy(ds("screenName","userId"))}))),c=new Date;if(0===this.props.filenamePattern.length)throw new Error("Filename pattern can't be empty.");const u=this.props.filenamePattern.join("-").replace("{account}",r).replace("{tweetId}",n).replace("{serial}",String(a).padStart(2,"0")).replace("{hash}",o).replace("{date}",bs(c)).replace("{datetime}",vs(c)).replace("{tweetDate}",bs(s)).replace("{tweetDatetime}",vs(s)).replace("{accountId}",i).replace("{underscoreDatetime}",ws(c)).replace("{underscroeTweetDatetime}",ws(s)).replace("{tweetTimestamp}",s.getTime().toString()).replace("{timestamp}",c.getTime().toString());return fs.posix.format({dir:t?.noDir?void 0:this.makeAggregationDirectory(e),name:u,ext:e.mapBy((e=>e.ext))})}makeAggregationDirectory(e){const t=this.props.noSubDirectory?"":this.props.directory;if(!this.props.fileAggregation)return t;if("{account}"===this.props.groupBy)return fs.posix.join(t,e.mapBy((e=>e.tweetUser.mapBy((e=>e.screenName)))));throw new Error("Invalid `groupBy` settings: "+this.props.groupBy)}}const vs=e=>String(e.getFullYear())+String(e.getMonth()+1).padStart(2,"0")+String(e.getDate()).padStart(2,"0")+String(e.getHours()).padStart(2,"0")+String(e.getMinutes()).padStart(2,"0")+String(e.getSeconds()).padStart(2,"0"),bs=e=>String(e.getFullYear())+String(e.getMonth()+1).padStart(2,"0")+String(e.getDate()).padStart(2,"0"),ws=e=>String(e.getFullYear())+String(e.getMonth()+1).padStart(2,"0")+String(e.getDate()).padStart(2,"0")+"_"+String(e.getHours()).padStart(2,"0")+String(e.getMinutes()).padStart(2,"0")+String(e.getSeconds()).padStart(2,"0"),_s=new ys({directory:"twitter_media_harvest",noSubDirectory:!1,filenamePattern:["{account}","{tweetId}","{serial}"],groupBy:"{account}",fileAggregation:!1}),xs=Un.minute(5);class Ss extends Gt{}class As extends Qt{get quota(){return this.props.quota}get isCooldown(){return!!this.props.warnedAt&&Date.now()-this.props.warnedAt.getTime()<xs}get warnedAt(){return this.props.warnedAt}get isRealTime(){return this.props.isRealtime}static create(e,t){return new As(new Ss(e),{...t})}async warnBy(e,t={force:!1}){if(!t.force&&this.isCooldown)return;const r=await e();return r||(this.props={...this.props,warnedAt:new Date(Date.now())}),r}updateQuota(e){this.props={...this.props,quota:e}}}class Es extends Vt{get remaining(){return this.props.quota}get resetTime(){return this.props.resetAt}get isReset(){return new Date(Date.now())>=this.props.resetAt}}const ks=e=>e.mapBy(((e,t)=>({[e.value]:{quota:t.quota.remaining,resetAt:t.quota.resetTime.getTime(),warnedAt:t.warnedAt?.getTime()}}))),Rs=Object.freeze({ignoreFilenameOverwritten:!1});class Is extends Vt{get value(){return this.props.value}}r(1517),r(1379),r(3777),r(4190),r(2359),r(6097),r(7273),r(7415),r(9929),r(7583),r(5122),r(230),r(7268),r(9733);const Os=(e,t)=>t.some((t=>e instanceof t));let $s,Cs;const Ts=new WeakMap,Ps=new WeakMap,js=new WeakMap;let Ms={get(e,t,r){if(e instanceof IDBTransaction){if("done"===t)return Ts.get(e);if("store"===t)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return qs(e[t])},set:(e,t,r)=>(e[t]=r,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function Ds(e){Ms=e(Ms)}function Ns(e){return"function"==typeof e?(t=e,(Cs||(Cs=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(Ls(this),e),qs(this.request)}:function(...e){return qs(t.apply(Ls(this),e))}):(e instanceof IDBTransaction&&function(e){if(Ts.has(e))return;const t=new Promise(((t,r)=>{const n=()=>{e.removeEventListener("complete",s),e.removeEventListener("error",i),e.removeEventListener("abort",i)},s=()=>{t(),n()},i=()=>{r(e.error||new DOMException("AbortError","AbortError")),n()};e.addEventListener("complete",s),e.addEventListener("error",i),e.addEventListener("abort",i)}));Ts.set(e,t)}(e),Os(e,$s||($s=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction]))?new Proxy(e,Ms):e);var t}function qs(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,r)=>{const n=()=>{e.removeEventListener("success",s),e.removeEventListener("error",i)},s=()=>{t(qs(e.result)),n()},i=()=>{r(e.error),n()};e.addEventListener("success",s),e.addEventListener("error",i)}));return js.set(t,e),t}(e);if(Ps.has(e))return Ps.get(e);const t=Ns(e);return t!==e&&(Ps.set(e,t),js.set(t,e)),t}const Ls=e=>js.get(e),Fs=["get","getKey","getAll","getAllKeys","count"],Us=["put","add","delete","clear"],Bs=new Map;function zs(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(Bs.get(t))return Bs.get(t);const r=t.replace(/FromIndex$/,""),n=t!==r,s=Us.includes(r);if(!(r in(n?IDBIndex:IDBObjectStore).prototype)||!s&&!Fs.includes(r))return;const i=async function(e,...t){const i=this.transaction(e,s?"readwrite":"readonly");let o=i.store;return n&&(o=o.index(t.shift())),(await Promise.all([o[r](...t),s&&i.done]))[0]};return Bs.set(t,i),i}Ds((e=>({...e,get:(t,r,n)=>zs(t,r)||e.get(t,r,n),has:(t,r)=>!!zs(t,r)||e.has(t,r)})));const Ws=["continue","continuePrimaryKey","advance"],Hs={},Vs=new WeakMap,Ks=new WeakMap,Gs={get(e,t){if(!Ws.includes(t))return e[t];let r=Hs[t];return r||(r=Hs[t]=function(...e){Vs.set(this,Ks.get(this)[t](...e))}),r}};async function*Qs(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;const r=new Proxy(t,Gs);for(Ks.set(r,t),js.set(r,Ls(t));t;)yield r,t=await(Vs.get(r)||t.continue()),Vs.delete(r)}function Js(e,t){return t===Symbol.asyncIterator&&Os(e,[IDBIndex,IDBObjectStore,IDBCursor])||"iterate"===t&&Os(e,[IDBIndex,IDBObjectStore])}Ds((e=>({...e,get:(t,r,n)=>Js(t,r)?Qs:e.get(t,r,n),has:(t,r)=>Js(t,r)||e.has(t,r)})));class Zs{version;callbacks;transactionCallbacks;constructor(e){this.version=e,this.callbacks={},this.transactionCallbacks={}}onUpgrade(e){return this.callbacks.upgrade=e,this}onBlocked(e){return this.callbacks.blocked=e,this}onBlocking(e){return this.callbacks.blocking=e,this}onTerminated(e){return this.callbacks.terminated=e,this}onQuotaExceeded(e){this.transactionCallbacks.quotaExceeded=e}async connect(){return await function(e,t,{blocked:r,upgrade:n,blocking:s,terminated:i}={}){const o=indexedDB.open(e,t),a=qs(o);return n&&o.addEventListener("upgradeneeded",(e=>{n(qs(o.result),e.oldVersion,e.newVersion,qs(o.transaction),e)})),r&&o.addEventListener("blocked",(e=>r(e.oldVersion,e.newVersion,e))),a.then((e=>{i&&e.addEventListener("close",(()=>i())),s&&e.addEventListener("versionchange",(e=>s(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),a}(this.databaseName,this.version,this.callbacks)}async delete(){return await function(e,{blocked:t}={}){const r=indexedDB.deleteDatabase(e);return t&&r.addEventListener("blocked",(e=>t(e.oldVersion,e))),qs(r).then((()=>{}))}(this.databaseName)}async prepareTransaction(e,t="readonly",r){const n=await this.connect(),s=n.transaction(e,t,r);return s.addEventListener("abort",(async()=>{if("QuotaExceededError"===s.error?.name){const e=this.transactionCallbacks?.quotaExceeded;e&&await e()}})),Object.freeze({tx:s,completeTx:async()=>{try{await s.done}finally{n.close()}},abortTx:()=>new Promise(((e,t)=>{try{s.abort(),e()}catch(e){t(e)}finally{n.close()}}))})}}const Ys=new Map([[1,e=>{e.createObjectStore("record",{keyPath:"id"})}],[2,e=>{const t=e.createObjectStore("history",{keyPath:"tweetId"});t.createIndex("byUserName",["displayName","screenName"]),t.createIndex("byTweetTime","tweetTime"),t.createIndex("byDownloadTime","downloadTime")}],[3,e=>{e.createObjectStore("hashtag",{keyPath:"name"})}]]),Xs=new class extends Zs{databaseName="download"}(3).onUpgrade(((e,t,r,n,s)=>{for(const i of function*(e,t){if(null!==t)for(let r=e+1;r<=t;r++)yield r;else yield e}(t,r)){const o=Ys.get(i);o&&o(e,t,r,n,s)}})),ei=Un.minute(3);class ti extends Vt{get isExpired(){return!this.props.expiration||this.props.expiration.getTime()-ei<Date.now()}}var ri=r(6055);function ni(e){return Promise.all(Object.keys(e).reduce(((t,r)=>{const n=e[r];return"string"==typeof n?t.push([r,n]):t.push(n().then((e=>[r,e]))),t}),[])).then((e=>e.reduce(((e,[t,r])=>(e[t]=r,e)),{})))}function si(e){throw new ri.C1("Response from Amazon Cognito contained no access key ID",{logger:e})}function ii(e){throw new ri.C1("Response from Amazon Cognito contained no credentials",{logger:e})}function oi(e){throw new ri.C1("Response from Amazon Cognito contained no secret key",{logger:e})}const ai="IdentityIds";class ci{dbName;constructor(e="aws:cognito-identity-ids"){this.dbName=e}getItem(e){return this.withObjectStore("readonly",(t=>{const r=t.get(e);return new Promise((e=>{r.onerror=()=>e(null),r.onsuccess=()=>e(r.result?r.result.value:null)}))})).catch((()=>null))}removeItem(e){return this.withObjectStore("readwrite",(t=>{const r=t.delete(e);return new Promise(((e,t)=>{r.onerror=()=>t(r.error),r.onsuccess=()=>e()}))}))}setItem(e,t){return this.withObjectStore("readwrite",(r=>{const n=r.put({id:e,value:t});return new Promise(((e,t)=>{n.onerror=()=>t(n.error),n.onsuccess=()=>e()}))}))}getDb(){const e=self.indexedDB.open(this.dbName,1);return new Promise(((t,r)=>{e.onsuccess=()=>{t(e.result)},e.onerror=()=>{r(e.error)},e.onblocked=()=>{r(new Error("Unable to access DB"))},e.onupgradeneeded=()=>{const t=e.result;t.onerror=()=>{r(new Error("Failed to create object store"))},t.createObjectStore(ai,{keyPath:"id"})}}))}withObjectStore(e,t){return this.getDb().then((r=>{const n=r.transaction(ai,e);return n.oncomplete=()=>r.close(),new Promise(((e,r)=>{n.onerror=()=>r(n.error),e(t(n.objectStore(ai)))})).catch((e=>{throw r.close(),e}))}))}}const ui=new class{store;constructor(e={}){this.store=e}getItem(e){return e in this.store?this.store[e]:null}removeItem(e){delete this.store[e]}setItem(e,t){this.store[e]=t}};function li(){return"object"==typeof self&&self.indexedDB?new ci:"object"==typeof window&&window.localStorage?window.localStorage:ui}function di(e){throw new ri.C1("Response from Amazon Cognito contained no identity ID",{logger:e})}class fi{storageProxy;constructor(e){this.storageProxy=e}async getItem(e){const t=await this.storageProxy.getItemByKey(e);return t&&Object.hasOwn(t,e)?String(t[e]):null}async removeItem(e){return this.storageProxy.removeItem(e)}async setItem(e,t){return this.storageProxy.setItem({[e]:t})}}class pi extends Vt{get id(){return this.props.id}get isPrivate(){return this.user.isProtected}get user(){return this.props.user}get medias(){return[...this.props.images,...this.props.videos]}get images(){return this.props.images}get videos(){return this.props.videos}get mediaType(){return 0===this.videos.length?"image":this.videos.length>0&&this.videos.length===this.images.length?"video":"mixed"}static create(e){return new pi(e)}}class hi extends Vt{get isProtected(){return this.props.isProtected}static create(e){return new hi(e)}}var mi=r(6075),gi=r.n(mi);const yi=e=>"TimelineTimelineItem"===e.__typename,vi=e=>"TimelineTweet"===e.__typename,bi=e=>{const t=e?.legacy?.entities??e?.legacy?.extended_entities;return!!t&&Object.hasOwn(t,"media")};class wi{static isTimelineAddEntries(e){return"TimelineAddEntries"===e.type}static isTimelinePinEntry(e){return"TimelinePinEntry"===e.type}static isTimelineAddToModule(e){return"TimelineAddToModule"===e.type}}const _i=gi().object({id:gi().string().required(),rest_id:gi().string().required()}).unknown(!0),xi=(gi().object({legacy:gi().object({name:gi().string().required(),screen_name:gi().string().required(),protected:gi().bool().optional()})}).unknown(!0),gi().object({core:gi().object({name:gi().string().required(),screen_name:gi().string().required(),created_at:gi().date().required()}).required(),privacy:gi().object({protected:gi().boolean().required()}).required()}).unknown(!0)),Si=e=>{const{error:t}=_i.concat(xi).validate(e);return void 0===t};class Ai extends Vt{get id(){return this.tweet.id}get tweet(){return this.props.tweet}get content(){return this.props.content}static create(e){return new Ai(e)}}class Ei extends Vt{get isVideo(){return"video"===this.props.type}get isThumbnail(){return"thumbnail"===this.props.type}getVariantUrl(e){if(this.isVideo)return this.props.url;const t=new URL(this.props.url);return t.pathname+=":"+e,t.href}static create(e){return new Ei(e)}}const ki=e=>{let t=0,r=0;return e.reduce(((e,n)=>{if(e.images.push(new Ei({index:t,type:"photo"===n.type?"photo":"thumbnail",url:n.media_url_https})),t+=1,Ii(n)){const t=Ri(n);t&&(e.videos.push(new Ei({index:r,type:"video",url:t})),r+=1)}return e}),{images:[],videos:[]})},Ri=e=>e.video_info.variants.filter(Oi).reduce(((e,t)=>t?.bitrate>=e?.bitrate?t:e)).url,Ii=e=>"animated_gif"===e.type||"video"===e.type,Oi=e=>"video/mp4"===e.content_type,$i=e=>{const t=bi(e)?ki(e.legacy.extended_entities.media):{images:[],videos:[]},r=e.core.user_results.result,n=new hi({...Si(r)?{displayName:r.core.name,screenName:r.core.screen_name,isProtected:r.privacy.protected,userId:r.rest_id}:{displayName:r.legacy.name,screenName:r.legacy.screen_name,isProtected:Boolean(r.legacy.protected),userId:r.rest_id}}),s=new pi({createdAt:new Date(e.legacy.created_at),id:e.legacy.id_str,videos:t.videos,images:t.images,user:n,hashtags:e.legacy.entities.hashtags.map((e=>e.text))});return new Ai({tweet:s,content:e.legacy.full_text})},Ci=e=>{const t=[$i(e)];if("retweeted_status_result"in e.legacy){const r=Pi(e.legacy.retweeted_status_result);zt(r)&&t.push($i(r.value))}return t},Ti=e=>Pi(e.item.itemContent.tweet_results),Pi=e=>{if((e=>{const t=e?.result;return"Tweet"===t?.__typename})(e))return Ut(e.result);if((e=>{const t=e?.result;return!!t&&"TweetWithVisibilityResults"===t?.__typename})(e))return Ut(e.result.tweet);var t;if(null!=(t=e.result)&&Object.hasOwn(t,"__typename")&&(e=>"TweetTombstone"===e.__typename)(e.result))return Ft(new Error("Tombstone!"));const r=`Unknown tweet result type\n${JSON.stringify(e)}`;return Ft(new Error(r))};class ji{query;bearerToken="AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA";rootPath="/i/api/graphql/";constructor(e){this.query=e}get method(){return this.query.method}makeEndpoint(e){const t=new URL(`${e.protocol}://${e.hostname}${this.rootPath}${this.query.id}/${this.query.name}`);return this.query.params.feature&&t.searchParams.append("features",JSON.stringify(this.query.params.feature)),this.query.params.fieldToggles&&t.searchParams.append("fieldToggles",JSON.stringify(this.query.params.fieldToggles)),this.query.params.variable&&t.searchParams.append("variables",JSON.stringify(this.query.params.variable)),t.href}makeAuthHeaders(e,t){return"guest"===this.authType?new Headers([["x-twitter-active-user","yes"],["x-guest-token",e]]):"auth"===this.authType?t?new Headers([["x-twitter-active-user","yes"],["x-csrf-token",e],["x-guest-token",e],["x-client-transaction-id",t]]):new Headers([["x-twitter-active-user","yes"],["x-csrf-token",e],["x-guest-token",e]]):new Headers}makeHeaders(e){return new Headers([["Content-Type","application/json"],["Authorization","Bearer "+this.bearerToken],["User-Agent",navigator.userAgent],...e])}}var Mi=function(e){return e[0]},Di=function(e){return e.length>0},Ni=function(e){return Di(e)?lr(Mi(e)):ur},qi=function(e){return function(t){return t.filter(e)}},Li=ur,Fi=lr;function Ui(e){return function(t){return e(t)?Fi(t):Li}}var Bi=function(e,t){return ar(e,Wi(t))},zi="Option",Wi=function(e){return function(t){return Ji(t)?Li:Fi(e(t.value))}},Hi={URI:zi,map:Bi},Vi=(cr(2,gr(Hi)),yr(Hi),function(e){return function(t){return Ji(t)||Ji(e)?Li:Fi(t.value(e.value))}}),Ki=cr(2,(function(e,t){return Ji(e)?Li:t(e.value)})),Gi={URI:zi,map:Bi,ap:function(e,t){return ar(e,Vi(t))},chain:Ki},Qi=(cr(2,(function(e,t){return Ji(e)?t():e})),{URI:zi,fromEither:function(e){return"Left"===e._tag?Li:Fi(e.right)}}),Ji=function(e){return"None"===e._tag},Zi=function(e,t){return function(r){return Ji(r)?e():t(r.value)}},Yi=(sr(Gi),mr(Qi,Gi),function(e){return null==e?Li:Fi(e)});const Xi=gi().object({id:gi().string().required(),hashtags:gi().array().items(gi().string()).required(),createdAt:gi().date().required().required()}),eo=gi().object({displayName:gi().string().required(),screenName:gi().string().required(),userId:gi().string().required(),isProtected:gi().boolean().default(!1)});class to extends ji{isCacheAble=!0;config;cache=void 0;constructor(e,t){super(t),this.config=e}getResultFromBody(e){return ar(e?.data?.threaded_conversation_with_injections_v2?.instructions,Yi,Tr((()=>"Failed to get instructions")),qr(or(qi((e=>"TimelineAddEntries"===e.type)),Ni,Tr((()=>"Failed to get `TimelineAddEntries` sinstruction")))),qr((e=>Array.isArray(e.entries)?br(e.entries):vr("Failed to get entries"))),qr(or(qi((e=>e.entryId.includes(this.config.tweetId))),Ni,Tr((()=>"Failed to get entry")))),qr((e=>ar(e.content,Ui(yi),Ki((e=>ar(e.itemContent,Ui(vi)))),Ki((e=>ar(Pi(e.tweet_results),Ht,Yi))),Tr((()=>"Failed to get result"))))))}parseBody(e){return e?(e=>ar(Dr,Nr("result",(()=>this.getResultFromBody(e))),Nr("tweetResult",(e=>{return t=e.result,ar(t?.tweet?.legacy??t?.legacy??t,Mr("Failed to get tweet result"));var t})),Nr("userProps",(e=>{return t=e.result,ar(t?.core?.user_results?.result??t?.tweet?.core?.user_results?.result,Mr("Failed to get user result"),qr((e=>ar(eo.validate({isProtected:e?.legacy?.protected,displayName:e?.legacy?.name,screenName:e?.legacy?.screen_name,userId:e?.rest_id}),so))));var t})),Nr("medias",(e=>ar(e.tweetResult?.extended_entities?.media,Mr("Failed to get medias.")))),Nr("mediaCollection",(e=>function(e,t){try{return br(e())}catch(e){return vr(t(e))}}((()=>ki(e.medias)),(e=>function(e){try{return e instanceof Error?e:new Error(String(e))}catch(e){return new Error}}(e).message??"Failed to parse medias")))),Nr("partialTweetProps",(e=>{return t=e.tweetResult,ar(Xi.validate({id:t.rest_id??t.id_str,hashtags:no(t?.entities),createdAt:new Date(t.created_at)}),so);var t})),Ar((e=>({user:new hi(e.userProps),...e.mediaCollection,...e.partialTweetProps}))),Ar((e=>new pi(e))),$r((e=>new oo(e))),jr(Ft,Ut)))(e):io(e)?Ft(new oo(e.error??e.errors??"Invalid body")):Ft(new oo("Invalid body"))}parseMetadata(e){const t=ar(e.headers.get("X-Rate-Limit-Remaining"),Yi,Zi((()=>{}),(e=>Number.parseInt(e,10)))),r=ar(e.headers.get("X-Rate-Limit-Reset"),Yi,Zi((()=>{}),(e=>new Date(1e3*Number.parseInt(e,10)))));return{httpStatusCode:e.status,remainingQuota:t,quotaResetTime:r}}async getCache(){return this.cache??="function"==typeof this.config.cacheProvider?await this.config.cacheProvider():this.config.cacheProvider}async readFromCache(e){return(await this.getCache()).get(e)}async putIntoCache(e,t){const r=await this.getCache();if(t.ok)return r.put(e,t)}async prepareRequest(e){const t=this.config.transactionIdProvider?await this.config.transactionIdProvider(`${this.rootPath}${this.query.id}/${this.query.name}`,this.query.method):void 0;return new Request(this.makeEndpoint(e),{method:this.method,headers:this.makeHeaders(this.makeAuthHeaders(this.config.csrfToken,t)),mode:"cors",referrer:`https://x.com/i/web/status/${this.config.tweetId}`})}async resolveResponse(e){const t=this.parseMetadata(e);if(!e.ok)return{$metadata:t,tweetResult:Ft(new ao("Failed to fetch tweet",e.status))};try{const r=await e.json();return{$metadata:t,tweetResult:this.parseBody(r)}}catch(e){return{$metadata:t,tweetResult:Ft(new oo("Failed to parse body",{cause:e}))}}}}const ro=e=>"object"==typeof e&&null!==e&&"text"in e&&"string"==typeof e.text,no=e=>(e=>{const t=e.hashtags;return Array.isArray(t)&&t.length>0&&t.every(ro)})(e)?e.hashtags.map((e=>e.text)):[],so=e=>e.error?vr(e.error.message):br(e.value),io=e=>"object"==typeof e&&null!==e&&("error"in e||"errors"in e);class oo extends Error{name="ParseTweetError"}class ao extends Error{name="FetchTweetError";statusCode;constructor(e,t,r){super(e,{cause:r}),this.statusCode=t}}const co={creator_subscriptions_tweet_preview_api_enabled:!1,tweetypie_unmention_optimization_enabled:!0,responsive_web_edit_tweet_api_enabled:!0,graphql_is_translatable_rweb_tweet_is_translatable_enabled:!1,view_counts_everywhere_api_enabled:!1,longform_notetweets_consumption_enabled:!0,responsive_web_twitter_article_tweet_consumption_enabled:!1,tweet_awards_web_tipping_enabled:!1,freedom_of_speech_not_reach_fetch_enabled:!0,standardized_nudges_misinfo:!1,tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled:!0,longform_notetweets_rich_text_read_enabled:!1,longform_notetweets_inline_media_enabled:!1,responsive_web_graphql_exclude_directive_enabled:!0,verified_phone_label_enabled:!1,responsive_web_media_download_video_enabled:!1,responsive_web_graphql_skip_user_profile_image_extensions_enabled:!1,responsive_web_graphql_timeline_navigation_enabled:!1,responsive_web_enhance_cards_enabled:!1},uo={withArticleRichContentState:!1,withAuxiliaryUserLabels:!1};class lo extends to{authType="auth";constructor(e){var t;super(e,{id:"0hWvDhmW8YQ-S_ib3azIrw",name:"TweetResultByRestId",method:"GET",params:{variable:(t=e.tweetId,{tweetId:t,withCommunity:!1,includePromotedContent:!1,withVoice:!1}),feature:co,fieldToggles:uo}})}getResultFromBody(e){return(0,In.pipe)(e?.data?.tweetResult?.result,(0,Rn.fromNullable)("Failed to get result"))}}const fo={blue_business_profile_image_shape_enabled:!1,responsive_web_graphql_exclude_directive_enabled:!0,verified_phone_label_enabled:!1,responsive_web_graphql_timeline_navigation_enabled:!1,responsive_web_graphql_skip_user_profile_image_extensions_enabled:!0,tweetypie_unmention_optimization_enabled:!1,vibe_api_enabled:!1,responsive_web_edit_tweet_api_enabled:!1,graphql_is_translatable_rweb_tweet_is_translatable_enabled:!1,view_counts_everywhere_api_enabled:!1,longform_notetweets_consumption_enabled:!1,tweet_awards_web_tipping_enabled:!1,freedom_of_speech_not_reach_fetch_enabled:!1,standardized_nudges_misinfo:!1,tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled:!0,interactive_text_enabled:!1,responsive_web_text_conversations_enabled:!1,longform_notetweets_rich_text_read_enabled:!1,responsive_web_enhance_cards_enabled:!1};class po extends to{authType="auth";constructor(e){super(e,{id:"BbCrSoXIR7z93lLCVFlQ2Q",name:"TweetDetail",method:"GET",params:{variable:{focalTweetId:e.tweetId,with_rux_injections:!1,includePromotedContent:!1,withCommunity:!1,withQuickPromoteEligibilityTweetFields:!1,withBirdwatchNotes:!1,withVoice:!1,withV2Timeline:!0},feature:fo}})}}const ho={rweb_video_screen_enabled:!1,profile_label_improvements_pcf_label_in_post_enabled:!0,rweb_tipjar_consumption_enabled:!0,verified_phone_label_enabled:!1,creator_subscriptions_tweet_preview_api_enabled:!0,responsive_web_graphql_timeline_navigation_enabled:!0,responsive_web_graphql_skip_user_profile_image_extensions_enabled:!1,premium_content_api_read_enabled:!1,communities_web_enable_tweet_community_results_fetch:!0,c9s_tweet_anatomy_moderator_badge_enabled:!0,responsive_web_grok_analyze_button_fetch_trends_enabled:!1,responsive_web_grok_analyze_post_followups_enabled:!0,responsive_web_jetfuel_frame:!1,responsive_web_grok_share_attachment_enabled:!0,articles_preview_enabled:!0,responsive_web_edit_tweet_api_enabled:!0,graphql_is_translatable_rweb_tweet_is_translatable_enabled:!0,view_counts_everywhere_api_enabled:!0,longform_notetweets_consumption_enabled:!0,responsive_web_twitter_article_tweet_consumption_enabled:!0,tweet_awards_web_tipping_enabled:!1,responsive_web_grok_show_grok_translated_post:!1,responsive_web_grok_analysis_button_from_backend:!1,creator_subscriptions_quote_tweet_preview_enabled:!1,freedom_of_speech_not_reach_fetch_enabled:!0,standardized_nudges_misinfo:!0,tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled:!0,longform_notetweets_rich_text_read_enabled:!0,longform_notetweets_inline_media_enabled:!0,responsive_web_grok_image_annotation_enabled:!0,responsive_web_enhance_cards_enabled:!1},mo={withArticleRichContentState:!0,withArticlePlainText:!1,withGrokAnalyze:!1,withDisallowedReplyControls:!1};class go extends to{authType="auth";constructor(e){super(e,{id:"_8aYOgEDz35BrBcBal1-_w",name:"TweetDetail",method:"GET",params:{variable:{focalTweetId:e.tweetId,rankingMode:"Relevance",includePromotedContent:!1,withCommunity:!1,withQuickPromoteEligibilityTweetFields:!1,withBirdwatchNotes:!1,withVoice:!1},feature:ho,fieldToggles:mo}})}}const yo={creator_subscriptions_tweet_preview_api_enabled:!0,premium_content_api_read_enabled:!1,communities_web_enable_tweet_community_results_fetch:!0,c9s_tweet_anatomy_moderator_badge_enabled:!0,responsive_web_grok_analyze_button_fetch_trends_enabled:!1,responsive_web_grok_analyze_post_followups_enabled:!1,responsive_web_jetfuel_frame:!1,responsive_web_grok_share_attachment_enabled:!0,articles_preview_enabled:!0,responsive_web_edit_tweet_api_enabled:!0,graphql_is_translatable_rweb_tweet_is_translatable_enabled:!0,view_counts_everywhere_api_enabled:!0,longform_notetweets_consumption_enabled:!0,responsive_web_twitter_article_tweet_consumption_enabled:!0,tweet_awards_web_tipping_enabled:!1,responsive_web_grok_show_grok_translated_post:!1,responsive_web_grok_analysis_button_from_backend:!0,creator_subscriptions_quote_tweet_preview_enabled:!1,freedom_of_speech_not_reach_fetch_enabled:!0,standardized_nudges_misinfo:!0,tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled:!0,longform_notetweets_rich_text_read_enabled:!0,longform_notetweets_inline_media_enabled:!0,profile_label_improvements_pcf_label_in_post_enabled:!0,rweb_tipjar_consumption_enabled:!0,responsive_web_graphql_exclude_directive_enabled:!0,verified_phone_label_enabled:!1,responsive_web_grok_image_annotation_enabled:!0,responsive_web_graphql_skip_user_profile_image_extensions_enabled:!1,responsive_web_graphql_timeline_navigation_enabled:!0,responsive_web_enhance_cards_enabled:!1},vo={withArticleRichContentState:!0,withArticlePlainText:!1,withGrokAnalyze:!1,withDisallowedReplyControls:!1};class bo extends to{authType="guest";rootPath="/graphql/";constructor(e){var t;super(e,{id:"Vg2Akr5FzUmF0sTplA5k6g",name:"TweetResultByRestId",method:"GET",params:{variable:(t=e.tweetId,{tweetId:t,withCommunity:!1,includePromotedContent:!1,withVoice:!1}),feature:yo,fieldToggles:vo}})}async prepareRequest(e){return super.prepareRequest({...e,hostname:"api.x.com"})}getResultFromBody(e){return(0,In.pipe)(e?.data?.tweetResult?.result,(0,Rn.fromNullable)("Failed to get result"))}}class wo{config;constructor(e){this.config=e}static with(e){return new wo(e)}makeCacheResult(e){return Ut({...e,$metadata:{...e.$metadata,remainingQuota:"omit",quotaResetTime:"omit"}})}async exec(e){try{const t=await e.prepareRequest({protocol:"https",hostname:"x.com"});this.config?.cookieStore&&t.headers.set("cookie",await this.config.cookieStore.get("x.com"));const r=_o(e)?await e.readFromCache(t):void 0,n=_o(e)&&void 0!==r,s=r??await fetch(t,{signal:AbortSignal.timeout(this.config?.timeout?Math.abs(this.config.timeout):1e4)}),i=n?s:s.clone(),o=await e.resolveResponse(s);if(_o(e)&&void 0===r&&200===o.$metadata.httpStatusCode&&await e.putIntoCache(t.clone(),i),!n&&this.config?.cookieStore){const e=s.headers.get("set-cookie");e&&await this.config.cookieStore.set("x.com",e)}return n?this.makeCacheResult(o):Ut(o)}catch(e){return Ft(e)}}}const _o=e=>e.isCacheAble,xo=()=>cs().runtime.getManifest().version;class So{storage;constructor(e){this.storage=e}async getItemByKey(e){const t=String(e),r=await this.storage.get(t);return Object.keys(r).includes(t)?r:void 0}async getItemByDefaults(e){const t=await this.storage.get(e);return{...e,...t}}async setItem(e){await this.storage.set(e)}async removeItem(e){await this.storage.remove(Array.isArray(e)?[...e].map((e=>String(e))):String(e))}}const Ao=new class extends So{constructor(e){super(e||cs().storage.sync)}},Eo=new class extends So{constructor(e){super(e||cs().storage.local)}},ko=new class{storageProxy;cognitoPoolConfig;constructor(e,t){this.storageProxy=e,this.cognitoPoolConfig=t}async fetch(){const e=await(t={identityPoolId:this.cognitoPoolConfig.identityPoolId,cache:new fi(this.storageProxy),clientConfig:{region:this.cognitoPoolConfig.region}},function({accountId:e,cache:t=li(),client:n,clientConfig:s,customRoleArn:i,identityPoolId:o,logins:a,userIdentifier:c=(a&&0!==Object.keys(a).length?void 0:"ANONYMOUS"),logger:u,parentClientConfig:l}){u?.debug("@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity");const d=c?`aws:cognito-identity-credentials:${o}:${c}`:void 0;let f=async c=>{const{GetIdCommand:p,CognitoIdentityClient:h}=await Promise.resolve().then(r.bind(r,2183)),m=e=>s?.[e]??l?.[e]??c?.callerClientConfig?.[e],g=n??new h(Object.assign({},s??{},{region:m("region"),profile:m("profile")}));let y=d&&await t.getItem(d);if(!y){const{IdentityId:r=di(u)}=await g.send(new p({AccountId:e,IdentityPoolId:o,Logins:a?await ni(a):void 0}));y=r,d&&Promise.resolve(t.setItem(d,y)).catch((()=>{}))}var v;return v={client:g,customRoleArn:i,logins:a,identityId:y},f=async e=>{v.logger?.debug("@aws-sdk/credential-provider-cognito-identity - fromCognitoIdentity");const{GetCredentialsForIdentityCommand:t,CognitoIdentityClient:n}=await Promise.resolve().then(r.bind(r,2183)),s=t=>v.clientConfig?.[t]??v.parentClientConfig?.[t]??e?.callerClientConfig?.[t],{Credentials:{AccessKeyId:i=si(v.logger),Expiration:o,SecretKey:a=oi(v.logger),SessionToken:c}=ii(v.logger)}=await(v.client??new n(Object.assign({},v.clientConfig??{},{region:s("region"),profile:s("profile")}))).send(new t({CustomRoleArn:v.customRoleArn,IdentityId:v.identityId,Logins:v.logins?await ni(v.logins):void 0}));return{identityId:v.identityId,accessKeyId:i,secretAccessKey:a,sessionToken:c,expiration:o}},f(c)};return e=>f(e).catch((async e=>{throw d&&Promise.resolve(t.removeItem(d)).catch((()=>{})),e}))}({...t}))();var t,n;return await this.storageProxy.setItem((n=e,{awsCredential:{...n,expiration:n?.expiration?.getTime()??0}})),(e=>new ti(e))(e)}async get(){const e=await this.storageProxy.getItemByKey("awsCredential");if(!e)return this.fetch();const t=new ti({...(r=e).awsCredential,expiration:new Date(r.awsCredential.expiration)});var r;return t.isExpired?this.fetch():t}}(Ao,{identityPoolId:"ap-northeast-1:27878b7d-fd1d-4c9c-92ac-1fc6086f744d",region:"ap-northeast-1"}),Ro=new Jn({apiKey:"uWLUg8C2qK97q22Y7fjoS4tHmi9s3O1d2qlMWUWo",clientVersion:xo(),hostName:"api.webext.prod.mediaharvest.app",region:"ap-northeast-1",credentials:async()=>(await ko.get()).mapBy(ds("accessKeyId","expiration","sessionToken","identityId","secretAccessKey"))}),Io=new wo({timeout:1e4}),Oo=new class{async getById(e){const[t]=await as.downloads.search({id:e});return t}async search(e){return as.downloads.search(e)}},$o=new class{async getByName(e){const t=await cs().cookies.get({url:"https://x.com",name:e});return t?new Is({name:t.name,value:t.value}):void 0}async getCsrfToken(){return this.getByName("ct0")}async getGuestToken(){return this.getByName("gt")}},Co=new class{storage;constructor(e){this.storage=e}async get(){const e=await this.storage.getItemByDefaults(_s.mapBy((e=>e)));return new ys(e)}async save(e){await this.storage.setItem(e.mapBy((e=>e)))}async reset(){await this.save(_s)}getDefault(){return _s}}(Ao),To=new class{apiClient;storageProxy;constructor(e,t){this.apiClient=e,this.storageProxy=t}async createClient(e){const t=new rs({initStats:{downloadCount:e.downloadCount,trafficUsage:e.trafficUsage}}),{value:r,error:n}=await this.apiClient.send(t);if(n)return Ft(n);const s=new Bn(r.clientUUID),i=new Wn(s,{syncToken:r.syncToken,syncedAt:0,uninstallCode:r.uninstallCode,usageStatistics:new Hn({...e})});return Ut(i)}async get(){const e=await this.storageProxy.getItemByDefaults({...is,...os});if([(t=e).csrfToken,t.uuid,t.uninstallCode].some((e=>e===ss))){const t=await this.createClient({downloadCount:e.downloadCount,trafficUsage:e.trafficUsage});return t.value&&await this.save(t.value),t}var t;const r=new Bn(e.uuid),n=new Wn(r,{syncToken:e.csrfToken,syncedAt:e.syncedAt,uninstallCode:e.uninstallCode,usageStatistics:new Hn({downloadCount:e.downloadCount,trafficUsage:e.trafficUsage})});return Ut(n)}async sync(e){const t=new ns({clientId:e.id.value,stats:e.usageStatistics.mapBy((e=>e)),syncToken:e.syncToken}),{value:r,error:n}=await this.apiClient.send(t);return n||(e.updateSyncToken(r.syncToken),this.save(e))}async save(e){return this.storageProxy.setItem((e=>e.mapBy(((e,t)=>({csrfToken:t.syncToken,syncedAt:t.syncedAt,uninstallCode:t.uninstallCode,uuid:e.value,...t.usageStatistics.mapBy((e=>e))}))))(e))}}(Ro,Eo),Po=new class{storageArea;constructor(e){this.storageArea=e}async get(){return await this.storageArea.getItemByDefaults(us)}async save(e){await this.storageArea.setItem(e)}async reset(){await this.save(us)}getDefault(){return us}}(Eo),jo=new class{storageArea;constructor(e){this.storageArea=e}async get(){return await this.storageArea.getItemByDefaults(ls)}async save(e){await this.storageArea.setItem(e)}async reset(){await this.save(ls)}getDefault(){return ls}}(Eo),Mo=new class{storage;constructor(e){this.storage=e}async get(){const e=await this.storage.getItemByDefaults({downloadCount:0,trafficUsage:0});return new Hn(e)}async save(e){await this.storage.setItem((e=>e.mapBy((({downloadCount:e,trafficUsage:t})=>({downloadCount:e,trafficUsage:t}))))(e))}}(Eo),Do=new class{idb;constructor(e){this.idb=e}async total(){try{const e=await this.idb.connect(),t=await e.count("history"),r=await e.count("hashtag");return Ut({historyTotal:t,hashtagTotal:r})}catch(e){return Ft((0,Rn.toError)(e))}}async clear(){const e=(0,In.pipe)(pn((()=>On(this.idb)),$n(Lt)),En((e=>pn((()=>Promise.all([e.hashtagCollection.clear(),e.historyCollection.clear()])),$n(e.abortTransaction)))),fn((e=>({error:e.error,complete:e.abort})),(e=>({error:void 0,complete:e.completeTransaction}))));return(0,In.pipe)(e,ln,En((e=>pn((()=>e.complete()),Rn.toError))),fn((e=>e),(e=>e.error)))()}async save(e){const t=(0,In.pipe)(pn((()=>On(this.idb)),$n(Lt)),kn("tweetId",(()=>an(e.id.value))),kn("hashtagDelta",(t=>pn((async()=>{const r=await t.historyCollection.get(e.id.value);return(({current:e,previous:t})=>{const r=t?t.mapBy(((e,t)=>new Set(t.hashtags))):new Set;return{current:e?e.mapBy(((e,t)=>new Set(t.hashtags))):new Set,previous:r}})({previous:r?tr(r):void 0,current:e})}),$n(t.abortTransaction))))),r=(0,In.pipe)(t,En((e=>pn((()=>(({hashtagDelta:e,tweetId:t,hashtagCollection:r})=>Promise.allSettled(Array.from(e.previous).filter((t=>!e.current.has(t))).map((e=>Cn({hashtag:e,tweetId:t})(r)))))(e)),$n(e.abortTransaction)))),En((e=>pn((()=>(({hashtagDelta:e,tweetId:t,hashtagCollection:r})=>Promise.allSettled(Array.from(e.current).filter((t=>!e.previous.has(t))).map((e=>Tn({tweetId:t,hashtag:e})(r)))))(e)),$n(e.abortTransaction)))),En((t=>{return(0,In.pipe)((r=e,e=>pn((()=>e.put(er(r))),Rn.toError))(t.historyCollection),gn($n(t.abortTransaction)));var r})),fn((e=>({error:e.error,complete:e.abort})),(e=>({error:void 0,complete:e.completeTransaction}))));return(0,In.pipe)(r,ln,En((e=>pn((()=>e.complete()),Rn.toError))),fn((e=>e),(e=>e.error)))()}async getByTweetId(e){try{const t=await this.idb.connect(),r=await t.get("history",e);return Ut(r?tr(r):r)}catch(e){return Ft((0,Rn.toError)(e))}}async removeByTweetId(e){const t=pn((()=>On(this.idb)),$n(Lt)),r=(0,In.pipe)(t,En((t=>pn((async()=>{const r=await t.historyCollection.get(e);r&&(await t.historyCollection.delete(e),await Promise.allSettled(Array.from(r.hashtags??((...e)=>new Set(e))()).map((r=>Cn({tweetId:e,hashtag:r})(t.hashtagCollection)))))}),$n(t.abortTransaction)))),fn((e=>({complete:e.abort})),(e=>({complete:e.completeTransaction}))));return(await r()).complete()}async hasTweetId(e){try{const t=await this.idb.prepareTransaction("history","readonly"),r=await t.tx.objectStore("history").getKey(IDBKeyRange.only(e));return Ut(void 0!==r)}catch(e){return Ft((0,Rn.toError)(e))}}}(Xs),No=new class{idb;constructor(e){this.idb=e}async getById(e){const{value:t,error:r}=await this.connect();if(r)return Ft(r);try{const r=await t.get("record",IDBKeyRange.only(e));return r?Ut((e=>new Dn({downloadConfig:new Mn({conflictAction:e.conflictAction,filename:e.filename,saveAs:e.saveAs,url:e.url}),downloadId:e.id,tweetInfo:new Nn(e.tweetInfo),recordedAt:new Date(e.recordedAt)}))(r)):Ft(new jn(e))}catch(e){return Ft((0,Rn.toError)(e))}}async save(e){const{value:t,error:r}=await this.connect();if(r)return r;try{await t.put("record",(n=e,n.mapBy((e=>({id:e.downloadId,recordedAt:e.recordedAt.getTime(),tweetInfo:e.tweetInfo.mapBy((e=>e)),...e.downloadConfig.mapBy((e=>e))})))))}catch(e){return(0,Rn.toError)(e)}var n}async removeById(e){const{value:t,error:r}=await this.connect();if(r)return r;try{await t.delete("record",IDBKeyRange.only(e))}catch(e){return(0,Rn.toError)(e)}}async connect(){try{const e=await this.idb.connect();return Ut(e)}catch(e){return Ft((0,Rn.toError)(e))}}}(Xs),qo=(new class{downloadIDB;constructor(e){this.downloadIDB=e}async export(e){try{const{tx:t}=await this.downloadIDB.prepareTransaction("history","readonly"),r=await t.objectStore("history").getAll(),n=new qn({items:r.map(rr)}),s=new Blob([JSON.stringify(n)],{type:"application/json"});return Ut(e?await e(s):s)}catch(e){return Ft((0,Rn.toError)(e))}}async import(e){let t;try{t=await this.downloadIDB.prepareTransaction(["hashtag","history"],"readwrite");const r=t.tx.objectStore("history"),n=t.tx.objectStore("hashtag"),s={};for(const t of e){await r.put(er(t));for(const e of t.mapBy(((e,t)=>t.hashtags)))e in s?s[e].add(t.id.value):s[e]=new Set([t.id.value])}for(const[e,t]of Object.entries(s)){const r=await n.get(IDBKeyRange.only(e));await n.put({name:e,tweetIds:r?new Set([...t,...Array.from(r.tweetIds)]):new Set(t)})}await t.completeTx()}catch(e){return await(t?.abortTx()),(0,Rn.toError)(e)}}}(Xs),new class{storage;constructor(e){this.storage=e}async get(){return await this.storage.getItemByDefaults(Rs)}async save(e){await this.storage.setItem(e)}async reset(){await this.storage.setItem(Rs)}getDefault(){return Rs}}(Eo)),Lo=new class{storageProxy;constructor(e){this.storageProxy=e}async get(e){const t=await this.storageProxy.getItemByKey("solutionQuotaCollection");if(!t)return;const{solutionQuotaCollection:r}=t;if(!(e in r))return;const n=r[e];return n?As.create(e,{isRealtime:!1,quota:new Es({quota:n.quota,resetAt:new Date(n.resetAt)}),warnedAt:n.warnedAt?new Date(n.warnedAt):void 0}):void 0}async save(e){const t=await this.storageProxy.getItemByKey("solutionQuotaCollection");t?await this.storageProxy.setItem({solutionQuotaCollection:{...t.solutionQuotaCollection,...ks(e)}}):await this.storageProxy.setItem({solutionQuotaCollection:ks(e)})}}(Eo),Fo=e=>e.mapBy((e=>e.filename)),Uo=e=>e.mapBy((e=>({filename:e.filename,conflictAction:e.conflictAction,url:e.url,saveAs:e.saveAs})));class Bo extends Ln{solutionId;remainingQuota;resetTime;constructor(e,t,r){super("tweetSolution:quota:changed"),this.solutionId=e,this.remainingQuota=t,this.resetTime=r}}class zo extends Ln{solutionId;remainingQuota;resetTime;constructor(e,t,r){super("tweetSolution:quota:insufficient"),this.solutionId=e,this.remainingQuota=t,this.resetTime=r}}class Wo extends Error{name="FetchTweetSolutionError"}class Ho extends Wo{name="NoValidSolutionToken"}class Vo extends Wo{name="TweetIsNotFound"}class Ko extends Wo{name="InsufficientQuota";isInternalControl;constructor(e,t){const{isInternalControl:r,...n}=t??{isInternalControl:!1};super(e,n),this.isInternalControl=r??!1}}class Go extends Wo{name="TweetProcessingError"}class Qo{cache;constructor(e){this.cache=e}get(e){return this.cache.match(e)}put(e,t){return this.cache.put(e,t)}async clear(){const e=await this.cache.keys();return Promise.allSettled(e.map((e=>this.cache.delete(e))))}}const Jo=e=>void 0!==e.value?.tweetResult?.value,Zo=/^\d+$/,Yo=(e=Lt)=>t=>({error:(0,Rn.toError)(t),abort:e}),Xo=e=>({error:e,matchedCount:-1,items:[]}),ea=new Map([["asc","next"],["desc","prev"]]),ta=new Map([["downloadTime","byDownloadTime"],["tweetTime","byTweetTime"]]);var ra=r(5363);const na=(e=Lt)=>t=>({error:(0,Rn.toError)(t),abort:e}),sa=()=>new Set;new class{downloadIDB;constructor(e){this.downloadIDB=e}async process(e){const t=(0,In.pipe)(pn((()=>this.downloadIDB.prepareTransaction("history","readonly")),Yo(Lt)),kn("historyCollection",(e=>pn((async()=>e.tx.objectStore("history")),Yo(e.abortTx)))),kn("cursor",(t=>pn((()=>t.historyCollection.index(ta.get(e.orderBy.key)??"byDownloadTime").openCursor(null,ea.get(e.orderBy.type)??"prev")),Yo(t.abortTx)))),kn("result",(t=>pn((async()=>{let r=0,n=e.skip;const s=[];let i=t.cursor;for(;i;){const t=n>0,a=e.filters.every((e=>i&&e(i.value)));a&&(r+=1),a&&t&&(n-=1),a&&!t&&s.length<e.limit&&0===n&&s.push((o=i.value,new Zt(new Jt(o.tweetId),{downloadTime:o.downloadTime,hashtags:Array.from(o?.hashtags??[]),mediaType:o.mediaType,tweetTime:o.tweetTime,tweetUser:new Yt({displayName:o.displayName,userId:o.userId,screenName:o.screenName}),thumbnail:o.thumbnail}))),i=await i.continue()}var o;return{matchedCount:r,items:s,error:void 0}}),Yo(t.abortTx)))),fn((e=>({done:e.abort,result:Xo(e.error)})),(e=>({done:e.completeTx,result:e.result}))));return(0,In.pipe)(t,ln,En((e=>pn((()=>e.done()),Rn.toError))),fn(Xo,(({result:e})=>e)))()}}(Xs),new class{downloadIdb;constructor(e){this.downloadIdb=e}async process(e){if(0===e.hashtags.length)return Ut(new Set);const t=(0,In.pipe)(pn((()=>this.downloadIdb.prepareTransaction("hashtag","readonly")),na()),kn("hashtags",(t=>pn((async()=>{return t=e.hashtags,new Set(t);var t}),na(t.abortTx)))),kn("hashtagCollection",(e=>pn((async()=>e.tx.objectStore("hashtag")),na(e.abortTx)))),kn("ids",(e=>pn((async()=>{let t=sa();const r=(()=>{let e=!1;return t=>r=>e?r.intersection(t):(e=!0,r.union(t))})();for(const n of e.hashtags){const s=await e.hashtagCollection.get(IDBKeyRange.only(n));if(!s)return sa();if((0,ra.isEmpty)(s.tweetIds))return s.tweetIds;if(t=r(s.tweetIds)(t),(0,ra.isEmpty)(t))return t}return t}),na(e.abortTx)))),fn((e=>({done:e.abort,result:Ft(e.error)})),(e=>({done:e.completeTx,result:Ut(e.ids)}))));return(0,In.pipe)(t,ln,En((e=>pn((()=>e.done()),Rn.toError))),fn(Ft,(({result:e})=>e)))()}}(Xs),new class{async process(e){if(!await cs().downloads.download(Uo(e.target)))return(0,Rn.toError)(cs().runtime.lastError)}},new e(as.runtime.id);const ia=new class{isTransactionIdConsumer=!0;infra;options;cache;_events;_statistics;constructor(e,t){this.infra=e,this.options=t,this.cache=void 0,this._events=[],this._statistics={}}get statistics(){return this._statistics}get events(){return this._events}setStatistics(e){return t=>this._statistics[e]=t}async getCacheStorage(){if(this.cache)return this.cache;const e=await caches.open("fetch-tweet");return this.cache=new Qo(e),this.cache}async clearCacheStorage(){const e=await this.getCacheStorage(),t=(await e.clear()).filter((e=>"rejected"===e.status));return t.forEach((e=>console.error(e.reason))),0===t.length}execCommand(e){return async t=>{const r=new e({tweetId:t.tweetId,csrfToken:t.csrfToken,cacheProvider:this.getCacheStorage,transactionIdProvider:async(e,r)=>{if(t.transactionIdProvider){const n=await t.transactionIdProvider(e,r);if(Bt(n))return;return n.value}}}),{value:n,error:s}=await this.infra.xApiClient.exec(r);return this.setStatistics(t.statIdentity)({error:s??n.tweetResult.error,...n?(i=n,{remaining:i.$metadata.remainingQuota,resetTime:i.$metadata.quotaResetTime}):{}}),{value:n,error:s};var i}}async process(e){const t=await this.infra.xTokenRepo.getCsrfToken(),r=await this.infra.xTokenRepo.getGuestToken(),n=r?.value??t?.value;if(n){const t=await this.execCommand(n.match(Zo)?bo:lo)({statIdentity:"guest",tweetId:e.tweetId,csrfToken:n});if(Jo(t))return t.value.tweetResult}const s=await this.infra.solutionQuotaRepo.get("native");if(void 0!==s&&!this.hasEnoughQuota(s))return this._events.push(new zo("native",this.calculateUsableQuota(s.quota.remaining),s.quota.resetTime)),Ft(new Ko(`Remaining quota is less than reserved quota (${this.options.reservedQuota}). `,{isInternalControl:!0}));if(t){const r=await this.execCommand(go)({statIdentity:"general",tweetId:e.tweetId,csrfToken:t.value,transactionIdProvider:e.transactionIdProvider});if(void 0!==(i=r.value)&&"number"==typeof i.$metadata.remainingQuota&&i.$metadata.quotaResetTime instanceof Date&&this._events.push(new Bo("native",r.value.$metadata.remainingQuota,r.value.$metadata.quotaResetTime)),Jo(r))return this.isCommandOutputLowQuota(r.value)&&this._events.push(new zo("native",this.calculateUsableQuota(r.value.$metadata.remainingQuota),r.value.$metadata.quotaResetTime)),r.value.tweetResult;const n=await this.execCommand(po)({statIdentity:"fallback",tweetId:e.tweetId,csrfToken:t.value});if(Jo(n))return n.value.tweetResult;const s=r.error??n.error;return Ft(this.parseCommandError(s)??new Vo(`Specified tweet is not found (${e.tweetId})`,{cause:s}))}var i;return Ft(new Ho("No valid solution token"))}parseCommandError(e){if(e){if(e instanceof ao)switch(e.statusCode){case 429:return new Ko("Rate limit exceeded");case 401:return new Ho("Unauthorized");case 403:return new Ho("Forbidden");case 404:return new Vo("Specified tweet is not found");default:return new Vo("Failed to fetch tweet",{cause:e})}return e instanceof oo?new Go("Failed to parse tweet",{cause:e}):void 0}}isCommandOutputLowQuota(e){return"number"==typeof e.$metadata.remainingQuota&&e.$metadata.quotaResetTime instanceof Date&&this.calculateUsableQuota(e.$metadata.remainingQuota)<=this.options.quotaThreshold+this.options.reservedQuota}hasEnoughQuota(e){return!!e.quota.isReset||e.quota.remaining>this.options.reservedQuota}calculateUsableQuota(e){return Math.max(e-this.options.reservedQuota,0)}}({xApiClient:Io,xTokenRepo:$o,solutionQuotaRepo:Lo},{quotaThreshold:10,reservedQuota:10}),oa=gi().object({index:gi().number().required(),type:gi().valid("photo","thumbnail","video").required(),url:gi().string().required()}),aa=gi().object({isProtected:gi().boolean().optional().default(!1),displayName:gi().string().required(),screenName:gi().string().required(),userId:gi().string().required()}),ca=gi().object({createdAt:gi().date().required(),hashtags:gi().array().items(gi().string()).required(),id:gi().string().required(),images:gi().array().items(oa).required(),videos:gi().array().items(oa).required(),user:aa.required()}),ua=gi().object({tweet:ca.required(),content:gi().string().required()}),la=e=>`http://mediaharvest.local/tweets/${e}`,da=new class{cache;constructor(){}async getCache(){return this.cache??=await caches.open("tweet-response")}async get(e){try{const t=await this.getCache(),r=new Request(la(e)),n=await t.match(r);if(!n)return Ut(void 0);const s=await n.json(),{value:i,error:o}=ua.validate(s);if(o)return Ft(o);const a=hi.create({...i.tweet.user}),c=pi.create({...i.tweet,videos:i.tweet.videos.map(Ei.create),images:i.tweet.images.map(Ei.create),user:a});return Ut(Ai.create({tweet:c,content:i.content}))}catch(e){return Ft(e)}}async save(e){try{const t=await this.getCache(),r=new Request(la(e.id)),n=new Response(JSON.stringify(e),{headers:{"Content-Type":"application/json","Cache-Control":`max-age=${Un.hour(24)}`}});return void await t.put(r,n)}catch(e){return e}}async saveAll(...e){if(0===e.length)return;const t=await Promise.all(e.map((e=>this.save(e))));return t.some((e=>e))?new Error("Failed to cache some tweet response",{cause:t}):void 0}};class fa extends Ln{downloadId;constructor(e,t){super(e),this.downloadId=t}}class pa extends fa{constructor(e){super("download:status:completed",e)}}class ha extends fa{reason;constructor(e,t){super("download:status:interrupted",e),this.reason=t}}class ma extends Ln{downloadId;constructor(e,t){super(e),this.downloadId=t}}class ga extends ma{constructor(e){super("notification:downloadFailed:retryButton:clicked",e)}}class ya extends ma{constructor(e){super("notification:downloadFailed:viewButton:clicked",e)}}class va extends Ln{constructor(){super("notification:filenameOverwritten:ignoreButton:clicked")}}class ba extends Ln{tweetId;constructor(e,t){super(e),this.tweetId=t}}class wa extends ba{constructor(e){super("notification:tweetFetchError:viewButton:clicked",e)}}class _a extends Ln{buttonIndex;constructor(e){super("notification:general:unknownButton:clicked"),this.buttonIndex=e}}const xa=/^tweet_(\d+)$/,Sa=/^download_(\d+)$/,Aa=e=>Boolean(e.match(xa)),Ea=e=>Boolean(e.match(Sa)),ka=e=>"filename is overwirrten"===e,Ra=e=>{const t=e.match(Sa);if(!t)throw new Error(`${e} is not a download notification id.`);return Number(t[1])},Ia=e=>{const t=e.match(xa);if(!t)throw new Error(`${e} is not a tweet notification id.`);return t[1]};class Oa extends ma{constructor(e){super("notification:downloadFailed:self:clicked",e)}}class $a extends Ln{constructor(){super("notification:filenameOverwritten:self:clicked")}}class Ca extends ba{constructor(e){super("notification:tweetFetchError:self:clicked",e)}}class Ta extends ma{constructor(e){super("notification:downloadFailed:self:closed",e)}}class Pa extends ba{constructor(e){super("notification:tweetFetchError:self:closed",e)}}class ja extends Ln{version;constructor(e){super("runtime:status:installed"),this.version=e}}class Ma extends Ln{currentVersion;previousVersion;constructor(e){super("runtime:status:updated"),this.currentVersion=e.current,this.previousVersion=e.previous}}r(8898);class Da extends Ln{expectedName;finalName;constructor(e,t){super("filename:overwritten"),this.expectedName=e,this.finalName=t}}const Na=e=>e.mapBy((e=>e.downloadConfig)),qa=(e,t)=>async(r,n)=>{const s=await e.getById(r.downloadId);if(!s)return;const{value:i,error:o}=await t.getById(r.downloadId);if(o)return;const a=(0,In.pipe)(i,Na,Fo,Fa),c=(0,In.pipe)(s.filename,Fa);c!==a&&await n.publish(new Da(a,c))},La=/[^\\/]+$/,Fa=e=>{const t=e.match(La);return t?t.at(0)??"":""},Ua=(e,t)=>async()=>{const{value:r,error:n}=await e.get();n?console.error(n):await t(r.uninstallUrl)},Ba=(e,t,r)=>{const n=as.i18n.getMessage((e=>t=>t?`${t}_${e}`:e)(e)(t))||e;return r?(e=>t=>Object.entries(e).reduce(((e,[t,r])=>e.replaceAll(`{{${t.toLowerCase()}}}`,r)),t))(r)(n):n};class za{static viewTweet(){return{title:Ba("dcc839a401","b36390a")}}}class Wa{static retryDownload(){return{title:Ba("942087cc2d","b36390a")}}}class Ha{static ignore(){return{title:Ba("fce77c34d3","33917e2")}}}const Va=({buttons:e,requireInteraction:t,...r})=>({type:"basic",iconUrl:cs().runtime.getURL("assets/icons/<EMAIL>"),contextMessage:"Media Harvest",eventTime:Date.now(),...r,buttons:e,requireInteraction:t});class Ka{static error(e,t){const r=Ba("2a2196801a","1f924ad",{account:e.screenName,"tweet-id":e.tweetId});return Va({title:Ba("eae34ccbc4","1f924ad"),message:r,eventTime:t.getTime(),buttons:[za.viewTweet(),Wa.retryDownload()],requireInteraction:!0})}}const Ga=({title:e,message:t,eventTime:r})=>Va({title:e,message:t,eventTime:r.getTime(),buttons:[za.viewTweet()],requireInteraction:!0});class Qa{static tooManyRequests(e){return Ga({title:Ba("802600d124","1f924ad"),message:Ba("5d5053f03f","1f924ad"),eventTime:e.eventTime})}static notFound(e){return Ga({title:Ba("c1fed0368e","9ffd3d1"),message:Ba("3177e027e2","9ffd3d1"),eventTime:e.eventTime})}static unauthorized(e){return Ga({title:Ba("d089c8a9fc","9ffd3d1"),message:Ba("445e314917","9ffd3d1"),eventTime:e.eventTime})}static forbidden(e){return Ga({title:Ba("78342a0905","9ffd3d1"),message:Ba("b71071945b","9ffd3d1"),eventTime:e.eventTime})}static unknown(e){return Ga({title:Ba("a0973b2764","9ffd3d1",{code:e.code.toString()}),message:Ba("c8119ad044","9ffd3d1"),eventTime:e.eventTime})}static failedToParseTweetInfo(){return Va({title:Ba("3838980a1a","31564b1"),message:Ba("4a87d0c817","31564b1")})}}class Ja{static native(e){return Va({title:Ba("a23ddae9e3","0b8d28d"),message:Ba("d3846e72a3","0b8d28d",{quota:e.remainingQuota.toString(),time:e.resetTime.toLocaleString()}),eventTime:e.eventTime?e.eventTime.getTime():Date.now(),requireInteraction:e.requireInteraction})}}const Za=(e,t)=>async r=>{if("USER_CANCELED"===r.reason)return;console.log("Download was interrupted. Reason:",r.reason);const{value:n,error:s}=await t.getById(r.downloadId);if(s)return;const i=n.mapBy((e=>e.tweetInfo)).mapBy((e=>e)),o=Ka.error(i,r.occuredAt);var a;await e.notify((a=r.downloadId,`download_${a}`),o)},Ya=(e,t)=>async r=>{const{ignoreFilenameOverwritten:n}=await t.get();if(n)return;const s=(e=>Va({title:Ba("f56e2a81af","378c634"),message:Ba("92665f39a5","378c634"),eventTime:e.occuredAt.getTime(),buttons:[Ha.ignore()]}))(r);await e.notify("filename is overwirrten",s)},Xa=e=>async t=>{const r=`tweet_${t.tweetInfo.tweetId}`;let n;switch(t.code){case 429:n=Qa.tooManyRequests({tweetInfo:t.tweetInfo,eventTime:t.occuredAt});break;case 401:n=Qa.unauthorized({tweetInfo:t.tweetInfo,eventTime:t.occuredAt});break;case 403:n=Qa.forbidden({tweetInfo:t.tweetInfo,eventTime:t.occuredAt});break;case 404:n=Qa.notFound({tweetInfo:t.tweetInfo,eventTime:t.occuredAt});break;default:n=Qa.unknown({tweetInfo:t.tweetInfo,eventTime:t.occuredAt,code:t.code})}n&&e.notify(r,n)},ec=e=>`https://x.com/i/web/status/${e}`,tc=async e=>{rc(e)?await cs().tabs.create({url:ec(e.tweetId)}):nc(e)&&await cs().tabs.create({url:ec(e.tweetInfo.tweetId)})},rc=e=>Object.hasOwn(e,"tweetId"),nc=e=>Object.hasOwn(e,"tweetInfo"),sc=e=>async t=>{const{value:r,error:n}=await e.getById(t.downloadId);var s;n?console.error(n):await as.tabs.create({url:(s=ic(r),`https://x.com/i/web/status/${s}`)})},ic=e=>e.mapBy((e=>e.tweetInfo.mapBy((e=>e.tweetId)))),oc=e=>async t=>{const r=new Dn({downloadId:t.downloadId,recordedAt:t.occuredAt,tweetInfo:t.tweetInfo,downloadConfig:t.downloadConfig});await e.save(r)},ac=e=>async t=>{const{value:r,error:n}=await e.get();n||(console.group("Client Information"),console.info("Client UUID:",r.id.value),console.groupEnd())},cc=e=>{console.group("The extension has been updated. Expand to see the details."),console.info("Previous version:",e.previousVersion),console.info("Current version:",e.currentVersion),console.groupEnd()},uc=(e,t)=>async r=>{const n=e=>async()=>{t.notify(`solution-quota-warning:${r.solutionId}`,Ja.native({remainingQuota:r.remainingQuota,resetTime:r.resetTime,requireInteraction:e.requireInteraction}))};if(0===r.remainingQuota&&await n({requireInteraction:!0})(),r.remainingQuota>0){const t=await e.get(r.solutionId);if(!t)return;const s=await t.warnBy(n({requireInteraction:!1}));s&&console.error("Failed to send quota warning notification.",s),await e.save(t)}};class lc{notify(...e){if(1===e.length){const[t]=e;return as.notifications.create(t)}if(2===e.length){const[t,r]=e;return as.notifications.create(t,r)}throw new Error(`${e} are not valid arguments. Expect 2`)}}const dc=(()=>{let e;return()=>(e||(e=new lc),e)})();class fc extends Ln{downloadId;tweetInfo;downloadConfig;constructor(e){super("download:status:dispatched:browser"),this.downloadId=e.id,this.tweetInfo=e.tweetInfo,this.downloadConfig=e.config}}class pc extends Ln{reason;tweetInfo;downloadConfig;constructor(e){super("download:status:failed:browser"),this.reason=e.reason,this.tweetInfo=e.tweetInfo,this.downloadConfig=e.config}}class hc extends Vt{}class mc{#e;#t;askWhereToSave;targetTweet;constructor(e,t){this.targetTweet=e,this.askWhereToSave=t,this.#t=[],this.#e=!0}get isOk(){return this.#e}get events(){return this.#t}downloadTargetToConfig(e){return new Mn({conflictAction:"overwrite",saveAs:this.askWhereToSave,...e.mapBy((e=>e))})}async process(e){const t=e.target instanceof hc?this.downloadTargetToConfig(e.target):e.target,r=await as.downloads.download(Uo(t));if(gc(r))return this.#t.push(new pc({reason:as.runtime.lastError??"Failed to download",config:t,tweetInfo:this.targetTweet})),void(this.#e=!1);this.#t.push(new fc({id:r,config:t,tweetInfo:this.targetTweet}))}}const gc=e=>void 0===e;class yc extends Ln{constructor(){super("download:status:dispatched:aria2")}}const vc=gi().object({action:gi().valid("download-media"),payload:gi().object({tweetId:gi().string().required(),screenName:gi().string().required()}).required()});class bc{payload;constructor(e){this.payload=e}makeResponse(...e){const[t,r]=e;return t?{status:"ok"}:{status:"error",reason:r}}static validate(e){const{value:t,error:r}=vc.validate(e);return r?Ft(r):Ut(new bc(t.payload))}toObject(){return{action:"download-media",payload:this.payload}}}const wc=gi().object({action:gi().valid("check-download-history").required(),payload:gi().object({tweetId:gi().string().required()}).required()});class _c{payload;constructor(e){this.payload=e}static validate(e){const{value:t,error:r}=wc.validate(e);return r?Ft(r):Ut(new _c(t.payload))}makeResponse(...e){const[t,r]=e;return t?{status:"ok",payload:{isExist:r.isExist}}:{status:"error",reason:r}}toObject(){return{action:"check-download-history",payload:this.payload}}}class xc{payload;constructor(e){this.payload=e}toObject(){return{action:"aria2-download",payload:this.payload}}}const Sc=gi().object({action:gi().valid("capture-response").required(),payload:gi().object({type:gi().string().valid("TweetDetail","TweetResultByRestId","UserTweets","UserMedia","HomeTimeline","UserTweetsAndReplies","UserHighlightsTweets","UserArticlesTweets","Bookmarks","Likes","CommunitiesExploreTimeline","ListLatestTweetsTimeline","HomeLatestTimeline").required(),body:gi().string().required()}).required()});class Ac{payload;constructor(e){this.payload=e}makeResponse(...e){const[t,r]=e;return t?{status:"ok"}:{status:"error",reason:r}}toObject(){return{action:"capture-response",payload:this.payload}}static validate(e){const{value:t,error:r}=Sc.validate(e);return r?Ft(r):Ut(new Ac(t.payload))}}class Ec{#r;events;targetTweet;constructor(e){this.targetTweet=e,this.#r=!0,this.events=[]}get isOk(){return this.#r}async process(e){const t=e.target.mapBy((e=>({filename:e.filename,url:e.url}))),r=new xc({...t,referrer:"https://x.com/i/web/status/"+this.targetTweet.tweetId});await(async e=>{const{action:t,payload:r}=e.toObject();switch(t){case"aria2-download":{const e="mpkodccbngfoacfalldjimigbofkhgjn";if(!e)break;return as.runtime.sendMessage(e,r)}}return{reason:"No target extension",status:"error"}})(r),this.events.push(new yc)}}class kc{infra;constructor(e){this.infra=e}cacheTweets(e){return Array.isArray(e)?this.infra.tweetResponseCache.saveAll(...e):this.infra.tweetResponseCache.save(e)}async processUserTimelineResponse(e){const{value:t,error:r}=Mc(e,$c);if(r)return r;const n=Dc(t.data.user.result.timeline.timeline.instructions);return this.cacheTweets(n)}async processRestTweetByIdResponse(e){const{value:t,error:r}=Mc(e,Oc);if(r)return r;const n=Pi(t.data.tweetResult);return!Bt(n)&&bi(n.value)?this.cacheTweets($i(n.value)):void 0}async processTweetDetailResponse(e){const{value:t,error:r}=Mc(e,Ic);if(r)return r;const n=Dc(t.data.threaded_conversation_with_injections_v2.instructions);return this.cacheTweets(n)}async processHomeTimelineResponse(e){const{value:t,error:r}=Mc(e,Cc);if(r)return r;const n=Dc(t.data.home.home_timeline_urt.instructions);return this.cacheTweets(n)}async processBookmarkTimelineResponse(e){const{value:t,error:r}=Mc(e,Tc);if(r)return r;const n=Dc(t.data.bookmark_timeline_v2.timeline.instructions);return this.cacheTweets(n)}async processCommunitiesExploreTimelineResponse(e){const{value:t,error:r}=Mc(e,Pc);if(r)return r;const n=Dc(t.data.viewer.explore_communities_timeline.timeline.instructions);return this.cacheTweets(n)}async processListTimelineResponse(e){const{value:t,error:r}=Mc(e,jc);if(r)return r;const n=Dc(t.data.list.tweets_timeline.timeline.instructions);return this.cacheTweets(n)}async process({type:e,body:t}){let r;switch(e){case"TweetDetail":r=await this.processTweetDetailResponse(t);break;case"TweetResultByRestId":r=await this.processRestTweetByIdResponse(t);break;case"UserTweets":case"UserArticlesTweets":case"UserHighlightsTweets":case"UserTweetsAndReplies":case"UserMedia":case"Likes":r=await this.processUserTimelineResponse(t);break;case"HomeTimeline":case"HomeLatestTimeline":r=await this.processHomeTimelineResponse(t);break;case"CommunitiesExploreTimeline":r=await this.processCommunitiesExploreTimelineResponse(t);break;case"Bookmarks":r=await this.processBookmarkTimelineResponse(t);break;case"ListLatestTweetsTimeline":r=await this.processListTimelineResponse(t)}return r&&(r instanceof gi().ValidationError?(console.group("Invalid response body has been captured."),console.warn(`Response type: ${e}`),console.warn(t),console.groupEnd()):console.error(r)),r}}const Rc=e=>{try{const t=JSON.parse(e);return Ut(t)}catch(e){return Ft(e)}},Ic=gi().object({data:gi().object({threaded_conversation_with_injections_v2:gi().object({instructions:gi().array().required()}).required().unknown(!0)}).required().unknown(!0)}),Oc=gi().object({data:gi().object({tweetResult:gi().object({result:gi().object().required().unknown(!0)}).required().unknown(!0)}).required().unknown(!0)}),$c=gi().object({data:gi().object({user:gi().object({result:gi().object({__typename:gi().valid("User").required()}).unknown(!0).required()}).required().unknown(!0)}).required().unknown(!0)}),Cc=gi().object({data:gi().object({home:gi().object({home_timeline_urt:gi().object({instructions:gi().array().required()}).required().unknown(!0)}).required().unknown(!0)}).unknown(!0).required()}).unknown(!0),Tc=gi().object({data:gi().object({bookmark_timeline_v2:gi().object({timeline:gi().object({instructions:gi().array().required()}).required().unknown(!0)}).required().unknown(!0)}).required().unknown(!0)}).unknown(!0),Pc=gi().object({data:gi().object({viewer:gi().object({explore_communities_timeline:gi().object({timeline:gi().object({instructions:gi().array().required()}).required().unknown(!0)})})}).required().unknown(!0)}).unknown(!0),jc=gi().object({data:gi().object({list:gi().object({tweets_timeline:gi().object({timeline:gi().object({instructions:gi().array().required()}).required().unknown(!0)})})}).required().unknown(!0)}).unknown(!0);function Mc(e,t){const r=Rc(e);if(Bt(r))return r;const{value:n,error:s}=t.validate(r.value);return s?Ft(s):Ut(n)}function Dc(e){return e.reduce(((e,t)=>e.concat((e=>{const t=[];if(wi.isTimelineAddToModule(e)&&t.push(...Wt(e.moduleItems.map(Ti))),wi.isTimelineAddEntries(e)&&t.push(...e.entries.reduce(((e,t)=>e.concat((e=>{const t=[];if(yi(e.content)&&vi(e.content.itemContent)){const r=Pi(e.content.itemContent.tweet_results);zt(r)&&t.push(r.value)}if("TimelineTimelineModule"===e.content.__typename){const r=e.content.items.map((e=>e.item.itemContent)).filter(vi).map((e=>Pi(e.tweet_results)));t.push(...Wt(r))}return t})(t))),[])),wi.isTimelinePinEntry(e)&&vi(e.entry.content.itemContent)){const r=Pi(e.entry.content.itemContent.tweet_results);zt(r)&&t.push(r.value)}return t})(t))),[]).filter(bi).map(Ci).flat()}const Nc=gi().object({action:gi().string().required()}).unknown(!0),qc=e=>({status:"error",reason:e});class Lc{routeMap;constructor(){this.routeMap=new Map}getHandlerByAction(e){return this.routeMap.get(e)}async handle(e){const{value:t,error:r}=Nc.validate(e.message);if(r)return e.response(qc("Invalid message."));const n=this.getHandlerByAction(t.action);return n?n(e):e.response(qc(`Invalid action(action: ${t.action})`))}route(e,t){return this.routeMap.set(e,t),this}}const Fc=(()=>{let e;return()=>e||=new Lc})();class Uc{infra;constructor(e){this.infra=e}async process(e){const{value:t,error:r}=await this.infra.downloadHistoryRepo.hasTweetId(e.tweetId);return r?(console.error(r),!1):t}}class Bc{payload;constructor(e){this.payload=e}makeResponse(e,t){if(!0===e&&this.isResponsePayload(t))return{status:"ok",payload:t};if(!1===e&&"string"==typeof t)return{status:"error",reason:t};throw new Error("Invalid arguments to makeResponse")}isResponsePayload(e){return"object"==typeof e&&null!==e&&"transactionId"in e}toObject(){return{action:"request-tx-id",payload:this.payload}}static validate(e){const{value:t,error:r}=Wc.validate(e);return r?Ft(r):Ut(new Bc(t.payload))}}const zc=gi().object({method:gi().string().required(),path:gi().string().required()}).unknown(!1),Wc=gi().object({action:gi().valid("request-tx-id").required(),payload:zc.required()}).unknown(!1);class Hc extends Ln{tweetInfo;code;constructor(e,t){super("api:twitter:failed"),this.tweetInfo=e,this.code=t}}class Vc extends Ln{tweetInfo;constructor(e){super("parse:tweet:failed"),this.tweetInfo=e}}r(3064),r(2577);class Kc extends Vt{get isVideo(){return"video"===this.props.type}get isThumbnail(){return"thumbnail"===this.props.type}static create(e){return new Kc(e)}}const Gc=e=>e.mapBy((e=>{switch(e.type){case"photo":return"image";case"thumbnail":return"thumbnail";case"video":return"video"}})),Qc=e=>e.mapBy((e=>e.index+1)),Jc=e=>{const t=e.mapBy((e=>new URL(e.url)));for(const e of t.searchParams.keys())t.searchParams.delete(e);return ps().parse(t.pathname)};class Zc{infra;constructor(e){this.infra=e}async process({tweetInfo:e,xTransactionIdProvider:t}){const r=await this.downloadFromCache(e);if(r)return r;const n=this.infra.solutionProvider(),s=function(e){return!0===e.isTransactionIdConsumer}(n)?await n.process({tweetId:e.tweetId,transactionIdProvider:t}):await n.process({tweetId:e.tweetId});return await this.infra.eventPublisher.publishAll(...n.events),await this.reportSolutionStatistics(n.statistics),Bt(s)?this.failDownload(s.error,e):this.processDownload(e,s.value)}async downloadFromCache(e){const{value:t}=await this.infra.tweetCacheRepo.get(e.tweetId);if(!t)return!1;const r=t instanceof pi?t:t.mapBy((e=>e.tweet));return this.processDownload(e,r)}async processDownload(e,t){await this.saveDownloadHistory((e=>{const t=e.images.find((e=>0===e.mapBy((e=>e.index))))?.getVariantUrl("thumb"),{id:r,hashtags:n,createdAt:s}=e.mapBy((e=>({id:e.id,hashtags:e.hashtags,createdAt:e.createdAt}))),i=new Jt(r);return new Zt(i,{mediaType:e.mediaType,downloadTime:new Date,hashtags:n,tweetTime:s,thumbnail:t,tweetUser:e.user})})(t));const r=await this.buildDownloader(e),n=await this.createDownloadCommands(t);return await Promise.allSettled(n.map((e=>r.process(e)))),await this.infra.eventPublisher.publishAll(...r.events),r.isOk}async createDownloadCommands(e){const t=await this.infra.filenameSettingRepo.get(),{includeVideoThumbnail:r}=await this.infra.featureSettingsRepo.get();return(e=>{const t=(e=>{const{id:t,createdAt:r}=e.mapBy((e=>({id:e.id,createdAt:e.createdAt})));return n=>{const s=Jc(n);return new Kc({tweetId:t,createdAt:r,tweetUser:e.user,type:Gc(n),source:n.getVariantUrl("orig"),serial:Qc(n),ext:s.ext,hash:s.name})}})(e);return e.medias.map(t)})(e).filter((e=>r||!e.isThumbnail)).map(Yc(t)).map(Xc)}async failDownload(e,t){if(e instanceof Ho){const r=eu(e?.cause)??401;await this.infra.eventPublisher.publish(new Hc(t,r))}else if(e instanceof Vo){const r=eu(e?.cause)??404;await this.infra.eventPublisher.publish(new Hc(t,r))}else e instanceof Ko?!1===e.isInternalControl&&await this.infra.eventPublisher.publish(new Hc(t,429)):e instanceof Go?await this.infra.eventPublisher.publish(new Vc(t)):console.error("An unexpected error occurred",e);return!1}async saveDownloadHistory(e){const t=await this.infra.downloadHistoryRepo.save(e);t&&console.error(t)}async buildDownloader(e){const{enableAria2:t,askWhereToSave:r}=await this.infra.downloadSettingsRepo.get();return(t?this.infra.downloaderBuilder.aria2:this.infra.downloaderBuilder.browser)({targetTweet:e,shouldPrompt:r})}async reportSolutionStatistics(e){}}const Yc=e=>t=>new hc({url:t.mapBy((e=>e.source)),filename:e.makeFilename(t)}),Xc=e=>({target:e}),eu=e=>{if(e&&"object"==typeof e){if("code"in e&&"number"==typeof e.code)return e.code;if("statusCode"in e&&"number"==typeof e.statusCode)return e.statusCode}},tu=/^https:\/\/(www\.)?x\.com\//,ru=s();(()=>{const e=new vt({dsn:"https://<EMAIL>/6263910",tracesSampleRate:.3,environment:"production",release:"twitter-media-harvest(chrome)@4.4.3",ignoreErrors:["Failed to fetch","network error","Download canceled by the user","intermediate value"],integrations:[At({handled:!0,levels:["error"]})],transport:$t,stackParser:Nt,attachStacktrace:!0,sendClientReports:!0});se().setClient(e),e.init()})();const nu=s();(e=>{const t=e??s(),r=dc(),n=(i=No,async e=>{await i.removeById(e.downloadId)});var i;const o=(a=Mo,c=Oo,async e=>{let t;const r=await a.get();if((e=>Object.hasOwn(e,"downloadId"))(e)){const n=await c.getById(e.downloadId);t=r.increase({downloadCount:1,trafficUsage:n?n.fileSize:0})}else t=r.increase({downloadCount:1,trafficUsage:0});await a.save(t)});var a,c;const u=(d="client:sync",l=e=>navigator.locks.request(d,e),e=>async(t,r)=>{const n=await l((async t=>{if(!t)return;const{value:n,error:s}=await e.get();if(s)return s;if(!n.shouldSync)return;const i=await e.sync(n);if(i)return i;await r.publishAll(...n.events)}));n&&console.error(n)})(To);var l,d;const f=(p=To,async e=>{const{value:t,error:r}=await p.get();r||function(e){ie().setUser(e)}({client_id:{clientId:t.id.value}.clientId})});var p,h,m,g,y;t.register("runtime:status:installed",[Ua(To,as.runtime.setUninstallURL),ac(To),f]).register("runtime:status:updated",[cc,ac(To),f,async()=>await ia.clearCacheStorage()]).register("download:status:dispatched:aria2",[o,u]).register("download:status:dispatched:browser",[oc(No)]).register("download:status:completed",[qa(Oo,No),o,u,n]).register("download:status:interrupted",[Za(r,No)]).register("filename:overwritten",[Ya(r,qo)]).register("api:twitter:failed",[Xa(r)]).register("parse:tweet:failed",[]).register("notification:downloadFailed:self:closed",n).register("notification:downloadFailed:retryButton:clicked",[(m=Po,g=No,y=e=>new mc(e.targetTweet,e.shouldPrompt),async(e,t)=>{const{value:r,error:n}=await g.getById(e.downloadId);if(n)return;const{tweetInfo:s,downloadConfig:i}=r.mapBy(ds("tweetInfo","downloadConfig")),o=await m.get(),a=y({shouldPrompt:o.askWhereToSave,targetTweet:s});await a.process({target:i}),t&&await t.publishAll(...a.events)}),n]).register("notification:downloadFailed:viewButton:clicked",[sc(No),n]).register("notification:tweetFetchError:self:closed",[]).register("notification:tweetFetchError:self:clicked",tc).register("notification:tweetFetchError:viewButton:clicked",tc).register("client:synced",f).register("download:status:failed:browser",[]).register("tweetSolution:quota:insufficient",[uc(Lo,r)]).register("tweetSolution:quota:changed",[(h=Lo,async e=>{let t=await h.get(e.solutionId);t?t.updateQuota(new Es({quota:e.remainingQuota,resetAt:e.resetTime})):t=As.create(e.solutionId,{isRealtime:!1,quota:new Es({quota:e.remainingQuota,resetAt:e.resetTime})}),await h.save(t)})])})(nu);const su=Fc();var iu;su.route("download-media",(e=>{const t=new Zc(e);return async e=>{const{value:r,error:n}=bc.validate(e.message);if(n)return e.response(qc(n.message));const s=await t.process({tweetInfo:new Nn(r.payload),xTransactionIdProvider:(c=e.sender,void 0!==c.tab&&(o=e.sender.tab,"number"==typeof o.id&&"string"==typeof(a=o.url)&&tu.test(a))?(i=e.sender.tab.id,async(e,t)=>{const r=await(e=>async t=>as.tabs.sendMessage(e,t.toObject()))(i)(new Bc({path:e,method:t}));return"ok"===r.status?Ut(r.payload.transactionId):Ft(new Error("Failed to request transaction id"))}):void 0)});var i,o,a,c;return e.response(s?r.makeResponse(s):r.makeResponse(s,"Failed to complete download task."))}})({eventPublisher:ru,downloadHistoryRepo:Do,downloadSettingsRepo:Po,filenameSettingRepo:Co,featureSettingsRepo:jo,downloaderBuilder:{aria2:e=>new Ec(e.targetTweet),browser:e=>new mc(e.targetTweet,e.shouldPrompt)},tweetCacheRepo:da,solutionProvider:()=>ia})).route("check-download-history",(e=>{const t=new Uc(e);return async e=>{const{value:r,error:n}=_c.validate(e.message);if(n)return e.response(qc(n.message));const s=await t.process({tweetId:r.payload.tweetId});return e.response(r.makeResponse(!0,{isExist:s}))}})({downloadHistoryRepo:Do})).route("capture-response",(e=>{const t=new kc(e);return async e=>{const{value:r,error:n}=Ac.validate(e.message);return n?e.response(qc(n.message)):await t.process({body:r.payload.body,type:r.payload.type})?e.response(r.makeResponse(!1,"Not implemented")):e.response(r.makeResponse(!0))}})({tweetResponseCache:da})),cs().runtime.onMessage.addListener(((e,t,r)=>(su.handle({message:e,sender:t,response:r}),!0))),cs().runtime.onInstalled.addListener((iu=nu,async e=>{if("browser_update"===e.reason)return;const t=xo();"install"===e.reason&&await iu.publish(new ja(t)),"update"===e.reason&&await iu.publish(new Ma({current:t,previous:e?.previousVersion??t}))})),cs().downloads.onChanged.addListener(((e,t,r)=>async n=>{const{state:s}=n;if(!s)return;const i=await e.getById(n.id);var o;i&&t.process({item:i,allowJSON:!1})&&("complete"!==(o=s).current||"in_progress"!==o.previous?(e=>"interrupted"===e.current&&"in_progress"===e.previous)(s)&&await r.publish(new ha(n.id,n.error?.current??"unknown")):await r.publish(new pa(n.id)))})(Oo,new e(cs().runtime.id),nu)),cs().notifications.onClosed.addListener((e=>async(t,r)=>{if(Aa(t)){const r=Ia(t);await e.publish(new Pa(r))}else if(Ea(t)){const r=Ra(t);await e.publish(new Ta(r))}})(nu)),cs().notifications.onClicked.addListener((e=>async t=>{if(Aa(t)){const r=Ia(t);await e.publish(new Ca(r))}else if(Ea(t)){const r=Ra(t);await e.publish(new Oa(r))}else ka(t)&&await e.publish(new $a)})(nu)),cs().notifications.onButtonClicked.addListener((e=>async(t,r)=>{if(Aa(t)){const n=Ia(t);let s;s=0===r?new wa(n):new _a(r),await e.publish(s)}else if(Ea(t)){const n=Ra(t);let s;switch(r){case 1:s=new ga(n);break;case 0:s=new ya(n);break;default:s=new _a(r)}await e.publish(s)}else ka(t)&&0===r&&await e.publish(new va)})(nu))})()})();
//# sourceMappingURL=sw.js.map