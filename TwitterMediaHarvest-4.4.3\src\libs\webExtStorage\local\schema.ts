/*
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 */
import type {
  ClientInfo,
  DownloadSettings,
  FeatureSettings,
  SentryExceptionRecord,
  SolutionQuotaCollection,
  V4Statistics,
  WarningSettings,
  XTransactionIdCollection,
} from '#schema'

export interface LocalStorageSchema
  extends FeatureSettings,
    DownloadSettings,
    ClientInfo,
    V4Statistics,
    SentryExceptionRecord,
    WarningSettings,
    SolutionQuotaCollection,
    XTransactionIdCollection {}
