// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`unit test for latest fetch tweet command should handle FetchTweetError 1`] = `
{
  "$metadata": {
    "httpStatusCode": 404,
    "quotaResetTime": 1970-08-07T14:54:48.000Z,
    "remainingQuota": 100,
  },
  "tweetResult": {
    "error": [FetchTweetError: Failed to fetch tweet],
    "value": undefined,
  },
}
`;

exports[`unit test for latest fetch tweet command should handle a parse error 1`] = `
{
  "$metadata": {
    "httpStatusCode": 200,
    "quotaResetTime": 1970-08-07T14:54:48.000Z,
    "remainingQuota": 100,
  },
  "tweetResult": {
    "error": [ParseTweetError: Failed to get result],
    "value": undefined,
  },
}
`;

exports[`unit test for latest fetch tweet command should handle a parse error when body is not json 1`] = `
{
  "$metadata": {
    "httpStatusCode": 200,
    "quotaResetTime": 1970-08-07T14:54:48.000Z,
    "remainingQuota": 100,
  },
  "tweetResult": {
    "error": [ParseTweetError: Failed to parse body],
    "value": undefined,
  },
}
`;

exports[`unit test for latest fetch tweet command should return a successful result when the image response is ok 1`] = `
{
  "$metadata": {
    "httpStatusCode": 200,
    "quotaResetTime": 1970-08-07T14:54:48.000Z,
    "remainingQuota": 100,
  },
  "tweetResult": {
    "error": undefined,
    "value": {
      "createdAt": 2022-11-02T19:47:39.000Z,
      "hashtags": [],
      "id": "1587894226695884800",
      "images": [
        {
          "index": 0,
          "type": "photo",
          "url": "https://pbs.twimg.com/media/FglVYVmXkAIWB5w.jpg",
        },
      ],
      "user": {
        "displayName": "Elon Musk",
        "isProtected": false,
        "screenName": "elonmusk",
        "userId": "44196397",
      },
      "videos": [],
    },
  },
}
`;
