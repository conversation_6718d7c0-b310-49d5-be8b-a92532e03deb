{"name": "Better TweetDeck", "description": "Take TweetDeck to the next level!", "short_name": "Better TweetDeck", "default_locale": "en", "version": "4.11.3", "manifest_version": 3, "icons": {"16": "build/assets/icons/icon-16.png", "32": "build/assets/icons/icon-32.png", "48": "build/assets/icons/icon-48.png", "96": "build/assets/icons/icon-96.png", "128": "build/assets/icons/icon-128.png", "256": "build/assets/icons/icon-256.png", "512": "build/assets/icons/icon-512.png"}, "content_scripts": [{"matches": ["*://tweetdeck.twitter.com/*", "*://tweetdeck.dimden.dev/*", "https://twitter.com/i/tweetdeck", "https://x.com/i/tweetdeck"], "js": ["build/content.js"], "run_at": "document_idle", "all_frames": true}], "background": {"service_worker": "background.js", "type": "module"}, "web_accessible_resources": [{"resources": ["build/inject.js", "*.png", "build/emoji-sheet-64.png"], "matches": ["*://tweetdeck.twitter.com/*", "*://tweetdeck.dimden.dev/*", "*://twitter.com/*", "*://x.com/*"]}], "permissions": ["storage", "contextMenus", "notifications"], "content_security_policy": {"extension_pages": "img-src https: data: 'self' *; default-src; font-src 'self' * data:; connect-src * https:; style-src 'unsafe-inline'; script-src 'self'"}, "host_permissions": ["*://tweetdeck.twitter.com/*", "*://tweetdeck.dimden.dev/*", "*://twitter.com/i/tweetdeck", "*://x.com/i/tweetdeck", "https://*.tenor.com/*", "https://*.giphy.com/*", "https://*.twimg.com/*"], "externally_connectable": {"matches": ["*://tweetdeck.twitter.com/*", "*://tweetdeck.dimden.dev/*", "*://twitter.com/*", "*://x.com/*"]}, "options_page": "build/options/ui.html"}