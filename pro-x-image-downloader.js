// ==UserScript==
// @name            Pro X.com Image Downloader
// @namespace       http://violentmonkey.github.io/
// @version         1.0.0
// @description     Add download button to pro.x.com posts for images - Violentmonkey version
// <AUTHOR>
// @match           https://pro.x.com/*
// @grant           GM_download
// @grant           GM_xmlhttpRequest
// @connect         twimg.com
// @connect         pbs.twimg.com
// @connect         pro.x.com
// ==/UserScript==

(function() {
    'use strict';

    const BUTTON_STYLE = `
        .pro-x-image-downloader-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 4px 12px;
            margin: 0 4px;
            background-color: rgba(15, 20, 25, 0.75);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 9999px;
            font-size: 13px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.2s;
            z-index: 1000;
        }
        .pro-x-image-downloader-btn:hover {
            background-color: rgba(39, 44, 48, 0.75);
        }
        .pro-x-image-downloader-btn svg {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
        .pro-x-image-downloader-btn.downloading {
            opacity: 0.6;
            cursor: not-allowed;
        }
    `;

    // Add styles to page
    const style = document.createElement('style');
    style.textContent = BUTTON_STYLE;
    document.head.appendChild(style);

    // Download icon SVG
    const DOWNLOAD_ICON = `<svg viewBox="0 0 24 24" fill="currentColor"><g><path d="M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"></path></g></svg>`;

    // Extract tweet ID from URL or closest article element
    function getTweetId(element) {
        const article = element.closest('article');
        if (!article) return null;
        
        const link = article.querySelector('a[href*="/status/"]');
        if (!link) return null;
        
        const match = link.href.match(/\/status\/(\d+)/);
        return match ? match[1] : null;
    }

    // Get highest quality image URL
    function getOriginalImageUrl(url) {
        if (!url || !url.includes('pbs.twimg.com')) return url;
        
        // Remove any existing size parameters and add ?format=jpg&name=orig
        const baseUrl = url.split('?')[0].split('&')[0];
        const format = baseUrl.includes('.png') ? 'png' : 'jpg';
        return `${baseUrl}?format=${format}&name=orig`;
    }

    // Download file using GM_download
    function downloadFile(url, filename) {
        if (!url) return;

        console.log(`Downloading: ${filename} from ${url}`);
        
        GM_download({
            url: url,
            name: filename,
            saveAs: false,
            onerror: function(err) {
                console.error('Download failed:', err);
                // Fallback to window.open
                window.open(url, '_blank');
            },
            onload: function() {
                console.log('Download completed:', filename);
            }
        });
    }

    // Create download button
    function createDownloadButton() {
        const button = document.createElement('div');
        button.className = 'pro-x-image-downloader-btn';
        button.innerHTML = DOWNLOAD_ICON + 'Download';
        button.title = 'Download image(s)';
        return button;
    }

    // Add download button to tweet
    function addDownloadButton(article) {
        // Check if button already exists
        if (article.querySelector('.pro-x-image-downloader-btn')) return;

        // Find action bar (where like, retweet buttons are)
        const actionBar = article.querySelector('[role="group"]');
        if (!actionBar) return;

        // Check if tweet has images (exclude videos)
        const images = article.querySelectorAll('img[src*="pbs.twimg.com"]');
        if (!images || images.length === 0) return;

        const button = createDownloadButton();
        
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Prevent multiple clicks
            if (button.classList.contains('downloading')) return;
            button.classList.add('downloading');
            button.innerHTML = DOWNLOAD_ICON + 'Downloading...';

            const tweetId = getTweetId(article);
            const timestamp = new Date().getTime();

            try {
                if (images.length === 1) {
                    // Single image
                    const imageUrl = getOriginalImageUrl(images[0].src);
                    const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                    const filename = `pro_x_${tweetId}_${timestamp}.${ext}`;
                    downloadFile(imageUrl, filename);
                } else if (images.length > 1) {
                    // Multiple images
                    images.forEach((img, index) => {
                        const imageUrl = getOriginalImageUrl(img.src);
                        const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                        const filename = `pro_x_${tweetId}_${timestamp}_${index + 1}.${ext}`;
                        downloadFile(imageUrl, filename);
                    });
                }
            } catch (error) {
                console.error('Error downloading images:', error);
            } finally {
                // Reset button state after a delay
                setTimeout(() => {
                    button.classList.remove('downloading');
                    button.innerHTML = DOWNLOAD_ICON + 'Download';
                }, 2000);
            }
        });

        // Insert button into action bar
        const lastButton = actionBar.lastElementChild;
        if (lastButton) {
            actionBar.insertBefore(button, lastButton);
        } else {
            actionBar.appendChild(button);
        }
    }

    // Observer to detect new tweets
    const observer = new MutationObserver((mutations) => {
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });
    });

    // Start observing
    function init() {
        console.log('Pro X.com Image Downloader initialized');
        
        // Process existing tweets
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });

        // Observe for new tweets
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Wait for page to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('Pro X.com Image Downloader script loaded');
})();
