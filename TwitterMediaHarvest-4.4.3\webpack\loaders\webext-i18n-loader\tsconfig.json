{
  "compilerOptions": {
    // project options
    "strict": true,
    "lib": ["ESNext"], // specifies which default set of type definitions to use ("DOM", "ES6", etc)
    "outDir": "dist", // .js (as well as .d.ts, .js.map, etc.) files will be emitted into this directory.,
    "module": "NodeNext",
    "removeComments": true, // Strips all comments from TypeScript files when converting into JavaScript- you rarely read compiled code so this saves space
    "target": "ES6", // Target environment. Most modern browsers support ES6, but you may want to set it to newer or older. (defaults to ES3)
    // Module resolution
    "baseUrl": "./", // Lets you set a base directory to resolve non-absolute module names.
    "esModuleInterop": true, // fixes some issues TS originally had with the ES6 spec where TypeScript treats CommonJS/AMD/UMD modules similar to ES6 module
    // "moduleResolution": "nodenext", // Pretty much always node for modern JS. Other option is "classic"
    "paths": {}, // A series of entries which re-map imports to lookup locations relative to the baseUrl
    // Source Map
    "sourceMap": true, // enables the use of source maps for debuggers and error reporting etc
    "sourceRoot": "/", // Specify the location where a debugger should locate TypeScript files instead of relative source locations.
    // Strict Checks
    "alwaysStrict": true, // Ensures that your files are parsed in the ECMAScript strict mode, and emit “use strict” for each source file.
    "allowUnreachableCode": false, // pick up dead code paths
    "noImplicitAny": true, // In some cases where no type annotations are present, TypeScript will fall back to a type of any for a variable when it cannot infer the type.
    "strictNullChecks": true, // When strictNullChecks is true, null and undefined have their own distinct types and you’ll get a type error if you try to use them where a concrete value is expected.
    // Linter Checks
    "noImplicitReturns": true,
    "noUncheckedIndexedAccess": true, // accessing index must always check for undefined
    "noUnusedLocals": true, // Report errors on unused local variables.
    "noUnusedParameters": true, // Report errors on unused parameters in functions
    "declaration": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*.ts"],
  "exclude": [
    "node_modules/**/*",
    "src/**/*.spec.ts",
    "src/**/*.test.ts",
    "integrated_tests/**",
    "fixtures/**"
  ]
}
