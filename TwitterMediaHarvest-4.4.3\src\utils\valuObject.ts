export const propsExtractor = <
  Props extends LiteralObject,
  <PERSON>p<PERSON><PERSON> extends keyof Props,
>(
  ...props: Prop<PERSON><PERSON>[]
): ((obj: Props) => Pick<Props, PropKey>) => {
  return (obj: Props) => {
    const result: Partial<Props> = {}
    for (const prop of props) {
      if (Object.hasOwn(obj, prop)) {
        result[prop] = obj[prop]
      }
    }
    return result as Pick<Props, PropKey>
  }
}
