name: Release

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

env:
  artifact_name: 'pkg'

jobs:
  generate_artifact:
    name: Generate Artifact
    runs-on: ubuntu-latest
    environment: release
    steps:
      - uses: actions/checkout@v4

      - name: Check env file
        run: |
          echo "${{ secrets.ENV_FILE }}" | base64 -d >> .env
          cat .env | grep "DSN" -q || exit 1

      # - name: Extract version
      #   id: version
      #   run: |
      #     TAG=${{ github.ref_name }}
      #     VERSION=${TAG#v}
      #     echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Enable corepack
        run: corepack enable

      - name: Setup Node.js
        uses: actions/setup-node@v4.4.0
        with:
          node-version: '22.x'
          cache: 'yarn'

      - name: Install Dependencies
        run: |
          yarn install

      - name: Test and Build
        run: |
          yarn lint
          yarn ci:all
          yarn check:all
          yarn build:all

      - name: Sign Firefox xpi
        continue-on-error: true
        run: |
          yarn web-ext sign -s build/firefox-signed -a dist --ignore-files "*.map" --channel unlisted --api-key ${{ secrets.FF_JWT_ISSUER }} --api-secret ${{ secrets.FF_JWT_SECRET }}
          mv dist/*.xpi dist/<EMAIL>-${{ github.ref_name }}.xpi

      - name: Create Artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.artifact_name }}
          path: |
            dist/
            build/
          if-no-files-found: error
          retention-days: 7

  github_release:
    name: Create Github Release
    environment: release
    runs-on: ubuntu-latest
    needs: generate_artifact

    steps:
      - uses: actions/checkout@v4

      - name: Download Artifact
        id: download-artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ env.artifact_name }}

      - name: Extract Changelog
        id: changelog
        uses: zogot/kacl-parser@1.0.0
        with:
          version: ${{ github.ref_name }}

      - name: Create Repository Release
        id: create-release
        uses: ncipollo/release-action@v1
        with:
          name: 'TwitterMediaHarvest ${{ github.ref_name }}'
          artifacts: ${{ format('{0}/dist/*.zip,{0}/dist/*.xpi', steps.download-artifact.outputs.download-path) }}
          token: ${{ secrets.GITHUB_TOKEN }}
          body: ${{ steps.changelog.outputs.release }}

      - name: Rollback Release
        if: failure() && steps.create-release.outputs.id != ''
        uses: author/action-rollback@stable
        with:
          release_id: ${{ steps.create-release.id }}
          tag: ${{ github.ref_name }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  sentry_release:
    name: Create Sentry Release
    environment: release
    runs-on: ubuntu-latest
    needs: generate_artifact
    strategy:
      matrix:
        target: ['chrome', 'edge', 'firefox']

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20.x'

      - name: Make release name
        id: release-name
        run: |
            echo "release=$(node ./utils/make-release-name.mjs --browser=${{ matrix.target }})" >> $GITHUB_OUTPUT

      - name: Download Artifact
        id: download-artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ env.artifact_name }}

      - name: Create Sentry release for ${{ matrix.target }}
        uses: getsentry/action-release@v1
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
          # SENTRY_URL: https://sentry.io/
        with:
          version: ${{ steps.release-name.outputs.release }}
          url_prefix: '~/'
          environment: production
          sourcemaps: ${{ format('{0}/build/{1}', steps.download-artifact.outputs.download-path, matrix.target) }}

  chrome_release:
    name: Release to Chrome Web Store
    environment: release
    runs-on: ubuntu-latest
    needs:
      - generate_artifact
      - sentry_release

    steps:
      - uses: actions/checkout@v4

      - name: Download Artifact
        id: download_artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ env.artifact_name }}

      - name: Get file path
        id: get_filepath
        run: |
          echo "zip_file=$(ls ${{ steps.download_artifact.outputs.download-path }}/dist/*chrome*)" >> $GITHUB_OUTPUT

      - name: Publish to Chrome Web Store
        uses: mobilefirstllc/cws-publish@latest
        with:
          action: 'publish'
          client_id: ${{ secrets.CWS_CLIENT }}
          client_secret: ${{ secrets.CWS_SECRET }}
          refresh_token: ${{ secrets.CWS_TOKEN }}
          extension_id: hpcgabhdlnapolkkjpejieegfpehfdok
          zip_file: ${{ steps.get_filepath.outputs.zip_file }}

  # firefox_release:
  #   name: Release to Firefox Addons Store
  #   runs-on: ubuntu-latest
  #   environment: release
  #   needs:
  #     - generate_artifact
  #     - sentry_release

  #   steps:
  #     - uses: actions/checkout@v3

  #     - name: Download Artifact
  #       id: download_artifact
  #       uses: actions/download-artifact@v4
  #       with:
  #         name: ${{ env.artifact_name }}

  #     - name: Get file path
  #       id: get_filepath
  #       run: |
  #         echo "zip_file=$(ls ${{ steps.download_artifact.outputs.download-path }}/dist/*firefox*)" >> $GITHUB_OUTPUT

  #     - name: Archive source code
  #       id: archive_source_code
  #       run: |
  #         ZIP_NAME=source.zip
  #         git archive --format zip --add-file .env -o $ZIP_NAME HEAD
  #         echo "zip_file=$ZIP_NAME" >> $GITHUB_OUTPUT

  #     - name: Upload to Firefox addons store
  #       uses: cardinalby/webext-buildtools-firefox-addons-action@v1
  #       with:
  #         zipFilePath: ${{ steps.get_filepath.outputs.zip_file }}
  #         extensionId: '<EMAIL>'
  #         jwtIssuer: ${{ secrets.FF_JWT_ISSUER }}
  #         jwtSecret: ${{ secrets.FF_JWT_SECRET }}
  #         sourcesZipFilePath: ${{ steps.archive_source_code.outputs.zip_file }}

  edge_release:
    name: Release to Edge Add-ons Store
    runs-on: ubuntu-latest
    environment: release
    needs:
      - generate_artifact
      - sentry_release

    steps:
      - uses: actions/checkout@v4

      - name: Download Artifact
        id: download-artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ env.artifact_name }}

      - name: Get file path
        id: get-filepath
        run: |
          echo "zip_file=$(ls ${{ steps.download-artifact.outputs.download-path }}/dist/*edge*)" >> $GITHUB_OUTPUT

      - name: Upload to Edge Add-ons Store
        uses: wdzeng/edge-addon@v2
        with:
          product-id: ${{ secrets.MS_STORE_PRODUCT_ID }}
          client-id: ${{ secrets.MS_STORE_CLIENT_ID }}
          api-key: ${{ secrets.MS_STORE_API_KEY }}
          zip-path: ${{ steps.get-filepath.outputs.zip_file }}
