// ==UserScript==
// @name         X Pro Unified Media Filter
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Make Images and Videos filters also include GIFs in X Pro (TweetDeck)
// <AUTHOR>
// @match        https://pro.x.com/*
// @match        https://pro.twitter.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // Function to click the GIF filter when Images or Videos is selected
    function syncGifFilter(targetHref) {
        // Find all filter links
        const filterLinks = document.querySelectorAll('a[href*="filter%3A"]');
        
        filterLinks.forEach(link => {
            const href = link.getAttribute('href');
            
            // Check if this is the Images or Videos filter being activated
            if (href && (href.includes('filter%3Aimages') || href.includes('filter%3Anative_video'))) {
                // Find the corresponding GIF filter in the same column
                const column = link.closest('[data-testid="column"]');
                if (column) {
                    const gifLink = column.querySelector('a[href*="filter%3Aanimated_gif"]');
                    if (gifLink && !href.includes('filter%3Aanimated_gif')) {
                        // Modify the href to include GIF filter
                        const newHref = href.replace(/(\?|&)q=/, '$1q=filter%3Aanimated_gif%20OR%20');
                        link.setAttribute('href', newHref);
                    }
                }
            }
        });
    }

    // Function to modify filter behavior
    function modifyFilters() {
        // Find all clickable filter elements
        const filterElements = document.querySelectorAll('[role="button"][aria-label*="Column settings"], [data-testid="drawerAnimatedDiv"]');
        
        filterElements.forEach(element => {
            // Check if we've already modified this element
            if (element.dataset.modifiedFilter) return;
            
            element.addEventListener('click', function(e) {
                setTimeout(() => {
                    // Find Images and Videos options
                    const imageOption = document.querySelector('div[style*="rgb(231, 233, 234)"] input[aria-describedby*="IMAGES"]');
                    const videoOption = document.querySelector('div[style*="rgb(231, 233, 234)"] input[aria-describedby*="VIDEOS"]');
                    const gifOption = document.querySelector('div[style*="rgb(231, 233, 234)"] input[aria-describedby*="GIFS"]');
                    
                    if (imageOption && gifOption) {
                        // Clone the original click handler
                        const originalImageClick = imageOption.onclick;
                        imageOption.onclick = function(event) {
                            if (originalImageClick) originalImageClick.call(this, event);
                            // Also check the GIF option
                            if (imageOption.checked && !gifOption.checked) {
                                gifOption.click();
                            }
                        };
                    }
                    
                    if (videoOption && gifOption) {
                        // Clone the original click handler
                        const originalVideoClick = videoOption.onclick;
                        videoOption.onclick = function(event) {
                            if (originalVideoClick) originalVideoClick.call(this, event);
                            // Also check the GIF option
                            if (videoOption.checked && !gifOption.checked) {
                                gifOption.click();
                            }
                        };
                    }
                }, 500);
            });
            
            element.dataset.modifiedFilter = 'true';
        });
    }

    // Alternative approach: Intercept navigation and modify URLs
    function interceptFilterClicks() {
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a');
            if (!link) return;
            
            const href = link.getAttribute('href');
            if (!href) return;
            
            // Check if this is an Images or Videos filter link
            if (href.includes('filter%3Aimages') || href.includes('filter%3Anative_video')) {
                // Check if GIF filter is already included
                if (!href.includes('filter%3Aanimated_gif')) {
                    e.preventDefault();
                    
                    // Build new URL with combined filters
                    let newHref = href;
                    if (href.includes('filter%3Aimages')) {
                        newHref = href.replace('filter%3Aimages', '(filter%3Aimages%20OR%20filter%3Aanimated_gif)');
                    } else if (href.includes('filter%3Anative_video')) {
                        newHref = href.replace('filter%3Anative_video', '(filter%3Anative_video%20OR%20filter%3Aanimated_gif)');
                    }
                    
                    // Navigate to the new URL
                    window.location.href = newHref;
                }
            }
        }, true);
    }

    // Watch for DOM changes to reapply modifications
    const observer = new MutationObserver(function(mutations) {
        modifyFilters();
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Initial setup
    setTimeout(() => {
        modifyFilters();
        interceptFilterClicks();
    }, 1000);

    // Also modify any existing filter links on page load
    setInterval(() => {
        const filterLinks = document.querySelectorAll('a[href*="filter%3A"]');
        filterLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href.includes('filter%3Aimages') || href.includes('filter%3Anative_video'))) {
                if (!href.includes('filter%3Aanimated_gif')) {
                    let newHref = href;
                    if (href.includes('filter%3Aimages')) {
                        newHref = href.replace('filter%3Aimages', '(filter%3Aimages%20OR%20filter%3Aanimated_gif)');
                    } else if (href.includes('filter%3Anative_video')) {
                        newHref = href.replace('filter%3Anative_video', '(filter%3Anative_video%20OR%20filter%3Aanimated_gif)');
                    }
                    link.setAttribute('href', newHref);
                }
            }
        });
    }, 2000);

})();