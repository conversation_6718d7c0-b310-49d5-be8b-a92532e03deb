{"app_desc": {"message": "Faites passer TweetDeck au niveau supérieur!"}, "app_name": {"message": "Better TweetDeck"}, "settings_title": {"message": "Better TweetDeck"}, "settings_show_cards_inside_columns": {"message": "Afficher les cartes des tweet à l'interieur des colonnes"}, "settings_show_profile_badges_on_top_of_avatars": {"message": "Afficher les badges de profil sur les avatars"}, "settings_collapse_read_dms": {"message": "<PERSON><PERSON><PERSON><PERSON> les MP lus"}, "settings_freeze_gifs_in_profile_pictures": {"message": "Ne pas animer les GIFs des photos de profil"}, "settings_remove_t_co_redirection_on_links": {"message": "Retirer la redirection t.co sur les liens"}, "settings_make_buttons_smaller_in_the_composer": {"message": "Rendre les boutons plus petits dans le composeur"}, "settings_reflect_new_tweets_and_dms_in_the_tabs_title": {"message": "<PERSON><PERSON> les nouveaux tweets et MP dans le titre de l'onglet"}, "settings_auto_switch_light_theme": {"message": "Basculer sur le thème lumineux lorsque l'OS est en mode clair"}, "settings_scrollbar_default": {"message": "<PERSON><PERSON>"}, "settings_scrollbar_thin": {"message": "Fine"}, "settings_scrollbar_hidden": {"message": "Masquée"}, "settings_style_of_scrollbars": {"message": "Style des barres de défilement"}, "settings_show_clear_button_column": {"message": "Affiche<PERSON> le bouton \"Effacer\" dans l'entête des colonnes"}, "settings_show_collapse_button_in_columns_header": {"message": "Affiche<PERSON> le bouton \"Réduire\" dans l'entête des colonnes"}, "settings_hide_icons_on_top_of_columns": {"message": "Masquer les icônes en haut des colonnes"}, "settings_use_old_style_of_replies": {"message": "Utiliser un ancien style de réponses (@mentions sur la même ligne)"}, "settings_timestamp_relative": {"message": "Relatif"}, "settings_timestamp_custom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_date_format": {"message": "Format de la date"}, "settings_short_time_after_24h": {"message": "Utiliser un format de date différent après 24h"}, "settings_timestamp_presets": {"message": "Préréglages"}, "settings_timestamp_preset_absolute": {"message": "Absolu"}, "settings_timestamp_preset_absolute_us": {"message": "Absolu (style américain)"}, "settings_fullname_username": {"message": "Nom et nom d'utilisateur"}, "settings_username_fullname": {"message": "Nom d'utilisateur et nom"}, "settings_username": {"message": "Nom d'utilisateur uniquement"}, "settings_fullname": {"message": "Nom uniquement"}, "settings_name_display_style": {"message": "Style d'affichage du nom"}, "settings_general": {"message": "Général"}, "settings_theme": {"message": "Thème"}, "settings_tweets_display": {"message": "Affich<PERSON> des tweets"}, "settings_tweet_actions": {"message": "Actions des Tweets"}, "settings_actions_visibility_always": {"message": "Toujours"}, "settings_actions_visibility_on_hover": {"message": "Au survol"}, "settings_actions_visibility": {"message": "Visibilité des actions"}, "settings_position_of_actions": {"message": "Position des actions"}, "settings_actions_position_left": {"message": "G<PERSON><PERSON>"}, "settings_actions_position_right": {"message": "<PERSON><PERSON><PERSON>"}, "settings_action_block_author": {"message": "Bloquer l'auteur"}, "settings_action_mute_author": {"message": "Masquer l'auteur"}, "settings_action_copy_media_links": {"message": "Co<PERSON>r les liens de médias"}, "settings_action_download_media": {"message": "Télécharger les médias"}, "settings_additional_actions": {"message": "Actions de tweet à ajouter"}, "settings_downloaded_filename_format": {"message": "Format du nom de fichier téléchargé"}, "settings_filename_format_tokens": {"message": "Token de format de nom de fichier"}, "settings_token_username_without": {"message": "nom d'utilisateur (sans @)"}, "settings_token_tweet_id": {"message": "ID du tweet"}, "settings_token_filename": {"message": "Nom du fichier"}, "settings_token_file_extension": {"message": "Extension du fichier"}, "settings_token_year": {"message": "<PERSON><PERSON>"}, "settings_token_day": {"message": "Jour"}, "settings_token_month": {"message": "<PERSON><PERSON>"}, "settings_token_minutes": {"message": "Minutes"}, "settings_token_seconds": {"message": "Secondes"}, "settings_menu_item_mute_hashtags": {"message": "Masquer les #hashtags"}, "settings_menu_item_mute_source": {"message": "Masquer la source du tweet"}, "settings_menu_item_redraft": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings_additional_tweet_menu_items": {"message": "Éléments supplémentaires du menu de tweet"}, "settings_replace_hearts_by_stars": {"message": "<PERSON><PERSON><PERSON><PERSON> les coeurs par des étoiles"}, "settings_custom_css": {"message": "CSS personnalisé"}, "settings_custom_css_warning": {"message": "Coller du code inconnu dans cet éditeur peut conduire à des problèmes étranges si vous ne savez pas ce que vous faites"}, "settings_avatar_shape": {"message": "Forme des avatars"}, "settings_save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "settings_use_original_aspect_ratio_images": {"message": "Afficher les images individuelles avec leur ratio d'aspect d'origine (uniquement pour les moyennes et grandes tailles)"}, "settings_show_delete_button_in_columns_header": {"message": "Affiche<PERSON> le bouton \"Effacer\" dans l'entête des colonnes"}, "settings_section_settings": {"message": "Réglages"}, "settings_support": {"message": "Assistance"}, "settings_avatar_square": {"message": "Carré"}, "settings_avatar_circle": {"message": "Cercle"}, "settings_display_modern_fullscreen_images": {"message": "Afficher les images en plein écran comme Twitter Web"}, "settings_browser_and_extension_informations": {"message": "Informations sur le navigateur et l'extension"}, "settings_version": {"message": "Version :"}, "settings_user_agent": {"message": "Agent d'utilisateur :"}, "settings_export_settings": {"message": "Exporter les réglages"}, "settings_export_settings_copy": {"message": "Exportez vos paramètres en cliquant sur le bouton ci-dessous"}, "settings_download_settings_button": {"message": "Télécharger les réglages"}, "settings_import_settings": {"message": "Importer les réglages"}, "settings_import_settings_copy": {"message": "Importer vos réglages depuis un fichier JSON"}, "settings_import_json_wrong_keys": {"message": "Votre fichier ne correspond pas au format des réglages de Better TweetDeck"}, "settings_import_success": {"message": "Vos réglages ont été correctement importés, n'oubliez pas de cliquer sur le bouton Sauvegarder !"}, "settings_imported_settings_summary": {"message": "Cliquez ici pour voir les paramètres importés"}, "settings_website": {"message": "Site internet"}, "settings_links": {"message": "<PERSON><PERSON>"}, "settings_footer_label": {"message": "TweetDeck devra être rechargé pour que les modifications prennent effet !"}, "settings_show_account_picker_like": {"message": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de compte lorsque vous cliquez sur le bouton \"J'aime\""}, "settings_show_account_picker_follow": {"message": "<PERSON><PERSON><PERSON><PERSON> le sélecteur de compte lorsque vous cliquez sur le bouton \"Suivre\""}, "settings_accent_color": {"message": "Couleur d'accentuation"}, "settings_misc": {"message": "Divers"}, "settings_meta": {"message": "Meta"}, "settings_import_export": {"message": "Import / export"}, "settings_columns": {"message": "Colonnes"}, "settings_tweet_content": {"message": "Contenu du tweet"}, "settings_pause_column_scrolling_on_hover": {"message": "Mettre en pause le défilement des colonnes au survol"}, "settings_use_a_custom_width_for_columns": {"message": "Utiliser une largeur personnalisée pour les colonnes"}, "settings_width_any_valid_css_value": {"message": "Largeur (n'imporque quelle valeur CSS valide)"}, "settings_share_on_tweetdeck": {"message": "Partager sur TweetDeck"}, "settings_enable_share_item": {"message": "Ajouter un élément \"Partager sur TweetDeck\" dans le menu contextuel du navigateur"}, "settings_shorten_the_shared_text": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> le texte partagé"}, "settings_contextual_menu": {"message": "Menu contextuel"}, "settings_old_gray": {"message": "<PERSON><PERSON>"}, "settings_super_black": {"message": "Noir"}, "settings_tokens_list": {"message": "Liste des jetons"}, "settings_make_emoji_bigger_in_tweets": {"message": "Rendre les émojis plus grands dans les tweets"}, "settings_add_search_columns_first_in_the_list": {"message": "Ajouter d'abord les colonnes de recherche dans la liste"}, "settings_save_tweeted_hashtags": {"message": "Enregistrer les hashtags tweetés"}, "settings_changelog": {"message": "Journal des modifications"}, "settings_bugs_or_suggestions": {"message": "<PERSON> ou suggestions"}, "settings_source_on_github": {"message": "Source sur GitHub"}, "settings_contributors": {"message": "Contributeurs"}, "settings_author": {"message": "<PERSON><PERSON><PERSON>"}, "settings_credits_about": {"message": "Crédits / À propos"}, "settings_always_characters_left": {"message": "Toujours afficher le nombre de caractères restants dans le compositeur de tweet"}, "settings_better_tweetdeck_ask_tabs": {"message": "Better TweetDeck demandera l'accès aux onglets du navigateur pour que cela fonctionne correctement"}, "settings_show_like_rt_indicators_on_top_of_tweets": {"message": "<PERSON><PERSON><PERSON><PERSON> les indicateurs \"J'aime/RT\" au-dessus des tweets"}, "settings_do_the_same_for_single_images_in_quoted_tweets": {"message": "Faire la même chose pour les images seules dans les tweets cités"}, "settings_default_dark_theme": {"message": "<PERSON><PERSON>"}, "settings_custom_dark_theme": {"message": "Thème sombre"}, "settings_tweet_composer": {"message": "Compositeur de tweet"}, "settings_show_the_emoji_picker": {"message": "<PERSON><PERSON><PERSON><PERSON> le sélecteur d'émoji"}, "settings_enable_emoji_autocompletion": {"message": "Activer l'auto-complétion des émojis en utilisant :code:"}, "settings_enable_the_gif_button": {"message": "Activer le bouton GIF"}, "settings_also_show_cards_in_columns_with_small_media_size": {"message": "Afficher également les cartes dans les colonnes avec les médias en petites tailles"}, "settings_looking_for_inspiration": {"message": "En cherche d'inspiration ?"}, "settings_check_the_collection_of_css_snippets": {"message": "Collection de snippets CSS"}, "settings_css_compress_warning": {"message": "Le CSS dans l'éditeur sera compressé afin d'économiser de l'espace vers le quota de stockage\n        de votre navigateur. Ce qui signifie une indentation ou un espace blanc sera supprimé."}, "settings_backup_warning": {"message": "N'oubliez pas de conserver les sauvegardes de votre travail à un autre endroit!"}, "settings_usernames_like_picker_allowlist": {"message": "Liste de noms d'utilisateur séparés par des virgules pour lesquels le sélecteur doit afficher (laisser vide pour toujours afficher)"}, "settings_override_translation_language": {"message": "Utiliser une langue spécifique lors de la traduction de tweets"}, "settings_default_browsers_language": {"message": "<PERSON><PERSON> (langue du navigateur)"}, "settings_logo_variation": {"message": "Variation du logo"}, "settings_default": {"message": "<PERSON><PERSON> <PERSON><PERSON>"}, "settings_action_follow_author": {"message": "Ajouter une action pour suivre l'auteur"}, "settings_follow_actions": {"message": "Action de suivi"}, "settings_show_followers_count": {"message": "Afficher le nombre d'abonnés à côté de l'icône de suivi"}, "settings_show_verified_badges": {"message": "Afficher les badges de vérification"}, "settings_show_translator_badges": {"message": "Afficher les badges traducteurs"}, "settings_show_badges_on_mutuals": {"message": "Montrer les badges sur les mutus (vous les suivez et ils vous suivent)"}, "settings_badges": {"message": "Badges"}, "settings_show_a_clear_all_columns_button_in_the_sidebar": {"message": "Affiche<PERSON> le bouton \"Effacer toutes les colonnes\" dans la barre latérale"}, "settings_require_alt_images": {"message": "Désactiver le bouton tweet jusqu'à ce que toutes les images aient une description"}, "settings_content_warning_hint": {"message": "Nous allons vérifier si les tweets contiennent les avertissements cw/tw/cn couramment utilisés pour afficher ces avertissements !"}, "settings_show_content_warnings": {"message": "Afficher les avertissements de contenu pour les tweets marqués de façon appropriée"}, "settings_images": {"message": "Images"}, "settings_collapse_unread_dms": {"message": "Réduire également les MP non lus"}, "settings_mutual_badge_use_a_heart": {"message": "Utiliser un cœur"}, "settings_mutual_badge_use_double_arrows": {"message": "Utiliser des flèches doubles"}, "settings_show_account_avatars_on_top_of_columns": {"message": "Afficher l'avatar du compte en haut des colonnes"}, "settings_show_conversation_control_button": {"message": "A<PERSON>iche<PERSON> le bouton de contrôle de la conversation"}, "settings_disable_it_in_the_dm_composer_too": {"message": "S'applique également au compositeur de DM"}, "settings_show_profile_labels_in_tweets_and_profile_modals": {"message": "Afficher les \"libellés de profil\" dans les tweets et les modales de profil"}, "settings_hide_the_try_tweetdeck_preview_button": {"message": "Masquer le bouton \"Essayer l'aperçu de TweetDeck\""}, "settings_pronouns_extra": {"message": "Ne support que les pronoms anglais pour le moment. Si vous remarquez des erreurs, veuillez me le faire savoir sur @BetterTDeck"}, "settings_extract_pronouns": {"message": "Extraire les pronoms de la bio ou de la localisation de l'utilisateur et les afficher dans les colonnes"}, "settings_mute_nfts_accounts": {"message": "Masquer les comptes qui utilise l'integration des avatars NFT (avatar en forme d'hexagone)"}, "settings_require_confirmation_for_block_and_mute_actions": {"message": "Exiger une confirmation pour les actions de blocage et de masquage"}, "settings_better_tweetdeck_has_been_updated": {"message": "Better TweetDeck a été mis à jour !"}, "settings_click_this_notification_to_reload": {"message": "Cliquez sur cette notification pour recharger les onglet(s) TweetDeck et récupérer la dernière mise à jour !"}, "settings_show_twitters_warnings_on_media": {"message": "Afficher les avertissements de Twitter sur les médias"}, "settings_twitter_added_feature_in_january_2022": {"message": "Fonctionnalité ajoutée par Twitter en Janvier 2022"}, "settings_show_warnings_for_adult_content": {"message": "Afficher les avertissements pour les médias avec de la nudité"}, "settings_show_warnings_for_graphic_violence": {"message": "Afficher les avertissements pour les médias avec de la violence graphique"}, "settings_show_warnings_for_sensitive_contents": {"message": "Afficher les avertissements pour les médias ayant un contenu sensible"}, "settings_warnings": {"message": "Avertissements"}, "settings_detect_content_warnings_without_the_keyword": {"message": "Détecter les avertissements de contenu sans le(s) mot(s) clé(s) CW/TW"}, "settings_collapse_tweets_who_match_one_of_the_following_keywords": {"message": "<PERSON><PERSON><PERSON><PERSON> les tweets qui correspondent à l'un des mots-clés suivants :"}, "settings_comma_separated_keywords_matched_by_order_in_the_list": {"message": "Mots-clés séparés par des virgules, par ordre dans la liste"}, "settings_will_match_patterns_like_food_lorem_ipsum": {"message": "Va détecter des textes comme \"[food] lorem ipsum\""}, "settings_you_can_use_this_to_hide_spoilers": {"message": "Vous pouvez l'utiliser pour masquer les spoilers"}, "settings_require_a_confirmation_before_deleting_and_editing": {"message": "Exiger une confirmation avant de supprimer et d'éditer"}, "settings_force_update_banners_to_be_dismissed": {"message": "Forcer la fermeture des bannières de mise à jour"}, "settings_force_dismissed_banner": {"message": "Forcer la fermeture de la bannière"}}