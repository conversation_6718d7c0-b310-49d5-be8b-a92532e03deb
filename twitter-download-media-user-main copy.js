// ==UserScript==
// @name            Twitter/X Media Downloader
// @version         1.0.0
// @description     Add download button to Twitter/X posts for images and videos
// <AUTHOR>
// @match           https://pro.x.com/*
// @grant           GM_download
// @grant           GM_xmlhttpRequest
// @connect         twimg.com
// @connect         twitter.com
// @connect         x.com
// ==/UserScript==

(function() {
    'use strict';

    const BUTTON_STYLE = `
        .twitter-media-downloader-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 34px;
            height: 34px;
            margin: 0 4px;
            background-color: transparent;
            cursor: pointer;
            transition: all 0.2s;
            border-radius: 50%;
        }
        .twitter-media-downloader-btn:hover {
            background-color: rgba(29, 155, 240, 0.1);
        }
        .twitter-media-downloader-btn span {
            font-weight: bold;
            font-size: 24px;
            position: relative;
            top: -5px;
        }
        .twitter-media-downloader-btn.download-all {
            color: #1d9bf0;
        }
        .twitter-media-downloader-btn.download-all:hover span {
            color: #1a8cd8;
        }
        .twitter-media-downloader-btn.download-only {
            color: #00ba7c;
        }
        .twitter-media-downloader-btn.download-only:hover span {
            color: #00a86b;
        }
    `;

    // Add styles to page
    const style = document.createElement('style');
    style.textContent = BUTTON_STYLE;
    document.head.appendChild(style);


    // Extract tweet ID from URL or closest article element
    function getTweetId(element) {
        const article = element.closest('article');
        if (!article) return null;

        const link = article.querySelector('a[href*="/status/"]');
        if (!link) return null;

        const match = link.href.match(/\/status\/(\d+)/);
        return match ? match[1] : null;
    }

    // Get highest quality image URL
    function getOriginalImageUrl(url) {
        if (!url || !url.includes('pbs.twimg.com')) return url;

        // Remove any existing size parameters and add ?format=jpg&name=4096x4096
        const baseUrl = url.split('?')[0].split('&')[0];
        const format = baseUrl.includes('.png') ? 'png' : 'jpg';
        return `${baseUrl}?format=${format}&name=4096x4096`;
    }

    // Extract tweet timestamp
    function getTweetTimestamp(article) {
        // Look for the time element in the tweet
        const timeElement = article.querySelector('time');
        if (timeElement && timeElement.dateTime) {
            return new Date(timeElement.dateTime);
        }
        return null;
    }

    // Extract username from tweet
    function getUsername(article) {
        // Try to find username from tweet author
        const userLink = article.querySelector('a[href^="/"][href*="/status/"]');
        if (userLink) {
            const match = userLink.href.match(/\/([^\/]+)\/status\//);
            if (match) return match[1];
        }

        // Alternative: look for username in display
        const usernameElement = article.querySelector('[data-testid="User-Name"] a[href^="/"]');
        if (usernameElement) {
            return usernameElement.href.split('/').pop();
        }

        return 'unknown';
    }

    // Get filename from URL
    function getFilenameFromUrl(url) {
        const match = url.match(/\/([^\/\?]+)(?:\?|$)/);
        if (match) {
            const filename = match[1];
            // Remove file extension if present
            return filename.replace(/\.[^.]+$/, '');
        }
        return 'image';
    }

    // Format date for filename
    function formatDate(timestamp) {
        const date = timestamp || new Date();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return { month, day, year, hours, minutes };
    }

    // Store intercepted video URLs (now stores arrays of URLs)
    const videoUrlCache = new Map();

    // Intercept XHR requests to capture video URLs
    function interceptVideoUrls() {
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (url.includes('/ext_tw_video/') || url.includes('.m3u8') || url.includes('.mp4')) {
                // Extract tweet ID from the page
                const urlParts = window.location.pathname.split('/');
                const statusIndex = urlParts.indexOf('status');
                if (statusIndex !== -1 && urlParts[statusIndex + 1]) {
                    const tweetId = urlParts[statusIndex + 1];
                    if (url.includes('.mp4') && !url.includes('.m3u8')) {
                        // Store as array to support multiple videos
                        const existingUrls = videoUrlCache.get(tweetId) || [];
                        if (!existingUrls.includes(url)) {
                            existingUrls.push(url);
                            videoUrlCache.set(tweetId, existingUrls);
                        }
                    }
                }
            }
            return originalOpen.apply(this, [method, url, ...args]);
        };

        // Also intercept fetch requests
        const originalFetch = window.fetch;
        window.fetch = function(url, ...args) {
            if (typeof url === 'string' && (url.includes('/ext_tw_video/') || url.includes('.mp4'))) {
                const urlParts = window.location.pathname.split('/');
                const statusIndex = urlParts.indexOf('status');
                if (statusIndex !== -1 && urlParts[statusIndex + 1]) {
                    const tweetId = urlParts[statusIndex + 1];
                    if (url.includes('.mp4') && !url.includes('.m3u8')) {
                        // Store as array to support multiple videos
                        const existingUrls = videoUrlCache.get(tweetId) || [];
                        if (!existingUrls.includes(url)) {
                            existingUrls.push(url);
                            videoUrlCache.set(tweetId, existingUrls);
                        }
                    }
                }
            }
            return originalFetch.apply(this, [url, ...args]);
        };
    }

    // Extract video URLs from tweet (returns array of URLs)
    async function getVideoUrls(tweetElement) {
        const tweetId = getTweetId(tweetElement);

        // Check cache first
        if (tweetId && videoUrlCache.has(tweetId)) {
            const cached = videoUrlCache.get(tweetId);
            // Ensure cache contains array
            return Array.isArray(cached) ? cached : [cached];
        }

        const videoUrls = [];
        let foundInReactProps = false;

        // Try to extract from React props - this is the most reliable method
        try {
            const reactKey = Object.keys(tweetElement).find(key => key.startsWith('__reactInternalInstance') || key.startsWith('__reactFiber'));
            if (reactKey) {
                let fiber = tweetElement[reactKey];
                while (fiber) {
                    if (fiber.memoizedProps?.tweet?.extended_entities?.media) {
                        const media = fiber.memoizedProps.tweet.extended_entities.media;
                        // Only process video items
                        const videoItems = media.filter(item => item.type === 'video' || item.type === 'animated_gif');

                        for (const item of videoItems) {
                            if (item.video_info?.variants) {
                                // Find highest quality MP4
                                const mp4Variants = item.video_info.variants
                                    .filter(v => v.content_type === 'video/mp4')
                                    .sort((a, b) => (b.bitrate || 0) - (a.bitrate || 0));
                                if (mp4Variants.length > 0) {
                                    videoUrls.push(mp4Variants[0].url);
                                }
                            }
                        }

                        if (videoUrls.length > 0) {
                            foundInReactProps = true;
                            break;
                        }
                    }
                    fiber = fiber.return;
                }
            }
        } catch (e) {
            console.log('Could not extract from React props:', e);
        }

        // If we found videos in React props, don't use fallback
        if (foundInReactProps && videoUrls.length > 0) {
            console.log(`Found ${videoUrls.length} videos from React props for tweet ${tweetId}`);
            videoUrlCache.set(tweetId, videoUrls);
            return videoUrls;
        }

        // Check intercepted URLs as fallback
        if (tweetId && videoUrlCache.has(tweetId)) {
            const cached = videoUrlCache.get(tweetId);
            const urls = Array.isArray(cached) ? cached : [cached];
            console.log(`Found ${urls.length} videos from intercepted URLs for tweet ${tweetId}`);
            return urls;
        }

        console.log(`No videos found for tweet ${tweetId}`);
        return null;
    }

    // Download file using GM_download
    function downloadFile(url, filename) {
        if (!url) return;

        GM_download({
            url: url,
            name: filename,
            saveAs: false,
            onerror: function(err) {
                console.error('Download failed:', err);
                // Fallback to window.open
                window.open(url, '_blank');
            },
            onload: function() {
                console.log('Download completed:', filename);
            }
        });
    }

    // Like tweet
    function likeTweet(article) {
        const likeButton = article.querySelector('[data-testid="like"]');
        if (likeButton && likeButton.getAttribute('data-testid') === 'like') {
            likeButton.click();
        }
    }

    // Bookmark tweet
    function bookmarkTweet(article) {
        const bookmarkButton = article.querySelector('[data-testid="bookmark"]');
        if (bookmarkButton) {
            bookmarkButton.click();
        }
    }

    // Create download buttons
    function createDownloadButtons() {
        const container = document.createElement('div');
        container.style.display = 'inline-flex';
        container.style.alignItems = 'center';

        // Download + Like + Bookmark button (blue X)
        const downloadAllBtn = document.createElement('div');
        downloadAllBtn.className = 'twitter-media-downloader-btn download-all';
        downloadAllBtn.title = 'Download, Like & Bookmark';
        downloadAllBtn.innerHTML = '<span>X</span>';

        // Download only button (green X)
        const downloadOnlyBtn = document.createElement('div');
        downloadOnlyBtn.className = 'twitter-media-downloader-btn download-only';
        downloadOnlyBtn.title = 'Download Only';
        downloadOnlyBtn.innerHTML = '<span>X</span>';

        container.appendChild(downloadAllBtn);
        container.appendChild(downloadOnlyBtn);

        return { container, downloadAllBtn, downloadOnlyBtn };
    }

    // Add download button to tweet
    function addDownloadButton(article) {
        // Check if button already exists
        if (article.querySelector('.twitter-media-downloader-btn')) return;

        // Find action bar (where like, retweet buttons are)
        const actionBar = article.querySelector('[role="group"]');
        if (!actionBar) return;

        // Check if tweet has media (exclude profile pictures)
        const hasImage = article.querySelector('img[src*="pbs.twimg.com"]:not([src*="_normal"]):not([src*="_bigger"]):not([src*="_mini"])');
        const hasVideo = article.querySelector('video, [data-testid="videoPlayer"]');

        if (!hasImage && !hasVideo) return;

        const { container, downloadAllBtn, downloadOnlyBtn } = createDownloadButtons();

        // Download function
        async function performDownload() {
            const username = getUsername(article);
            const tweetTimestamp = getTweetTimestamp(article);
            const { month, day, year, hours, minutes } = formatDate(tweetTimestamp);

            // Download videos if present
            if (hasVideo) {
                const videoUrls = await getVideoUrls(article);
                if (videoUrls && videoUrls.length > 0) {
                    // Download all videos
                    videoUrls.forEach((videoUrl, index) => {
                        const videoFilename = getFilenameFromUrl(videoUrl);
                        const suffix = videoUrls.length > 1 ? `_${index + 1}` : '';
                        const filename = `${username}-${videoFilename}${suffix}-${month}-${day}-${year}-${hours}-${minutes}.mp4`;
                        downloadFile(videoUrl, filename);
                    });
                } else {
                    // Try clicking the video first to trigger loading
                    const videoElement = article.querySelector('video, [data-testid="videoPlayer"]');
                    if (videoElement) {
                        alert('Please play the video first, then try downloading again. This helps load the video URL.');
                    } else {
                        alert('Could not find video URL. The video might be protected or not loaded yet.');
                    }
                }
            }

            // Also download images if present (not mutually exclusive with videos)
            if (hasImage) {
                // Only select tweet media images, exclude profile pictures
                const images = article.querySelectorAll('img[src*="pbs.twimg.com"]:not([src*="_normal"]):not([src*="_bigger"]):not([src*="_mini"]):not([src*="_200x200"]):not([src*="_400x400"])');

                // Filter out images that are likely profile pictures or other non-media
                const mediaImages = Array.from(images).filter(img => {
                    // Check if image is inside a media container
                    const isInMediaContainer = img.closest('[data-testid="tweetPhoto"]') ||
                                               img.closest('[role="link"][href*="/photo/"]') ||
                                               img.closest('a[href*="/photo/"]');
                    return isInMediaContainer;
                });

                if (mediaImages.length === 1) {
                    // Single image
                    const imageUrl = getOriginalImageUrl(mediaImages[0].src);
                    const imageFilename = getFilenameFromUrl(imageUrl);
                    const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                    const filename = `${username}-${imageFilename}-${month}-${day}-${year}-${hours}-${minutes}.${ext}`;
                    downloadFile(imageUrl, filename);
                } else if (mediaImages.length > 1) {
                    // Multiple images
                    mediaImages.forEach((img, index) => {
                        const imageUrl = getOriginalImageUrl(img.src);
                        const imageFilename = getFilenameFromUrl(imageUrl);
                        const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                        const filename = `${username}-${imageFilename}_${index + 1}-${month}-${day}-${year}-${hours}-${minutes}.${ext}`;
                        downloadFile(imageUrl, filename);
                    });
                }
            }
        }

        // Download + Like + Bookmark button
        downloadAllBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Perform all actions
            await performDownload();
            setTimeout(() => likeTweet(article), 100);
            setTimeout(() => bookmarkTweet(article), 200);
        });

        // Download only button
        downloadOnlyBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            await performDownload();
        });

        // Insert buttons into action bar
        const lastButton = actionBar.lastElementChild;
        if (lastButton) {
            actionBar.insertBefore(container, lastButton);
        } else {
            actionBar.appendChild(container);
        }
    }

    // Observer to detect new tweets
    const observer = new MutationObserver((mutations) => {
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });
    });

    // Start observing
    function init() {
        // Set up video URL interception
        interceptVideoUrls();

        // Process existing tweets
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });

        // Observe for new tweets
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Wait for page to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();