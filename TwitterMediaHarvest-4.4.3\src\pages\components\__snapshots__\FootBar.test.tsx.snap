// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`unit test for FootBar component can render properly 1`] = `
<div>
  <div
    class="css-keh7tk"
  >
    <div
      class="chakra-stack css-1ppptz2"
    >
      <p
        class="chakra-text css-0"
      >
        Translated&lt;options:footBar:text_Do you like Media Harvest?&gt;
      </p>
      <a
        class="chakra-link css-163zzif"
        href="https://chrome.google.com/webstore/detail/hpcgabhdlnapolkkjpejieegfpehfdok"
        rel="noopener"
        target="_blank"
      >
        <button
          class="chakra-button css-117sk0y"
          data-testid="rate-button"
          type="button"
        >
          Translated&lt;options:footBar:button_Rate it&gt;
          <span
            class="chakra-button__icon css-1hzyiq5"
          >
            <svg
              aria-hidden="true"
              class="chakra-icon css-onkibi"
              focusable="false"
              viewBox="0 0 24 24"
            >
              <path
                d="M23.555,8.729a1.505,1.505,0,0,0-1.406-.98H16.062a.5.5,0,0,1-.472-.334L13.405,1.222a1.5,1.5,0,0,0-2.81,0l-.005.016L8.41,7.415a.5.5,0,0,1-.471.334H1.85A1.5,1.5,0,0,0,.887,10.4l5.184,4.3a.5.5,0,0,1,.155.543L4.048,21.774a1.5,1.5,0,0,0,2.31,1.684l5.346-3.92a.5.5,0,0,1,.591,0l5.344,3.919a1.5,1.5,0,0,0,2.312-1.683l-2.178-6.535a.5.5,0,0,1,.155-.543l5.194-4.306A1.5,1.5,0,0,0,23.555,8.729Z"
                fill="currentColor"
              />
            </svg>
          </span>
        </button>
      </a>
      <p
        class="chakra-text css-0"
      >
        Translated&lt;options:footBar:text_or&gt;
      </p>
      <a
        class="chakra-link css-163zzif"
        href="https://ko-fi.com/eltonhy"
        rel="noopener"
        target="_blank"
      >
        <button
          class="chakra-button css-117sk0y"
          data-testid="coffee-button"
          type="button"
        >
          Translated&lt;options:footBar:button_Buy me a coffee&gt;
          <span
            class="chakra-button__icon css-1hzyiq5"
          >
            <svg
              aria-hidden="true"
              fill="currentColor"
              focusable="false"
              height="1em"
              stroke="currentColor"
              stroke-width="0"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7 22h10a1 1 0 0 0 .99-.858L19.867 8H21V6h-1.382l-1.724-3.447A.998.998 0 0 0 17 2H7c-.379 0-.725.214-.895.553L4.382 6H3v2h1.133L6.01 21.142A1 1 0 0 0 7 22zm10.418-11H6.582l-.429-3h11.693l-.428 3zm-9.551 9-.429-3h9.123l-.429 3H7.867zM7.618 4h8.764l1 2H6.618l1-2z"
              />
            </svg>
          </span>
        </button>
      </a>
    </div>
  </div>
</div>
`;
