// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`test 1`] = `
""use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function i18n(msg, context, placeholders) {
    return msg + context || '' + JSON.stringify(placeholders);
}
function nope(msg) {
    return msg;
}
function getText(msg) {
    return msg;
}
i18n("6a30f630de");
i18n("6a30f630de", "9f86d08");
i18n("6a30f630de", "9f86d08", { foo: 'bar' });
nope('kappa');
getText("ea38562ecb");
"
`;
