// ==UserScript==
// @name            Twitter/X Media Downloader
// @namespace       https://github.com/yourusername/
// @version         1.0.0
// @description     Add download button to Twitter/X posts for images and videos
// <AUTHOR>
// @match           https://twitter.com/*
// @match           https://x.com/*
// @grant           GM_download
// @grant           GM_xmlhttpRequest
// @connect         twimg.com
// @connect         twitter.com
// @connect         x.com
// @license         MIT
// ==/UserScript==

/*
 * Twitter/X Media Downloader
 * 
 * This script adds a download button to Twitter/X posts that contain images or videos.
 * 
 * Features:
 * - Downloads images in highest quality (4096x4096)
 * - Downloads videos with smart URL detection
 * - Excludes profile pictures from downloads
 * - Custom filename format with username and timestamp
 * 
 * Filename Format:
 * Default: {username}-{originalFilename}-{MM}-{DD}-{YYYY}-{HH}-{mm}.{extension}
 * Example: elonmusk-FyH3K8VaAAEKzHG-12-25-2024-14-30.jpg
 * 
 * To customize the filename format, modify the filename variable in lines 264, 293, 301:
 * - ${username} - Twitter username
 * - ${videoFilename} or ${imageFilename} - Original filename from Twitter
 * - ${month}, ${day}, ${year} - Date components
 * - ${hours}, ${minutes} - Time components
 * - ${index + 1} - Image number for multiple images
 * 
 * Credits:
 * - Inspired by twOpenOriginalImage by furyu
 * - Video URL detection techniques adapted from various Twitter userscripts
 * - Download handling based on TamperMonkey/ViolentMonkey best practices
 */

(function() {
    'use strict';

    const BUTTON_STYLE = `
        .twitter-media-downloader-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 4px 12px;
            margin: 0 4px;
            background-color: rgba(15, 20, 25, 0.75);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 9999px;
            font-size: 13px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .twitter-media-downloader-btn:hover {
            background-color: rgba(39, 44, 48, 0.75);
        }
        .twitter-media-downloader-btn svg {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
    `;

    // Add styles to page
    const style = document.createElement('style');
    style.textContent = BUTTON_STYLE;
    document.head.appendChild(style);

    // Download icon SVG
    const DOWNLOAD_ICON = `<svg viewBox="0 0 24 24" fill="currentColor"><g><path d="M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"></path></g></svg>`;

    // Extract tweet ID from URL or closest article element
    function getTweetId(element) {
        const article = element.closest('article');
        if (!article) return null;
        
        const link = article.querySelector('a[href*="/status/"]');
        if (!link) return null;
        
        const match = link.href.match(/\/status\/(\d+)/);
        return match ? match[1] : null;
    }

    // Get highest quality image URL
    function getOriginalImageUrl(url) {
        if (!url || !url.includes('pbs.twimg.com')) return url;
        
        // Remove any existing size parameters and add ?format=jpg&name=4096x4096
        const baseUrl = url.split('?')[0].split('&')[0];
        const format = baseUrl.includes('.png') ? 'png' : 'jpg';
        return `${baseUrl}?format=${format}&name=4096x4096`;
    }
    
    // Extract username from tweet
    function getUsername(article) {
        // Try to find username from tweet author
        const userLink = article.querySelector('a[href^="/"][href*="/status/"]');
        if (userLink) {
            const match = userLink.href.match(/\/([^\/]+)\/status\//);
            if (match) return match[1];
        }
        
        // Alternative: look for username in display
        const usernameElement = article.querySelector('[data-testid="User-Name"] a[href^="/"]');
        if (usernameElement) {
            return usernameElement.href.split('/').pop();
        }
        
        return 'unknown';
    }
    
    // Get filename from URL
    function getFilenameFromUrl(url) {
        const match = url.match(/\/([^\/\?]+)(?:\?|$)/);
        if (match) {
            const filename = match[1];
            // Remove file extension if present
            return filename.replace(/\.[^.]+$/, '');
        }
        return 'image';
    }
    
    // Format date for filename
    function formatDate() {
        const now = new Date();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const year = now.getFullYear();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        return { month, day, year, hours, minutes };
    }

    // Store intercepted video URLs
    const videoUrlCache = new Map();
    
    // Intercept XHR requests to capture video URLs
    function interceptVideoUrls() {
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (url.includes('/ext_tw_video/') || url.includes('.m3u8') || url.includes('.mp4')) {
                // Extract tweet ID from the page
                const urlParts = window.location.pathname.split('/');
                const statusIndex = urlParts.indexOf('status');
                if (statusIndex !== -1 && urlParts[statusIndex + 1]) {
                    const tweetId = urlParts[statusIndex + 1];
                    if (url.includes('.mp4') && !url.includes('.m3u8')) {
                        videoUrlCache.set(tweetId, url);
                    }
                }
            }
            return originalOpen.apply(this, [method, url, ...args]);
        };
        
        // Also intercept fetch requests
        const originalFetch = window.fetch;
        window.fetch = function(url, ...args) {
            if (typeof url === 'string' && (url.includes('/ext_tw_video/') || url.includes('.mp4'))) {
                const urlParts = window.location.pathname.split('/');
                const statusIndex = urlParts.indexOf('status');
                if (statusIndex !== -1 && urlParts[statusIndex + 1]) {
                    const tweetId = urlParts[statusIndex + 1];
                    if (url.includes('.mp4') && !url.includes('.m3u8')) {
                        videoUrlCache.set(tweetId, url);
                    }
                }
            }
            return originalFetch.apply(this, [url, ...args]);
        };
    }
    
    // Extract video URL from tweet
    async function getVideoUrl(tweetElement) {
        const tweetId = getTweetId(tweetElement);
        
        // Check cache first
        if (tweetId && videoUrlCache.has(tweetId)) {
            return videoUrlCache.get(tweetId);
        }
        
        // Try to extract from React props
        try {
            const reactKey = Object.keys(tweetElement).find(key => key.startsWith('__reactInternalInstance') || key.startsWith('__reactFiber'));
            if (reactKey) {
                let fiber = tweetElement[reactKey];
                while (fiber) {
                    if (fiber.memoizedProps?.tweet?.extended_entities?.media) {
                        const media = fiber.memoizedProps.tweet.extended_entities.media;
                        for (const item of media) {
                            if (item.video_info?.variants) {
                                // Find highest quality MP4
                                const mp4Variants = item.video_info.variants
                                    .filter(v => v.content_type === 'video/mp4')
                                    .sort((a, b) => (b.bitrate || 0) - (a.bitrate || 0));
                                if (mp4Variants.length > 0) {
                                    return mp4Variants[0].url;
                                }
                            }
                        }
                    }
                    fiber = fiber.return;
                }
            }
        } catch (e) {
            // Silently fail - React props might not be available
        }
        
        // Fallback: Try to find in page's script tags
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            if (script.textContent && script.textContent.includes('ext_tw_video')) {
                const matches = script.textContent.match(/https:\/\/[^"'\s]*\.mp4[^"'\s]*/g);
                if (matches && matches.length > 0) {
                    // Return highest quality
                    return matches.find(url => url.includes('/pu/vid/')) || matches[0];
                }
            }
        }
        
        return null;
    }

    // Download file using GM_download
    function downloadFile(url, filename) {
        if (!url) return;

        GM_download({
            url: url,
            name: filename,
            saveAs: false,
            onerror: function(err) {
                console.error('Download failed:', err);
                // Fallback to window.open
                window.open(url, '_blank');
            },
            onload: function() {
                console.log('Download completed:', filename);
            }
        });
    }

    // Create download button
    function createDownloadButton() {
        const button = document.createElement('div');
        button.className = 'twitter-media-downloader-btn';
        button.innerHTML = DOWNLOAD_ICON + 'Download';
        return button;
    }

    // Add download button to tweet
    function addDownloadButton(article) {
        // Check if button already exists
        if (article.querySelector('.twitter-media-downloader-btn')) return;

        // Find action bar (where like, retweet buttons are)
        const actionBar = article.querySelector('[role="group"]');
        if (!actionBar) return;

        // Check if tweet has media (exclude profile pictures)
        const hasImage = article.querySelector('img[src*="pbs.twimg.com"]:not([src*="_normal"]):not([src*="_bigger"]):not([src*="_mini"])');
        const hasVideo = article.querySelector('video, [data-testid="videoPlayer"]');
        
        if (!hasImage && !hasVideo) return;

        const button = createDownloadButton();
        
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            const username = getUsername(article);
            const { month, day, year, hours, minutes } = formatDate();

            if (hasVideo) {
                const videoUrl = await getVideoUrl(article);
                if (videoUrl) {
                    const videoFilename = getFilenameFromUrl(videoUrl);
                    const filename = `${username}-${videoFilename}-${month}-${day}-${year}-${hours}-${minutes}.mp4`;
                    downloadFile(videoUrl, filename);
                } else {
                    // Try clicking the video first to trigger loading
                    const videoElement = article.querySelector('video, [data-testid="videoPlayer"]');
                    if (videoElement) {
                        alert('Please play the video first, then try downloading again. This helps load the video URL.');
                    } else {
                        alert('Could not find video URL. The video might be protected or not loaded yet.');
                    }
                }
            } else if (hasImage) {
                // Only select tweet media images, exclude profile pictures
                const images = article.querySelectorAll('img[src*="pbs.twimg.com"]:not([src*="_normal"]):not([src*="_bigger"]):not([src*="_mini"]):not([src*="_200x200"]):not([src*="_400x400"])');
                
                // Filter out images that are likely profile pictures or other non-media
                const mediaImages = Array.from(images).filter(img => {
                    // Check if image is inside a media container
                    const isInMediaContainer = img.closest('[data-testid="tweetPhoto"]') || 
                                               img.closest('[role="link"][href*="/photo/"]') ||
                                               img.closest('a[href*="/photo/"]');
                    return isInMediaContainer;
                });
                
                if (mediaImages.length === 1) {
                    // Single image
                    const imageUrl = getOriginalImageUrl(mediaImages[0].src);
                    const imageFilename = getFilenameFromUrl(imageUrl);
                    const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                    const filename = `${username}-${imageFilename}-${month}-${day}-${year}-${hours}-${minutes}.${ext}`;
                    downloadFile(imageUrl, filename);
                } else if (mediaImages.length > 1) {
                    // Multiple images
                    mediaImages.forEach((img, index) => {
                        const imageUrl = getOriginalImageUrl(img.src);
                        const imageFilename = getFilenameFromUrl(imageUrl);
                        const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                        const filename = `${username}-${imageFilename}_${index + 1}-${month}-${day}-${year}-${hours}-${minutes}.${ext}`;
                        downloadFile(imageUrl, filename);
                    });
                }
            }
        });

        // Insert button into action bar
        const lastButton = actionBar.lastElementChild;
        if (lastButton) {
            actionBar.insertBefore(button, lastButton);
        } else {
            actionBar.appendChild(button);
        }
    }

    // Observer to detect new tweets
    const observer = new MutationObserver((mutations) => {
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });
    });

    // Start observing
    function init() {
        // Set up video URL interception
        interceptVideoUrls();
        
        // Process existing tweets
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });

        // Observe for new tweets
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Wait for page to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();